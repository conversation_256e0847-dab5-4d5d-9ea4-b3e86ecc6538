"""
Script de lancement sécurisé pour GesComPro_LibTam
Évite les erreurs matplotlib et lance l'application en mode dégradé si nécessaire
"""

import sys
import os
import warnings
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

# Supprimer les avertissements
warnings.filterwarnings("ignore")

def check_dependencies():
    """Vérifier les dépendances essentielles"""
    print("🔍 Vérification des dépendances...")
    missing_deps = []
    
    try:
        import kivy
        print(f"✅ Kivy {kivy.__version__}")
    except ImportError:
        missing_deps.append("kivy")
    
    try:
        import kivymd
        print(f"✅ KivyMD")
    except ImportError:
        missing_deps.append("kivymd")
    
    # Matplotlib optionnel
    try:
        import matplotlib
        print(f"✅ Matplotlib {matplotlib.__version__}")
        matplotlib_available = True
    except ImportError:
        print("⚠️ Matplotlib non disponible - Mode graphiques désactivé")
        matplotlib_available = False
    
    try:
        import reportlab
        print(f"✅ ReportLab")
    except ImportError:
        missing_deps.append("reportlab")
    
    if missing_deps:
        print(f"\n❌ Dépendances manquantes: {', '.join(missing_deps)}")
        print("Installez-les avec: pip install " + " ".join(missing_deps))
        return False, matplotlib_available
    
    return True, matplotlib_available


def configure_matplotlib_safe():
    """Configurer matplotlib de manière sécurisée"""
    try:
        import matplotlib
        # Utiliser un backend non-interactif
        matplotlib.use('Agg')
        
        # Configuration pour éviter les erreurs
        import matplotlib.pyplot as plt
        plt.ioff()  # Mode non-interactif
        
        print("✅ Matplotlib configuré en mode sécurisé")
        return True
    except Exception as e:
        print(f"⚠️ Impossible de configurer matplotlib: {e}")
        return False


def patch_reports_screen():
    """Patcher l'écran des rapports pour éviter les erreurs matplotlib"""
    try:
        # Définir une variable globale pour désactiver matplotlib
        os.environ['GESCOM_DISABLE_MATPLOTLIB'] = '1'
        print("✅ Mode graphiques désactivé pour éviter les erreurs")
        return True
    except Exception as e:
        print(f"⚠️ Erreur lors du patch: {e}")
        return False


def main():
    """Fonction principale sécurisée"""
    print("🚀 LANCEMENT SÉCURISÉ DE GesComPro_LibTam")
    print("=" * 50)
    
    # Vérifier les dépendances
    deps_ok, matplotlib_available = check_dependencies()
    if not deps_ok:
        input("\nAppuyez sur Entrée pour quitter...")
        return
    
    # Configurer matplotlib si disponible
    if matplotlib_available:
        matplotlib_ok = configure_matplotlib_safe()
        if not matplotlib_ok:
            patch_reports_screen()
    else:
        patch_reports_screen()
    
    # Optimiser la base de données
    print("\n⚡ Optimisation de la base de données...")
    try:
        from database.db_manager import DatabaseManager
        
        db = DatabaseManager()
        if db.connect():
            db.initialize_database()
            print("   ✅ Base de données optimisée")
        else:
            print("   ⚠️ Avertissement: Problème de base de données")
    except Exception as e:
        print(f"   ⚠️ Erreur DB: {e}")
    
    # Démarrer l'application
    print("\n📱 Démarrage de l'application...")
    
    try:
        # Configuration Kivy pour éviter les erreurs
        os.environ['KIVY_LOG_MODE'] = 'PYTHON'
        
        # Supprimer les avertissements KivyMD
        import logging
        logging.getLogger('kivy').setLevel(logging.ERROR)
        
        # Importer et lancer l'application
        from main import GesComApp
        
        print("   ✅ Application initialisée")
        print("   🎯 Interface utilisateur prête")
        
        if not matplotlib_available:
            print("   📊 Mode graphiques désactivé - Données disponibles en mode texte")
        
        print("\n🎉 Application prête ! Lancement en cours...")
        
        # Lancer l'application
        app = GesComApp()
        app.run()
        
    except Exception as e:
        print(f"\n❌ Erreur lors du lancement: {e}")
        
        # Diagnostic de l'erreur
        error_str = str(e).lower()
        
        if 'figurecanvaskivyagg' in error_str or 'resize_event' in error_str:
            print("\n🔧 SOLUTION POUR L'ERREUR MATPLOTLIB:")
            print("   1. Exécutez: python fix_matplotlib.py")
            print("   2. Ou utilisez: python launch_safe.py")
            print("   3. L'application fonctionnera sans graphiques matplotlib")
        
        elif 'kivymd' in error_str:
            print("\n🔧 SOLUTION POUR L'ERREUR KIVYMD:")
            print("   1. Vérifiez la version KivyMD: pip show kivymd")
            print("   2. Réinstallez si nécessaire: pip install kivymd")
        
        else:
            print("\n🔧 DIAGNOSTIC GÉNÉRAL:")
            print("   1. Vérifiez les dépendances: pip install -r requirements.txt")
            print("   2. Consultez les logs pour plus de détails")
        
        import traceback
        traceback.print_exc()
        input("\nAppuyez sur Entrée pour quitter...")


if __name__ == "__main__":
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    main()
