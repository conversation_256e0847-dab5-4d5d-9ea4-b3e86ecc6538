#!/usr/bin/env python3
"""
Test avec un dialog très simple pour identifier le problème
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

# Configuration pour Windows
if sys.platform == 'win32':
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton, MDFlatButton
from kivymd.uix.label import MDLabel
from kivymd.uix.dialog import MDDialog
from kivymd.uix.textfield import MDTextField


class SimpleTestDialog(MDDialog):
    """Dialog de test très simple"""
    
    def __init__(self, **kwargs):
        # Créer le contenu d'abord
        content = self.create_simple_content()
        
        # Créer les boutons
        cancel_btn = MDFlatButton(
            text="❌ Annuler",
            on_release=self.dismiss
        )
        
        save_btn = MDRaisedButton(
            text="💾 Enregistrer",
            on_release=self.dismiss
        )
        
        super().__init__(
            title="🧪 Test Dialog Simple",
            type="custom",
            content_cls=content,
            size_hint=(0.8, None),
            height="400dp",
            buttons=[cancel_btn, save_btn],
            **kwargs
        )
    
    def create_simple_content(self):
        """Créer un contenu très simple"""
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="20dp",
            size_hint_y=None,
            height="300dp"  # Hauteur fixe pour test
        )
        
        # Titre
        title = MDLabel(
            text="🧪 Contenu de Test",
            font_style="H6",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="40dp"
        )
        
        # Champ 1
        field1 = MDTextField(
            hint_text="Champ de test 1",
            mode="rectangle",
            size_hint_y=None,
            height="56dp"
        )
        
        # Champ 2
        field2 = MDTextField(
            hint_text="Champ de test 2",
            mode="rectangle",
            size_hint_y=None,
            height="56dp"
        )
        
        # Label info
        info = MDLabel(
            text="Si vous voyez ce texte et les champs,\nle problème vient du formulaire de vente.",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        layout.add_widget(title)
        layout.add_widget(field1)
        layout.add_widget(field2)
        layout.add_widget(info)
        
        return layout


class TestSimpleDialogApp(MDApp):
    """Test avec dialog simple"""
    
    def build(self):
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        title = MDLabel(
            text="🧪 Test Dialog Simple\nvs Formulaire de Vente",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Test dialog simple
        simple_btn = MDRaisedButton(
            text="🧪 Dialog Simple (doit marcher)",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_simple_dialog
        )
        
        # Test formulaire de vente
        sales_btn = MDRaisedButton(
            text="🛒 Formulaire de Vente (problème)",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_sales_dialog
        )
        
        self.result_label = MDLabel(
            text="Comparaison entre dialog simple et formulaire de vente",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(simple_btn)
        layout.add_widget(sales_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_simple_dialog(self, *args):
        """Tester le dialog simple"""
        try:
            dialog = SimpleTestDialog()
            dialog.open()
            
            self.result_label.text = "✅ Dialog simple ouvert !\n\n" \
                                   "Si vous voyez le contenu,\n" \
                                   "le problème vient du formulaire de vente."
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur dialog simple :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def test_sales_dialog(self, *args):
        """Tester le formulaire de vente"""
        try:
            from forms.sales_form import SalesFormDialog
            
            dialog = SalesFormDialog()
            dialog.open()
            
            self.result_label.text = "🛒 Formulaire de vente ouvert !\n\n" \
                                   "Comparez avec le dialog simple."
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur formulaire vente :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()


def main():
    print("🧪 Test Comparaison - Dialog Simple vs Formulaire de Vente")
    print("OBJECTIF: Identifier la différence entre un dialog qui marche et celui qui ne marche pas")
    
    try:
        app = TestSimpleDialogApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()