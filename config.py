"""
Configuration de l'application GesComPro
"""

import os
from pathlib import Path

# Chemins de l'application
APP_DIR = Path(__file__).parent
DATA_DIR = APP_DIR / "data"
BACKUP_DIR = APP_DIR / "backups"
EXPORTS_DIR = APP_DIR / "exports"

# Créer les dossiers s'ils n'existent pas
DATA_DIR.mkdir(exist_ok=True)
BACKUP_DIR.mkdir(exist_ok=True)
EXPORTS_DIR.mkdir(exist_ok=True)

# Configuration de la base de données
DATABASE_PATH = DATA_DIR / "gescom.db"

# Configuration de l'application
APP_NAME = "GesComPro"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Application de gestion commerciale"

# Configuration par défaut
DEFAULT_SETTINGS = {
    'entreprise_nom': 'Mon Entreprise',
    'entreprise_adresse': '123 Rue de la Gestion',
    'entreprise_telephone': '***********.89',
    'entreprise_email': '<EMAIL>',
    'tva_defaut': '20.0',
    'devise': 'MAD',
    'theme_style': 'Light',
    'format_facture': 'FAC-{YYYY}-{MM}-{NNNN}',
    'notifications_enabled': 'True',
    'stock_alert_enabled': 'True'
}

# Configuration des couleurs du thème
THEME_COLORS = {
    'primary': 'Blue',
    'accent': 'Amber',
    'success': 'Green',
    'warning': 'Orange',
    'error': 'Red'
}

# Configuration des exports
EXPORT_FORMATS = ['CSV', 'Excel', 'PDF']

# Configuration des sauvegardes
BACKUP_RETENTION_DAYS = 30  # Conserver les sauvegardes pendant 30 jours

# Configuration des notifications
NOTIFICATION_TYPES = {
    'stock_bas': 'Produits en stock bas',
    'vente_importante': 'Vente importante',
    'nouveau_client': 'Nouveau client',
    'objectif_atteint': 'Objectif de vente atteint'
}

# Configuration des rapports
REPORT_PERIODS = {
    'today': "Aujourd'hui",
    'week': 'Cette semaine',
    'month': 'Ce mois',
    'quarter': 'Ce trimestre',
    'year': 'Cette année',
    'custom': 'Période personnalisée'
}

# Configuration des formats de date
DATE_FORMAT = '%d/%m/%Y'
DATETIME_FORMAT = '%d/%m/%Y %H:%M'
DATABASE_DATE_FORMAT = '%Y-%m-%d'
DATABASE_DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S'

# Configuration des limites
MAX_ITEMS_PER_PAGE = 50
MAX_SEARCH_RESULTS = 100
MAX_EXPORT_ROWS = 10000

# Configuration de sécurité
ALLOWED_FILE_EXTENSIONS = ['.csv', '.xlsx', '.pdf', '.json', '.db']
MAX_FILE_SIZE_MB = 50

# Messages d'erreur
ERROR_MESSAGES = {
    'db_connection': 'Impossible de se connecter à la base de données',
    'db_query': 'Erreur lors de l\'exécution de la requête',
    'file_not_found': 'Fichier non trouvé',
    'permission_denied': 'Permission refusée',
    'invalid_data': 'Données invalides',
    'network_error': 'Erreur de réseau',
    'unknown_error': 'Erreur inconnue'
}

# Messages de succès
SUCCESS_MESSAGES = {
    'data_saved': 'Données sauvegardées avec succès',
    'data_deleted': 'Données supprimées avec succès',
    'export_completed': 'Export terminé avec succès',
    'backup_created': 'Sauvegarde créée avec succès',
    'settings_updated': 'Paramètres mis à jour avec succès'
}

# Configuration des logs
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_FILE = DATA_DIR / 'gescom.log'

# Configuration des codes-barres
BARCODE_CONFIG = {
    'default_type': 'EAN13',
    'auto_generate': True,
    'validate_on_input': True,
    'prefix_custom': 'PRD',
    'length_custom': 12
}

# Configuration des factures
INVOICE_CONFIG = {
    'auto_number': True,
    'number_format': 'FAC-{YYYY}-{MM}-{NNNN}',
    'default_payment_method': 'Espèces',
    'include_logo': False,
    'footer_text': 'Merci de votre confiance'
}

# Configuration des alertes stock
STOCK_CONFIG = {
    'alert_threshold': 5,
    'critical_threshold': 1,
    'auto_reorder': False,
    'reorder_quantity': 10
}