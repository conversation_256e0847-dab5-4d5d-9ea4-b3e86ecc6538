"""
Optimisation automatique de la base de données GesComPro
"""

import os
import sys
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

from database.db_manager import DatabaseManager


def optimiser_base_donnees():
    """Optimiser automatiquement la base de données"""
    print("⚡ OPTIMISATION AUTOMATIQUE DE LA BASE DE DONNÉES")
    print("=" * 55)
    
    db = DatabaseManager()
    
    if not db.connect():
        print("❌ Impossible de se connecter à la base de données")
        return False
    
    try:
        cursor = db.connection.cursor()
        
        # 1. Vérifier la configuration actuelle
        print("🔍 1. VÉRIFICATION DE LA CONFIGURATION ACTUELLE")
        print("-" * 45)
        
        config_checks = [
            ("PRAGMA journal_mode", "Mode journal"),
            ("PRAGMA synchronous", "Mode synchronisation"),
            ("PRAGMA cache_size", "Taille du cache"),
            ("PRAGMA temp_store", "Stockage temporaire"),
            ("PRAGMA foreign_keys", "Clés étrangères")
        ]
        
        for pragma, description in config_checks:
            try:
                result = cursor.execute(pragma).fetchone()
                if result:
                    print(f"   📊 {description}: {result[0]}")
            except Exception as e:
                print(f"   ⚠️ Impossible de vérifier {description}: {e}")
        
        # 2. Créer les index manquants
        print("\n🏗️ 2. CRÉATION DES INDEX DE PERFORMANCE")
        print("-" * 45)
        
        indexes = [
            # Index pour les produits
            ("CREATE INDEX IF NOT EXISTS idx_produits_nom ON produits(nom)", "Index nom produits"),
            ("CREATE INDEX IF NOT EXISTS idx_produits_reference ON produits(reference)", "Index référence produits"),
            ("CREATE INDEX IF NOT EXISTS idx_produits_categorie ON produits(categorie_id)", "Index catégorie produits"),
            ("CREATE INDEX IF NOT EXISTS idx_produits_actif ON produits(actif)", "Index statut produits"),
            ("CREATE INDEX IF NOT EXISTS idx_produits_stock ON produits(stock_actuel)", "Index stock produits"),
            
            # Index pour les ventes
            ("CREATE INDEX IF NOT EXISTS idx_ventes_date ON ventes(date_vente)", "Index date ventes"),
            ("CREATE INDEX IF NOT EXISTS idx_ventes_client ON ventes(client_id)", "Index client ventes"),
            ("CREATE INDEX IF NOT EXISTS idx_ventes_statut ON ventes(statut)", "Index statut ventes"),
            ("CREATE INDEX IF NOT EXISTS idx_ventes_montant ON ventes(montant_ttc)", "Index montant ventes"),
            
            # Index pour les détails de vente
            ("CREATE INDEX IF NOT EXISTS idx_details_vente ON details_vente(vente_id)", "Index vente détails"),
            ("CREATE INDEX IF NOT EXISTS idx_details_produit ON details_vente(produit_id)", "Index produit détails"),
            
            # Index pour les clients
            ("CREATE INDEX IF NOT EXISTS idx_clients_nom ON clients(nom)", "Index nom clients"),
            ("CREATE INDEX IF NOT EXISTS idx_clients_email ON clients(email)", "Index email clients"),
            ("CREATE INDEX IF NOT EXISTS idx_clients_actif ON clients(actif)", "Index statut clients"),
            
            # Index pour les catégories
            ("CREATE INDEX IF NOT EXISTS idx_categories_nom ON categories(nom)", "Index nom catégories"),
            
            # Index composites pour les requêtes fréquentes
            ("CREATE INDEX IF NOT EXISTS idx_produits_actif_categorie ON produits(actif, categorie_id)", "Index composite produits"),
            ("CREATE INDEX IF NOT EXISTS idx_ventes_date_client ON ventes(date_vente, client_id)", "Index composite ventes")
        ]
        
        created_count = 0
        for sql, description in indexes:
            try:
                cursor.execute(sql)
                print(f"   ✅ {description}")
                created_count += 1
            except Exception as e:
                print(f"   ⚠️ {description}: {e}")
        
        print(f"\n📊 {created_count}/{len(indexes)} index créés/vérifiés")
        
        # 3. Analyser les statistiques
        print("\n📈 3. ANALYSE DES STATISTIQUES")
        print("-" * 45)
        
        try:
            cursor.execute("ANALYZE")
            print("   ✅ Statistiques de la base de données mises à jour")
        except Exception as e:
            print(f"   ⚠️ Erreur lors de l'analyse: {e}")
        
        # 4. Optimiser l'espace disque
        print("\n💾 4. OPTIMISATION DE L'ESPACE DISQUE")
        print("-" * 45)
        
        try:
            # Vérifier la taille avant
            db_size_before = os.path.getsize(db.db_path) if os.path.exists(db.db_path) else 0
            print(f"   📏 Taille avant: {db_size_before / 1024:.1f} KB")
            
            # Défragmenter
            cursor.execute("VACUUM")
            print("   ✅ Défragmentation effectuée")
            
            # Vérifier la taille après
            db_size_after = os.path.getsize(db.db_path) if os.path.exists(db.db_path) else 0
            print(f"   📏 Taille après: {db_size_after / 1024:.1f} KB")
            
            if db_size_before > 0:
                reduction = ((db_size_before - db_size_after) / db_size_before) * 100
                print(f"   🗜️ Réduction: {reduction:.1f}%")
            
        except Exception as e:
            print(f"   ⚠️ Erreur lors de la défragmentation: {e}")
        
        # 5. Vérifier l'intégrité
        print("\n🔍 5. VÉRIFICATION DE L'INTÉGRITÉ")
        print("-" * 45)
        
        try:
            integrity_result = cursor.execute("PRAGMA integrity_check").fetchone()
            if integrity_result and integrity_result[0] == "ok":
                print("   ✅ Intégrité de la base de données: OK")
            else:
                print(f"   ⚠️ Problème d'intégrité: {integrity_result}")
        except Exception as e:
            print(f"   ⚠️ Erreur lors de la vérification: {e}")
        
        # 6. Résumé des performances
        print("\n📊 6. RÉSUMÉ DES OPTIMISATIONS")
        print("-" * 45)
        
        # Compter les enregistrements
        try:
            tables_info = [
                ("categories", "SELECT COUNT(*) FROM categories"),
                ("produits", "SELECT COUNT(*) FROM produits"),
                ("clients", "SELECT COUNT(*) FROM clients"),
                ("ventes", "SELECT COUNT(*) FROM ventes"),
                ("details_vente", "SELECT COUNT(*) FROM details_vente")
            ]
            
            for table_name, query in tables_info:
                try:
                    result = cursor.execute(query).fetchone()
                    count = result[0] if result else 0
                    print(f"   📋 {table_name}: {count} enregistrements")
                except Exception as e:
                    print(f"   ⚠️ Erreur pour {table_name}: {e}")
        
        except Exception as e:
            print(f"   ⚠️ Erreur lors du comptage: {e}")
        
        print("\n🎉 OPTIMISATION TERMINÉE AVEC SUCCÈS!")
        print("=" * 40)
        print("📈 Améliorations appliquées:")
        print("   • Configuration SQLite optimisée")
        print("   • Index de performance créés")
        print("   • Statistiques mises à jour")
        print("   • Base de données défragmentée")
        print("   • Intégrité vérifiée")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'optimisation: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        db.disconnect()


def main():
    """Fonction principale"""
    try:
        success = optimiser_base_donnees()
        if success:
            print("\n✅ L'optimisation s'est terminée avec succès!")
            print("🚀 Votre base de données est maintenant optimisée pour de meilleures performances.")
        else:
            print("\n❌ L'optimisation a échoué.")
            print("🔧 Vérifiez les erreurs ci-dessus et réessayez.")
    except KeyboardInterrupt:
        print("\n⏹️ Optimisation interrompue par l'utilisateur.")
    except Exception as e:
        print(f"\n💥 Erreur inattendue: {e}")


if __name__ == "__main__":
    main()
