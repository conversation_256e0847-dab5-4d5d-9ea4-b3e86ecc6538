#!/usr/bin/env python3
"""
Test complet du système de ventes
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from database.db_manager import DatabaseManager

class TestVentesCompletApp(MDApp):
    def build(self):
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Initialiser la base de données
        self.db_manager = DatabaseManager()
        
        screen = MDScreen()
        layout = MDBoxLayout(
            orientation='vertical', 
            padding="20dp", 
            spacing="20dp",
            pos_hint={'center_x': 0.5, 'center_y': 0.5}
        )
        
        # Titre
        title = MDLabel(
            text="🛒 Test Système Ventes Complet",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(orientation='vertical', spacing="10dp")
        
        init_btn = MDRaisedButton(
            text="1. Initialiser Données Test",
            size_hint=(None, None),
            size=("300dp", "50dp"),
            pos_hint={'center_x': 0.5},
            on_release=self.init_test_data
        )
        
        test_functions_btn = MDRaisedButton(
            text="2. Tester Fonctions DB",
            size_hint=(None, None),
            size=("300dp", "50dp"),
            pos_hint={'center_x': 0.5},
            on_release=self.test_db_functions
        )
        
        test_screen_btn = MDRaisedButton(
            text="3. Tester Écran Ventes",
            size_hint=(None, None),
            size=("300dp", "50dp"),
            pos_hint={'center_x': 0.5},
            on_release=self.test_sales_screen
        )
        
        buttons_layout.add_widget(init_btn)
        buttons_layout.add_widget(test_functions_btn)
        buttons_layout.add_widget(test_screen_btn)
        
        # Label pour les résultats
        self.result_label = MDLabel(
            text="Cliquez sur les boutons pour tester le système de ventes",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        layout.add_widget(title)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def init_test_data(self, *args):
        """Initialiser des données de test"""
        try:
            print("🔧 Initialisation des données de test...")
            self.result_label.text = "Initialisation en cours..."
            
            # Import des fonctions
            from database.db_manager import add_client, add_product
            
            # Ajouter des clients de test
            clients_test = [
                {
                    'nom': 'Alami',
                    'prenom': 'Mohammed',
                    'email': '<EMAIL>',
                    'telephone': '0612345678',
                    'adresse': 'Casablanca, Maroc',
                    'actif': True
                },
                {
                    'nom': 'Bennani',
                    'prenom': 'Fatima',
                    'email': '<EMAIL>',
                    'telephone': '0687654321',
                    'adresse': 'Rabat, Maroc',
                    'actif': True
                },
                {
                    'entreprise': 'TechCorp Maroc',
                    'nom': 'Responsable',
                    'prenom': 'Achats',
                    'email': '<EMAIL>',
                    'telephone': '0522123456',
                    'adresse': 'Casablanca, Maroc',
                    'actif': True
                }
            ]
            
            for client in clients_test:
                try:
                    add_client(self.db_manager, client)
                    print(f"✅ Client ajouté: {client.get('prenom', '')} {client.get('nom', '')}")
                except Exception as e:
                    print(f"⚠️ Client déjà existant ou erreur: {e}")
            
            # Ajouter des produits de test
            produits_test = [
                {
                    'nom': 'Smartphone Pro Max',
                    'description': 'Smartphone haut de gamme avec écran OLED',
                    'reference': 'PHONE-001',
                    'code_barre': '1234567890123',
                    'prix_achat': 800.0,
                    'prix_vente': 1200.0,
                    'stock_actuel': 25,
                    'stock_minimum': 5,
                    'tva': 20.0,
                    'actif': True
                },
                {
                    'nom': 'Ordinateur Portable Gaming',
                    'description': 'PC portable pour gaming avec RTX',
                    'reference': 'LAPTOP-001',
                    'code_barre': '2345678901234',
                    'prix_achat': 1500.0,
                    'prix_vente': 2200.0,
                    'stock_actuel': 10,
                    'stock_minimum': 2,
                    'tva': 20.0,
                    'actif': True
                },
                {
                    'nom': 'Casque Audio Bluetooth',
                    'description': 'Casque sans fil avec réduction de bruit',
                    'reference': 'AUDIO-001',
                    'code_barre': '3456789012345',
                    'prix_achat': 80.0,
                    'prix_vente': 150.0,
                    'stock_actuel': 50,
                    'stock_minimum': 10,
                    'tva': 20.0,
                    'actif': True
                }
            ]
            
            for produit in produits_test:
                try:
                    add_product(self.db_manager, produit)
                    print(f"✅ Produit ajouté: {produit['nom']}")
                except Exception as e:
                    print(f"⚠️ Produit déjà existant ou erreur: {e}")
            
            self.result_label.text = "✅ Données de test initialisées avec succès!"
            print("✅ Initialisation terminée")
            
        except Exception as e:
            error_msg = f"❌ Erreur initialisation: {str(e)}"
            self.result_label.text = error_msg
            print(error_msg)
            import traceback
            traceback.print_exc()
    
    def test_db_functions(self, *args):
        """Tester les fonctions de base de données"""
        try:
            print("🧪 Test des fonctions de base de données...")
            self.result_label.text = "Test des fonctions DB..."
            
            # Import des fonctions
            from database.db_manager import (
                get_all_clients, get_all_products, get_all_sales,
                create_sale, get_sales_statistics
            )
            
            # Test récupération clients
            clients = get_all_clients(self.db_manager)
            print(f"📋 Clients trouvés: {len(clients)}")
            
            # Test récupération produits
            products = get_all_products(self.db_manager)
            print(f"📦 Produits trouvés: {len(products)}")
            
            # Test récupération ventes
            sales = get_all_sales(self.db_manager)
            print(f"🛒 Ventes trouvées: {len(sales)}")
            
            # Test création d'une vente si on a des données
            if clients and products:
                print("🔧 Test création d'une vente...")
                
                # Préparer une vente de test
                vente_data = {
                    'client_id': clients[0]['id'],
                    'mode_paiement': 'Espèces',
                    'notes': 'Vente de test automatique',
                    'statut': 'En cours'
                }
                
                sale_items = [
                    {
                        'product': products[0],
                        'quantite': 2,
                        'prix_unitaire': products[0]['prix_vente']
                    }
                ]
                
                vente_id = create_sale(self.db_manager, vente_data, sale_items)
                
                if vente_id:
                    print(f"✅ Vente de test créée (ID: {vente_id})")
                else:
                    print("❌ Échec création vente de test")
            
            # Test statistiques
            stats = get_sales_statistics(self.db_manager)
            print(f"📊 Statistiques: {stats}")
            
            self.result_label.text = f"✅ Tests DB OK! Clients: {len(clients)}, Produits: {len(products)}, Ventes: {len(sales)}"
            
        except Exception as e:
            error_msg = f"❌ Erreur test DB: {str(e)}"
            self.result_label.text = error_msg
            print(error_msg)
            import traceback
            traceback.print_exc()
    
    def test_sales_screen(self, *args):
        """Tester l'écran des ventes"""
        try:
            print("🧪 Test de l'écran ventes...")
            self.result_label.text = "Chargement écran ventes..."
            
            # Import de l'écran
            from screens.sales_screen import SalesScreen
            
            # Créer l'écran
            sales_screen = SalesScreen()
            
            # Changer vers cet écran
            self.root.clear_widgets()
            self.root.add_widget(sales_screen)
            
            print("✅ Écran ventes chargé avec succès")
            
        except Exception as e:
            error_msg = f"❌ Erreur écran: {str(e)}"
            self.result_label.text = error_msg
            print(error_msg)
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    print("🚀 TEST SYSTÈME VENTES COMPLET")
    print("=" * 50)
    
    try:
        app = TestVentesCompletApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()