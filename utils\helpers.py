"""
Fonctions utilitaires pour l'application
"""

import re
import os
import json
import csv
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path


def format_currency(amount: float, currency: str = None) -> str:
    """
    Formater un montant en devise
    
    Args:
        amount: Montant à formater
        currency: Code de la devise (optionnel, utilise la config par défaut)
        
    Returns:
        Montant formaté
    """
    try:
        # Importer la configuration de devise
        from config.currency_config import format_currency as format_with_config, CURRENCY_CONFIG
        
        # Si aucune devise spécifiée, utiliser la configuration par défaut
        if currency is None:
            return format_with_config(amount)
        
        # Sinon, utiliser la devise spécifiée (pour compatibilité)
        if currency == "EUR":
            return f"{amount:.2f} DH"  # Converti en DH même si EUR demandé
        elif currency == "USD":
            return f"${amount:.2f}"
        elif currency == "MAD":
            return f"{amount:.2f} DH"
        else:
            return f"{amount:.2f} {currency}"
    except ImportError:
        # Fallback si la config n'est pas disponible
        return f"{amount:.2f} DH"


def format_date(date_obj: datetime, format_type: str = "short") -> str:
    """
    Formater une date
    
    Args:
        date_obj: Objet datetime
        format_type: Type de format ('short', 'long', 'datetime')
        
    Returns:
        Date formatée
    """
    if format_type == "short":
        return date_obj.strftime("%d/%m/%Y")
    elif format_type == "long":
        return date_obj.strftime("%A %d %B %Y")
    elif format_type == "datetime":
        return date_obj.strftime("%d/%m/%Y %H:%M")
    else:
        return date_obj.strftime("%d/%m/%Y")


def parse_date(date_string: str) -> Optional[datetime]:
    """
    Parser une chaîne de date
    
    Args:
        date_string: Chaîne de date
        
    Returns:
        Objet datetime ou None si erreur
    """
    formats = [
        "%Y-%m-%d",
        "%d/%m/%Y",
        "%Y-%m-%d %H:%M:%S",
        "%d/%m/%Y %H:%M"
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(date_string, fmt)
        except ValueError:
            continue
    
    return None


def validate_email(email: str) -> bool:
    """
    Valider une adresse email
    
    Args:
        email: Adresse email à valider
        
    Returns:
        True si valide, False sinon
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def validate_phone(phone: str) -> bool:
    """
    Valider un numéro de téléphone français
    
    Args:
        phone: Numéro de téléphone à valider
        
    Returns:
        True si valide, False sinon
    """
    # Supprimer les espaces et points
    clean_phone = re.sub(r'[\s\.]', '', phone)
    
    # Vérifier le format français
    patterns = [
        r'^0[1-9](\d{8})$',  # Format 0123456789
        r'^\+33[1-9](\d{8})$',  # Format +33123456789
        r'^33[1-9](\d{8})$'  # Format 33123456789
    ]
    
    return any(re.match(pattern, clean_phone) for pattern in patterns)


def generate_reference(prefix: str = "REF", length: int = 6) -> str:
    """
    Générer une référence unique
    
    Args:
        prefix: Préfixe de la référence
        length: Longueur de la partie numérique
        
    Returns:
        Référence générée
    """
    import random
    import string
    
    timestamp = datetime.now().strftime("%Y%m%d")
    random_part = ''.join(random.choices(string.digits, k=length))
    
    return f"{prefix}-{timestamp}-{random_part}"


def calculate_tva(amount_ht: float, tva_rate: float) -> Dict[str, float]:
    """
    Calculer la TVA et le montant TTC
    
    Args:
        amount_ht: Montant hors taxes
        tva_rate: Taux de TVA en pourcentage
        
    Returns:
        Dictionnaire avec montant_ht, montant_tva, montant_ttc
    """
    montant_tva = amount_ht * (tva_rate / 100)
    montant_ttc = amount_ht + montant_tva
    
    return {
        'montant_ht': round(amount_ht, 2),
        'montant_tva': round(montant_tva, 2),
        'montant_ttc': round(montant_ttc, 2)
    }


def export_to_csv(data: List[Dict], filename: str, fieldnames: List[str] = None) -> bool:
    """
    Exporter des données vers un fichier CSV
    
    Args:
        data: Liste de dictionnaires à exporter
        filename: Nom du fichier de sortie
        fieldnames: Liste des noms de colonnes
        
    Returns:
        True si succès, False sinon
    """
    try:
        if not data:
            return False
        
        if fieldnames is None:
            fieldnames = list(data[0].keys())
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        return True
    except Exception as e:
        print(f"Erreur lors de l'export CSV: {e}")
        return False


def export_to_json(data: Any, filename: str) -> bool:
    """
    Exporter des données vers un fichier JSON
    
    Args:
        data: Données à exporter
        filename: Nom du fichier de sortie
        
    Returns:
        True si succès, False sinon
    """
    try:
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, indent=2, ensure_ascii=False, default=str)
        
        return True
    except Exception as e:
        print(f"Erreur lors de l'export JSON: {e}")
        return False


def sanitize_filename(filename: str) -> str:
    """
    Nettoyer un nom de fichier
    
    Args:
        filename: Nom de fichier à nettoyer
        
    Returns:
        Nom de fichier nettoyé
    """
    # Supprimer les caractères interdits
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Limiter la longueur
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename


def get_file_size(filepath: str) -> int:
    """
    Obtenir la taille d'un fichier en octets
    
    Args:
        filepath: Chemin vers le fichier
        
    Returns:
        Taille en octets
    """
    try:
        return os.path.getsize(filepath)
    except OSError:
        return 0


def format_file_size(size_bytes: int) -> str:
    """
    Formater une taille de fichier
    
    Args:
        size_bytes: Taille en octets
        
    Returns:
        Taille formatée
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def create_backup_filename(prefix: str = "backup") -> str:
    """
    Créer un nom de fichier de sauvegarde
    
    Args:
        prefix: Préfixe du nom de fichier
        
    Returns:
        Nom de fichier de sauvegarde
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{prefix}_{timestamp}.db"


def get_period_dates(period: str) -> tuple:
    """
    Obtenir les dates de début et fin pour une période
    
    Args:
        period: Période ('today', 'week', 'month', 'year')
        
    Returns:
        Tuple (date_debut, date_fin)
    """
    today = datetime.now()
    
    if period == 'today':
        start_date = today.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = today.replace(hour=23, minute=59, second=59, microsecond=999999)
    
    elif period == 'week':
        start_date = today - timedelta(days=today.weekday())
        start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = start_date + timedelta(days=6, hours=23, minutes=59, seconds=59)
    
    elif period == 'month':
        start_date = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        if today.month == 12:
            end_date = today.replace(year=today.year+1, month=1, day=1) - timedelta(microseconds=1)
        else:
            end_date = today.replace(month=today.month+1, day=1) - timedelta(microseconds=1)
    
    elif period == 'year':
        start_date = today.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
        end_date = today.replace(month=12, day=31, hour=23, minute=59, second=59, microsecond=999999)
    
    else:
        # Par défaut, aujourd'hui
        start_date = today.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = today.replace(hour=23, minute=59, second=59, microsecond=999999)
    
    return start_date, end_date


def truncate_text(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """
    Tronquer un texte
    
    Args:
        text: Texte à tronquer
        max_length: Longueur maximale
        suffix: Suffixe à ajouter si tronqué
        
    Returns:
        Texte tronqué
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    Division sécurisée (évite la division par zéro)
    
    Args:
        numerator: Numérateur
        denominator: Dénominateur
        default: Valeur par défaut si division par zéro
        
    Returns:
        Résultat de la division ou valeur par défaut
    """
    try:
        return numerator / denominator if denominator != 0 else default
    except (TypeError, ZeroDivisionError):
        return default


def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """
    Calculer le pourcentage de changement
    
    Args:
        old_value: Ancienne valeur
        new_value: Nouvelle valeur
        
    Returns:
        Pourcentage de changement
    """
    if old_value == 0:
        return 100.0 if new_value > 0 else 0.0
    
    return ((new_value - old_value) / old_value) * 100


def is_valid_barcode(barcode: str) -> bool:
    """
    Valider un code-barres (format basique)
    
    Args:
        barcode: Code-barres à valider
        
    Returns:
        True si valide, False sinon
    """
    # Vérification basique : que des chiffres et longueur appropriée
    if not barcode.isdigit():
        return False
    
    # Longueurs courantes pour les codes-barres
    valid_lengths = [8, 12, 13, 14]
    return len(barcode) in valid_lengths


def generate_invoice_number(format_template: str = "FAC-{YYYY}-{MM}-{NNNN}") -> str:
    """
    Générer un numéro de facture
    
    Args:
        format_template: Template du format de facture
        
    Returns:
        Numéro de facture généré
    """
    now = datetime.now()
    
    # Remplacements disponibles
    replacements = {
        '{YYYY}': now.strftime('%Y'),
        '{YY}': now.strftime('%y'),
        '{MM}': now.strftime('%m'),
        '{DD}': now.strftime('%d'),
        '{NNNN}': f"{now.hour:02d}{now.minute:02d}",  # Simplification pour l'exemple
    }
    
    result = format_template
    for placeholder, value in replacements.items():
        result = result.replace(placeholder, value)
    
    return result


def clean_phone_number(phone: str) -> str:
    """
    Nettoyer un numéro de téléphone
    
    Args:
        phone: Numéro de téléphone à nettoyer
        
    Returns:
        Numéro de téléphone nettoyé
    """
    # Supprimer tous les caractères non numériques sauf le +
    cleaned = re.sub(r'[^\d+]', '', phone)
    
    # Formater selon les standards français
    if cleaned.startswith('+33'):
        cleaned = '0' + cleaned[3:]
    elif cleaned.startswith('33') and len(cleaned) == 11:
        cleaned = '0' + cleaned[2:]
    
    return cleaned