"""
Test du formulaire de vente avec recherche de produits
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

# Supprimer les avertissements
import warnings
warnings.filterwarnings("ignore")

# Configuration Kivy
os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
os.environ['KIVY_LOG_MODE'] = 'PYTHON'

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel

from forms.sales_form import SalesFormDialog
from database.db_manager import DatabaseManager, add_client, add_category, add_product, get_all_categories


class TestSalesFormSearchApp(MDApp):
    """Application de test pour le formulaire de vente avec recherche"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Formulaire de Vente avec Recherche"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Initialiser la base de données
        self.db_manager = DatabaseManager()
        if self.db_manager.connect():
            self.db_manager.initialize_database()
            print("✅ Base de données initialisée")
        else:
            print("❌ Erreur d'initialisation de la base de données")
    
    def build(self):
        """Construire l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="20dp"
        )
        
        # Titre
        title = MDLabel(
            text="Test du Formulaire de Vente avec Recherche",
            font_style="H5",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        # Description
        description = MDLabel(
            text="Cliquez sur le bouton pour ouvrir le formulaire de vente amélioré\navec la fonctionnalité de recherche de produits.",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Bouton pour ouvrir le formulaire
        open_form_btn = MDRaisedButton(
            text="🛒 Ouvrir Formulaire de Vente",
            size_hint=(None, None),
            size=("300dp", "50dp"),
            pos_hint={"center_x": 0.5},
            on_release=self.open_sales_form
        )
        
        # Bouton pour créer des données de test
        create_data_btn = MDRaisedButton(
            text="📦 Créer Données de Test",
            size_hint=(None, None),
            size=("300dp", "50dp"),
            pos_hint={"center_x": 0.5},
            on_release=self.create_test_data
        )
        
        # Instructions
        instructions = MDLabel(
            text="Instructions:\n" +
                 "1. Créez d'abord des données de test\n" +
                 "2. Ouvrez le formulaire de vente\n" +
                 "3. Testez la recherche de produits\n" +
                 "4. Tapez au moins 2 caractères pour déclencher la recherche\n" +
                 "5. Cliquez sur les produits trouvés pour les ajouter",
            font_style="Caption",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="120dp"
        )
        
        layout.add_widget(title)
        layout.add_widget(description)
        layout.add_widget(create_data_btn)
        layout.add_widget(open_form_btn)
        layout.add_widget(instructions)
        
        screen.add_widget(layout)
        return screen
    
    def create_test_data(self, *args):
        """Créer des données de test"""
        try:
            # Créer des clients de test
            clients_test = [
                {
                    'nom': 'Dupont',
                    'prenom': 'Jean',
                    'entreprise': 'SARL Dupont',
                    'email': '<EMAIL>',
                    'telephone': '0123456789'
                },
                {
                    'nom': 'Martin',
                    'prenom': 'Marie',
                    'entreprise': 'Entreprise Martin',
                    'email': '<EMAIL>',
                    'telephone': '0987654321'
                }
            ]
            
            for client in clients_test:
                add_client(self.db_manager, client)
            
            # Créer des catégories de test
            categories_test = [
                {'nom': 'Électronique', 'description': 'Produits électroniques'},
                {'nom': 'Informatique', 'description': 'Matériel informatique'},
                {'nom': 'Mobilier', 'description': 'Mobilier de bureau'}
            ]
            
            for category in categories_test:
                add_category(self.db_manager, category)
            
            # Récupérer les catégories créées
            categories = get_all_categories(self.db_manager)
            cat_electronique = next((c for c in categories if c['nom'] == 'Électronique'), None)
            cat_informatique = next((c for c in categories if c['nom'] == 'Informatique'), None)
            cat_mobilier = next((c for c in categories if c['nom'] == 'Mobilier'), None)
            
            # Créer des produits de test
            produits_test = [
                {
                    'nom': 'Ordinateur Portable HP',
                    'description': 'Ordinateur portable HP 15 pouces',
                    'reference': 'HP-001',
                    'prix_achat': 500.00,
                    'prix_vente': 750.00,
                    'stock_actuel': 10,
                    'stock_minimum': 2,
                    'categorie_id': cat_informatique['id'] if cat_informatique else None,
                    'tva': 20.0
                },
                {
                    'nom': 'Souris Logitech',
                    'description': 'Souris optique sans fil',
                    'reference': 'LOG-001',
                    'prix_achat': 15.00,
                    'prix_vente': 25.00,
                    'stock_actuel': 50,
                    'stock_minimum': 10,
                    'categorie_id': cat_informatique['id'] if cat_informatique else None,
                    'tva': 20.0
                },
                {
                    'nom': 'Clavier Mécanique',
                    'description': 'Clavier mécanique RGB',
                    'reference': 'KEY-001',
                    'prix_achat': 80.00,
                    'prix_vente': 120.00,
                    'stock_actuel': 25,
                    'stock_minimum': 5,
                    'categorie_id': cat_informatique['id'] if cat_informatique else None,
                    'tva': 20.0
                },
                {
                    'nom': 'Écran Samsung 24"',
                    'description': 'Écran LED 24 pouces Full HD',
                    'reference': 'SAM-001',
                    'prix_achat': 150.00,
                    'prix_vente': 220.00,
                    'stock_actuel': 15,
                    'stock_minimum': 3,
                    'categorie_id': cat_electronique['id'] if cat_electronique else None,
                    'tva': 20.0
                },
                {
                    'nom': 'Chaise de Bureau',
                    'description': 'Chaise ergonomique de bureau',
                    'reference': 'CHR-001',
                    'prix_achat': 100.00,
                    'prix_vente': 150.00,
                    'stock_actuel': 8,
                    'stock_minimum': 2,
                    'categorie_id': cat_mobilier['id'] if cat_mobilier else None,
                    'tva': 20.0
                },
                {
                    'nom': 'Bureau en Bois',
                    'description': 'Bureau en bois massif 120x60cm',
                    'reference': 'BUR-001',
                    'prix_achat': 200.00,
                    'prix_vente': 300.00,
                    'stock_actuel': 5,
                    'stock_minimum': 1,
                    'categorie_id': cat_mobilier['id'] if cat_mobilier else None,
                    'tva': 20.0
                }
            ]
            
            for produit in produits_test:
                add_product(self.db_manager, produit)
            
            print("✅ Données de test créées avec succès!")
            print(f"   - {len(clients_test)} clients")
            print(f"   - {len(categories_test)} catégories")
            print(f"   - {len(produits_test)} produits")
            
        except Exception as e:
            print(f"❌ Erreur lors de la création des données de test: {e}")
    
    def open_sales_form(self, *args):
        """Ouvrir le formulaire de vente"""
        try:
            dialog = SalesFormDialog(
                on_save_callback=self.on_sale_saved
            )
            dialog.open()
            print("✅ Formulaire de vente ouvert")
        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du formulaire: {e}")
    
    def on_sale_saved(self, sale_data):
        """Callback appelé quand une vente est sauvegardée"""
        print(f"✅ Vente sauvegardée: {sale_data}")


def main():
    """Fonction principale"""
    print("🚀 Démarrage du test du formulaire de vente avec recherche...")
    
    try:
        app = TestSalesFormSearchApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()