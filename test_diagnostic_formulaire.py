#!/usr/bin/env python3
"""
Test de diagnostic pour identifier pourquoi les champs ne sont pas visibles
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton, MDFlatButton
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.dialog import MDDialog


class DiagnosticFormDialog(MDDialog):
    """Dialog de diagnostic pour tester la visibilité des champs"""
    
    def __init__(self, **kwargs):
        # Créer les boutons d'abord
        self.cancel_btn = MDFlatButton(
            text="❌ Annuler",
            on_release=self.dismiss_dialog
        )
        
        self.save_btn = MDRaisedButton(
            text="💾 Test",
            on_release=self.test_fields
        )
        
        super().__init__(
            title="🔍 Test Diagnostic",
            type="custom",
            size_hint=(0.9, None),
            height="500dp",
            buttons=[self.cancel_btn, self.save_btn],
            **kwargs
        )
        
        self.create_diagnostic_form()
    
    def create_diagnostic_form(self):
        """Créer un formulaire de diagnostic simple"""
        print("🔍 Création du formulaire de diagnostic...")
        
        # Container principal
        main_container = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="20dp",
            size_hint_y=None,
            height="400dp"
        )
        print("✅ Container principal créé")
        
        # Test 1: Label simple
        test_label = MDLabel(
            text="🧪 TEST 1: Ce label devrait être visible",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp"
        )
        main_container.add_widget(test_label)
        print("✅ Label de test ajouté")
        
        # Test 2: Champ avec couleurs explicites
        test_field = MDTextField(
            text="Test de visibilité",
            hint_text="Ce champ devrait être visible",
            size_hint_y=None,
            height="56dp",
            mode="rectangle",
            line_color_normal=[1, 0, 0, 1],  # Rouge pour être sûr de le voir
            line_color_focus=[0, 1, 0, 1],   # Vert au focus
            text_color_normal=[0, 0, 0, 1],  # Texte noir
            fill_color_normal=[1, 1, 0, 0.3], # Fond jaune transparent
        )
        main_container.add_widget(test_field)
        print("✅ Champ de test ajouté avec couleurs visibles")
        
        # Test 3: Deuxième champ
        test_field2 = MDTextField(
            text="Deuxième test",
            hint_text="Deuxième champ de test",
            size_hint_y=None,
            height="56dp",
            mode="rectangle",
            line_color_normal=[0, 0, 1, 1],  # Bleu
            text_color_normal=[0, 0, 0, 1],
            fill_color_normal=[0.9, 0.9, 0.9, 1],
        )
        main_container.add_widget(test_field2)
        print("✅ Deuxième champ de test ajouté")
        
        # Test 4: Label d'information
        info_label = MDLabel(
            text="Si vous voyez ce texte mais pas les champs ci-dessus,\n"
                 "il y a un problème de rendu des MDTextField.",
            font_style="Body1",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="60dp",
            halign="center"
        )
        main_container.add_widget(info_label)
        print("✅ Label d'information ajouté")
        
        # Stocker les références pour le test
        self.test_field = test_field
        self.test_field2 = test_field2
        
        # IMPORTANT: Assigner le contenu
        self.content_cls = main_container
        print("✅ Contenu assigné au dialog")
        print("🔍 Formulaire de diagnostic créé avec succès")
    
    def test_fields(self, *args):
        """Tester les champs"""
        try:
            value1 = self.test_field.text
            value2 = self.test_field2.text
            
            print(f"✅ Champ 1 accessible: '{value1}'")
            print(f"✅ Champ 2 accessible: '{value2}'")
            
            # Modifier les valeurs pour voir si c'est visible
            self.test_field.text = "MODIFIÉ !"
            self.test_field2.text = "CHANGÉ !"
            
            print("✅ Valeurs modifiées - vérifiez si vous voyez les changements")
            
        except Exception as e:
            print(f"❌ Erreur lors du test: {e}")
    
    def dismiss_dialog(self, *args):
        """Fermer le dialog"""
        print("🔍 Fermeture du dialog de diagnostic")
        self.dismiss()


class DiagnosticApp(MDApp):
    """Application de diagnostic"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Diagnostic - Formulaire"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construction de l'interface"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        # Titre
        title = MDLabel(
            text="🔍 DIAGNOSTIC - Visibilité des Champs",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Instructions
        instructions = MDLabel(
            text="Ce test va ouvrir un formulaire de diagnostic.\n\n"
                 "VÉRIFIEZ QUE VOUS VOYEZ :\n"
                 "✅ Un label 'TEST 1: Ce label devrait être visible'\n"
                 "✅ Un champ avec bordure ROUGE et fond JAUNE\n"
                 "✅ Un champ avec bordure BLEUE et fond gris\n"
                 "✅ Un label d'information en bas\n\n"
                 "Si vous ne voyez que les boutons, nous saurons\n"
                 "que le problème vient du rendu des MDTextField.",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="200dp"
        )
        
        # Bouton de test
        test_btn = MDRaisedButton(
            text="🔍 Ouvrir Test Diagnostic",
            size_hint_y=None,
            height="60dp",
            on_release=self.open_diagnostic
        )
        
        # Résultats
        self.result_label = MDLabel(
            text="Cliquez sur le bouton pour lancer le diagnostic.",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(instructions)
        layout.add_widget(test_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def open_diagnostic(self, *args):
        """Ouvrir le diagnostic"""
        self.result_label.text = "🔍 DIAGNOSTIC OUVERT\n\n" \
                                "Vérifiez dans le formulaire :\n" \
                                "1. Voyez-vous le label de test ?\n" \
                                "2. Voyez-vous les champs colorés ?\n" \
                                "3. Pouvez-vous cliquer dans les champs ?\n\n" \
                                "Cliquez 'Test' pour modifier les valeurs."
        
        print("🔍 Ouverture du diagnostic...")
        dialog = DiagnosticFormDialog()
        dialog.open()


def main():
    """Fonction principale"""
    print("🔍 DIAGNOSTIC - Problème de Visibilité des Champs")
    print("=" * 60)
    print("Ce diagnostic va identifier pourquoi les champs ne sont pas visibles.")
    print()
    print("TESTS À EFFECTUER :")
    print("1. Ouvrir le formulaire de diagnostic")
    print("2. Vérifier si vous voyez les labels")
    print("3. Vérifier si vous voyez les champs colorés")
    print("4. Tester l'interaction avec les champs")
    print()
    print("RÉSULTATS POSSIBLES :")
    print("- Si vous voyez tout : Le problème est ailleurs")
    print("- Si vous ne voyez que les boutons : Problème de rendu MDTextField")
    print("- Si vous voyez les labels mais pas les champs : Problème spécifique aux champs")
    print("=" * 60)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = DiagnosticApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du diagnostic: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()