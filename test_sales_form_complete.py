#!/usr/bin/env python3
"""
Test du formulaire de vente complet avec tous les champs et boutons
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

# Configuration pour Windows
if sys.platform == 'win32':
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel

# Import du formulaire de vente
from forms.sales_form import SalesFormDialog


class TestSalesFormCompleteApp(MDApp):
    """Test du formulaire de vente complet"""
    
    def build(self):
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        title = MDLabel(
            text="🛒 Test Formulaire Vente\nComplet avec Tous les Champs",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Test nouveau formulaire
        new_form_btn = MDRaisedButton(
            text="➕ Nouveau Formulaire de Vente",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_new_form
        )
        
        # Test modification formulaire
        edit_form_btn = MDRaisedButton(
            text="✏️ Formulaire de Modification",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_edit_form
        )
        
        self.result_label = MDLabel(
            text="Test du formulaire de vente complet\n\n" \
                 "CHAMPS À VÉRIFIER :\n" \
                 "✅ 👤 Client (liste déroulante)\n" \
                 "✅ 🛍️ Produits (sélection multiple)\n" \
                 "✅ 💰 Montants (HT/TTC calculés)\n" \
                 "✅ 💳 Mode de paiement (liste)\n" \
                 "✅ 📝 Notes (texte libre)\n" \
                 "✅ ℹ️ Informations (en modification)\n\n" \
                 "BOUTONS À VÉRIFIER :\n" \
                 "✅ ❌ Annuler\n" \
                 "✅ 💾 Enregistrer\n" \
                 "✅ ➕ Ajouter produit\n" \
                 "✅ 🗑️ Supprimer produit",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(new_form_btn)
        layout.add_widget(edit_form_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_new_form(self, *args):
        """Tester le formulaire de nouvelle vente"""
        try:
            def on_save(sale_data):
                print(f"✅ Vente sauvegardée: {sale_data}")
                self.result_label.text = "✅ NOUVEAU FORMULAIRE TESTÉ !\n\n" \
                                       "VÉRIFICATIONS :\n" \
                                       "✅ Modalview 95% x 85% (grande taille)\n" \
                                       "✅ Scroll vertical activé\n" \
                                       "✅ Toutes les sections visibles\n" \
                                       "✅ Champs bien dimensionnés\n" \
                                       "✅ Boutons Annuler/Enregistrer\n\n" \
                                       "SECTIONS AFFICHÉES :\n" \
                                       "📋 En-tête avec titre\n" \
                                       "👤 Client (obligatoire)\n" \
                                       "🛍️ Produits (avec bouton +)\n" \
                                       "💰 Montants (calculés auto)\n" \
                                       "💳 Paiement (liste déroulante)\n" \
                                       "📝 Notes (optionnel)"
            
            dialog = SalesFormDialog(on_save_callback=on_save)
            dialog.open()
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur nouveau formulaire :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def test_edit_form(self, *args):
        """Tester le formulaire de modification"""
        try:
            # Données de test pour la modification
            test_sale_data = {
                'id': 123,
                'numero_facture': 'FAC-20241201-TEST',
                'client_id': 1,
                'client_nom': 'Client Test',
                'date_vente': '2024-12-01T10:30:00',
                'montant_ht': 100.00,
                'montant_ttc': 120.00,
                'mode_paiement': '💳 Carte bancaire',
                'statut': 'En cours',
                'notes': 'Vente de test pour modification'
            }
            
            def on_save(sale_data):
                print(f"✅ Vente modifiée: {sale_data}")
                self.result_label.text = "✅ FORMULAIRE MODIFICATION TESTÉ !\n\n" \
                                       "VÉRIFICATIONS :\n" \
                                       "✅ Titre 'Modifier la vente'\n" \
                                       "✅ Données pré-remplies\n" \
                                       "✅ Section informations ajoutée\n" \
                                       "✅ Date et ID affichés\n" \
                                       "✅ Tous les champs éditables\n\n" \
                                       "SECTIONS AFFICHÉES :\n" \
                                       "📋 En-tête avec numéro facture\n" \
                                       "👤 Client (pré-sélectionné)\n" \
                                       "🛍️ Produits (existants)\n" \
                                       "💰 Montants (pré-calculés)\n" \
                                       "💳 Paiement (pré-sélectionné)\n" \
                                       "📝 Notes (pré-remplies)\n" \
                                       "ℹ️ Informations (date/ID)"
            
            dialog = SalesFormDialog(
                sale_data=test_sale_data,
                on_save_callback=on_save
            )
            dialog.open()
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur formulaire modification :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()


def main():
    print("🛒 Test Formulaire de Vente Complet")
    print("OBJECTIF: Vérifier que tous les champs et boutons sont visibles")
    
    try:
        app = TestSalesFormCompleteApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()