#!/usr/bin/env python3
"""
Test du formulaire de vente amélioré avec listes déroulantes
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from sales_form_improved import ImprovedSaleFormDialog


class TestSalesFormImprovedApp(MDApp):
    """Application de test pour le formulaire de vente amélioré"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Formulaire de Vente Amélioré"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construction de l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        # Titre
        title = MDLabel(
            text="🛒 Formulaire de Vente Amélioré",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Instructions
        instructions = MDLabel(
            text="FORMULAIRE DE VENTE AVEC LISTES DÉROULANTES :\n\n"
                 "✅ Structure basée sur la table ventes\n"
                 "✅ Champ client : Liste déroulante des clients actifs\n"
                 "✅ Champ paiement : Liste avec icônes (Espèces, Carte, etc.)\n"
                 "✅ Validation automatique des champs obligatoires\n"
                 "✅ Interface utilisateur intuitive\n"
                 "✅ Couleurs distinctives pour les listes\n\n"
                 "FONCTIONNALITÉS TESTÉES :\n"
                 "👤 Sélection client avec recherche\n"
                 "💳 Modes de paiement avec icônes\n"
                 "📦 Gestion des produits (à implémenter)\n"
                 "💰 Calcul automatique des totaux\n"
                 "📝 Notes optionnelles\n"
                 "💾 Sauvegarde en base de données",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="320dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="140dp"
        )
        
        new_sale_btn = MDRaisedButton(
            text="🆕 Nouvelle Vente",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_new_sale
        )
        
        edit_sale_btn = MDRaisedButton(
            text="✏️ Modification avec Données",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_edit_sale
        )
        
        buttons_layout.add_widget(new_sale_btn)
        buttons_layout.add_widget(edit_sale_btn)
        
        # Résultats
        self.result_label = MDLabel(
            text="Testez le formulaire de vente avec listes déroulantes !",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(instructions)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_new_sale(self, *args):
        """Tester le formulaire de nouvelle vente"""
        self.result_label.text = "🆕 NOUVEAU FORMULAIRE DE VENTE OUVERT\n\n" \
                                "TESTEZ LES LISTES DÉROULANTES :\n" \
                                "👤 Cliquez sur le champ Client\n" \
                                "💳 Cliquez sur le champ Mode de paiement\n" \
                                "📦 Ajoutez des produits (bouton +)\n" \
                                "📝 Saisissez des notes optionnelles\n" \
                                "💾 Enregistrez la vente\n\n" \
                                "Vérifiez que les couleurs distinguent\n" \
                                "les champs de liste (bleu/vert) !"
        
        dialog = ImprovedSaleFormDialog(
            on_save_callback=self.on_sale_saved
        )
        dialog.open()
        print("🆕 Nouveau formulaire de vente avec listes déroulantes ouvert")
    
    def test_edit_sale(self, *args):
        """Tester le formulaire de modification"""
        self.result_label.text = "✏️ MODIFICATION AVEC DONNÉES DE TEST\n\n" \
                                "DONNÉES CHARGÉES :\n" \
                                "🆔 ID: 1 (PRIMARY KEY)\n" \
                                "📄 Facture: FAC-20241215-143000\n" \
                                "👤 Client: Jean Dupont (pré-sélectionné)\n" \
                                "💳 Paiement: Carte bancaire (pré-sélectionné)\n" \
                                "📅 Date: 15/12/2024\n" \
                                "💰 Montant: 150.00 DH TTC\n\n" \
                                "Vérifiez que les listes affichent\n" \
                                "les valeurs actuelles !"
        
        # Données de test correspondant à la structure de la table ventes
        test_data = {
            'id': 1,  # PRIMARY KEY AUTOINCREMENT
            'numero_facture': 'FAC-20241215-143000',  # TEXT UNIQUE NOT NULL
            'client_id': 1,  # INTEGER REFERENCES clients(id)
            'date_vente': '2024-12-15T14:30:00',  # TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            'montant_ht': 125.00,  # DECIMAL(10,2) NOT NULL
            'montant_ttc': 150.00,  # DECIMAL(10,2) NOT NULL
            'mode_paiement': '💳 Carte bancaire',  # TEXT NOT NULL
            'statut': 'En cours',  # TEXT DEFAULT 'En cours'
            'notes': 'Vente de test avec données pré-remplies'  # TEXT
        }
        
        dialog = ImprovedSaleFormDialog(
            sale_data=test_data,
            on_save_callback=self.on_sale_saved
        )
        dialog.open()
        print("✏️ Formulaire de modification avec données de test ouvert")
    
    def on_sale_saved(self, sale_data):
        """Callback de sauvegarde"""
        numero_facture = sale_data.get('numero_facture', 'N/A')
        client_id = sale_data.get('client_id', 'N/A')
        mode_paiement = sale_data.get('mode_paiement', 'N/A')
        montant_ttc = sale_data.get('montant_ttc', 0)
        sale_id = sale_data.get('id', 'Nouveau')
        
        self.result_label.text = f"🎉 FORMULAIRE DE VENTE FONCTIONNE !\n\n" \
                                f"DONNÉES SAUVEGARDÉES :\n" \
                                f"🆔 ID: {sale_id}\n" \
                                f"📄 Facture: {numero_facture}\n" \
                                f"👤 Client ID: {client_id}\n" \
                                f"💳 Paiement: {mode_paiement}\n" \
                                f"💰 Montant TTC: {montant_ttc:.2f} DH\n\n" \
                                f"✅ Listes déroulantes fonctionnelles !\n" \
                                f"✅ Validation automatique !\n" \
                                f"✅ Sauvegarde réussie !\n" \
                                f"✅ Interface utilisateur optimisée !"
        
        print("🎉 SUCCÈS - Formulaire de vente avec listes déroulantes fonctionnel !")
        print(f"  - Facture: {numero_facture}")
        print(f"  - Client ID: {client_id}")
        print(f"  - Mode de paiement: {mode_paiement}")
        print(f"  - Montant TTC: {montant_ttc:.2f} DH")
        print("✅ Les listes déroulantes pour client et paiement fonctionnent parfaitement")


def main():
    """Fonction principale"""
    print("🛒 Test - Formulaire de Vente Amélioré")
    print("=" * 60)
    print("FORMULAIRE DE VENTE AVEC LISTES DÉROULANTES :")
    print("✅ Basé sur la structure exacte de la table ventes")
    print("✅ Champ client : Liste déroulante des clients actifs")
    print("✅ Champ paiement : Liste avec icônes et descriptions")
    print("✅ Validation automatique des champs obligatoires")
    print("✅ Interface utilisateur intuitive et colorée")
    print()
    print("STRUCTURE DE LA TABLE VENTES :")
    print("- id INTEGER PRIMARY KEY AUTOINCREMENT")
    print("- numero_facture TEXT UNIQUE NOT NULL")
    print("- client_id INTEGER REFERENCES clients(id)")
    print("- date_vente TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    print("- montant_ht DECIMAL(10,2) NOT NULL")
    print("- montant_ttc DECIMAL(10,2) NOT NULL")
    print("- mode_paiement TEXT NOT NULL")
    print("- statut TEXT DEFAULT 'En cours'")
    print("- notes TEXT")
    print()
    print("MODES DE PAIEMENT DISPONIBLES :")
    print("💰 Espèces")
    print("💳 Carte bancaire")
    print("📄 Chèque")
    print("🏦 Virement bancaire")
    print("📱 Paiement électronique")
    print("💸 Crédit")
    print("🔄 Paiement échelonné")
    print("🎁 Bon cadeau")
    print("🤝 Compensation")
    print("❓ Autre")
    print()
    print("FONCTIONNALITÉS DU FORMULAIRE :")
    print("1. Sélection client avec recherche dans la liste")
    print("2. Sélection mode de paiement avec icônes")
    print("3. Validation automatique des champs obligatoires")
    print("4. Couleurs distinctives pour les listes déroulantes")
    print("5. Sauvegarde directe dans la table ventes")
    print("6. Gestion des totaux HT et TTC")
    print("7. Notes optionnelles")
    print("8. Informations de base de données en modification")
    print("=" * 60)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestSalesFormImprovedApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()