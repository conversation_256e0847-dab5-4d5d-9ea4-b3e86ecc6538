"""
Générateur de rapports PDF
"""

from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from datetime import datetime
import os
from pathlib import Path


class PDFGenerator:
    """Générateur de documents PDF"""
    
    def __init__(self, company_info=None):
        self.company_info = company_info or {}
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Configurer les styles personnalisés"""
        # Style pour le titre principal
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        ))
        
        # Style pour les sous-titres
        self.styles.add(ParagraphStyle(
            name='CustomSubtitle',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=20,
            textColor=colors.darkblue
        ))
        
        # Style pour l'en-tête d'entreprise
        self.styles.add(ParagraphStyle(
            name='CompanyHeader',
            parent=self.styles['Normal'],
            fontSize=12,
            alignment=TA_RIGHT,
            textColor=colors.grey
        ))
        
        # Style pour les totaux
        self.styles.add(ParagraphStyle(
            name='Total',
            parent=self.styles['Normal'],
            fontSize=12,
            alignment=TA_RIGHT,
            textColor=colors.darkblue,
            fontName='Helvetica-Bold'
        ))
    
    def create_company_header(self):
        """Créer l'en-tête de l'entreprise"""
        elements = []
        
        if self.company_info:
            company_text = []
            if self.company_info.get('nom'):
                company_text.append(f"<b>{self.company_info['nom']}</b>")
            if self.company_info.get('adresse'):
                company_text.append(self.company_info['adresse'])
            if self.company_info.get('telephone'):
                company_text.append(f"Tél: {self.company_info['telephone']}")
            if self.company_info.get('email'):
                company_text.append(f"Email: {self.company_info['email']}")
            
            if company_text:
                company_para = Paragraph("<br/>".join(company_text), self.styles['CompanyHeader'])
                elements.append(company_para)
                elements.append(Spacer(1, 20))
        
        return elements
    
    def create_footer(self):
        """Créer un pied de page avec les crédits"""
        elements = []
        
        # Ligne de séparation
        elements.append(Spacer(1, 30))
        elements.append(HRFlowable(width="100%", thickness=1, lineCap='round', color=colors.grey))
        elements.append(Spacer(1, 10))
        
        # Informations de crédit
        footer_text = [
            "Généré par GesComPro_LibTam",
            "Powered by: LKAIHAL LAHCEN_AIA",
            f"Date de génération: {datetime.now().strftime('%d/%m/%Y à %H:%M')}"
        ]
        
        footer_para = Paragraph("<br/>".join(footer_text), self.styles['Normal'])
        footer_para.alignment = TA_CENTER
        elements.append(footer_para)
        
        return elements
    
    def generate_invoice_pdf(self, invoice_data, items, filename):
        """Générer une facture PDF"""
        doc = SimpleDocTemplate(filename, pagesize=A4)
        elements = []
        
        # En-tête de l'entreprise
        elements.extend(self.create_company_header())
        
        # Titre
        title = Paragraph("FACTURE", self.styles['CustomTitle'])
        elements.append(title)
        elements.append(Spacer(1, 20))
        
        # Informations de la facture
        invoice_info = [
            ['Numéro de facture:', invoice_data.get('numero_facture', '')],
            ['Date:', invoice_data.get('date_vente', datetime.now().strftime('%d/%m/%Y'))],
            ['Client:', invoice_data.get('client_nom', '')],
            ['Mode de paiement:', invoice_data.get('mode_paiement', '')]
        ]
        
        invoice_table = Table(invoice_info, colWidths=[4*cm, 6*cm])
        invoice_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        elements.append(invoice_table)
        elements.append(Spacer(1, 30))
        
        # Tableau des articles
        data = [['Article', 'Quantité', 'Prix unitaire', 'Total']]
        
        for item in items:
            data.append([
                item.get('nom', ''),
                str(item.get('quantite', 0)),
                f"{item.get('prix_unitaire', 0):.2f} DH",
                f"{item.get('montant_ligne', 0):.2f} DH"
            ])
        
        # Ligne de séparation
        data.append(['', '', '', ''])
        
        # Totaux
        data.append(['', '', 'Total HT:', f"{invoice_data.get('montant_ht', 0):.2f} DH"])
        data.append(['', '', 'TVA:', f"{invoice_data.get('montant_tva', 0):.2f} DH"])
        data.append(['', '', 'Total TTC:', f"{invoice_data.get('montant_ttc', 0):.2f} DH"])
        
        table = Table(data, colWidths=[8*cm, 2*cm, 3*cm, 3*cm])
        table.setStyle(TableStyle([
            # En-tête
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            
            # Corps du tableau
            ('FONTNAME', (0, 1), (-1, -4), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -4), 10),
            ('ALIGN', (0, 1), (0, -4), 'LEFT'),  # Nom des articles à gauche
            ('ALIGN', (1, 1), (-1, -4), 'RIGHT'),  # Quantités et prix à droite
            
            # Ligne de séparation
            ('LINEBELOW', (0, -4), (-1, -4), 1, colors.black),
            
            # Totaux
            ('FONTNAME', (0, -3), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, -3), (-1, -1), 11),
            ('ALIGN', (0, -3), (-1, -1), 'RIGHT'),
            
            # Bordures
            ('GRID', (0, 0), (-1, -4), 1, colors.black),
        ]))
        
        elements.append(table)
        
        # Notes
        if invoice_data.get('notes'):
            elements.append(Spacer(1, 30))
            notes_title = Paragraph("Notes:", self.styles['CustomSubtitle'])
            elements.append(notes_title)
            notes_text = Paragraph(invoice_data['notes'], self.styles['Normal'])
            elements.append(notes_text)
        
        # Pied de page
        elements.extend(self.create_footer())
        
        # Générer le PDF
        doc.build(elements)
        return True
    
    def generate_product_catalog_pdf(self, products, filename):
        """Générer un catalogue de produits PDF"""
        doc = SimpleDocTemplate(filename, pagesize=A4)
        elements = []
        
        # En-tête de l'entreprise
        elements.extend(self.create_company_header())
        
        # Titre
        title = Paragraph("CATALOGUE PRODUITS", self.styles['CustomTitle'])
        elements.append(title)
        elements.append(Spacer(1, 20))
        
        # Date de génération
        date_para = Paragraph(f"Généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}", 
                             self.styles['Normal'])
        elements.append(date_para)
        elements.append(Spacer(1, 20))
        
        # Tableau des produits
        data = [['Nom', 'Référence', 'Code-barres', 'Prix', 'Stock']]
        
        for product in products:
            data.append([
                product.get('nom', ''),
                product.get('reference', ''),
                product.get('code_barre', ''),
                f"{product.get('prix_vente', 0):.2f} DH",
                str(product.get('stock_actuel', 0))
            ])
        
        table = Table(data, colWidths=[5*cm, 3*cm, 3*cm, 2*cm, 2*cm])
        table.setStyle(TableStyle([
            # En-tête
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            
            # Corps du tableau
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),  # Nom à gauche
            ('ALIGN', (3, 1), (-1, -1), 'RIGHT'),  # Prix et stock à droite
            
            # Bordures
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        elements.append(table)
        
        # Statistiques
        elements.append(Spacer(1, 30))
        stats_title = Paragraph("Statistiques", self.styles['CustomSubtitle'])
        elements.append(stats_title)
        
        total_products = len(products)
        total_value = sum(p.get('prix_vente', 0) * p.get('stock_actuel', 0) for p in products)
        avg_price = sum(p.get('prix_vente', 0) for p in products) / total_products if total_products > 0 else 0
        
        stats_data = [
            ['Nombre total de produits:', str(total_products)],
            ['Valeur totale du stock:', f"{total_value:.2f} DH"],
            ['Prix moyen:', f"{avg_price:.2f} DH"]
        ]
        
        stats_table = Table(stats_data, colWidths=[6*cm, 4*cm])
        stats_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        elements.append(stats_table)
        
        # Pied de page
        elements.extend(self.create_footer())
        
        # Générer le PDF
        doc.build(elements)
        return True
    
    def generate_sales_report_pdf(self, sales_data, period_name, filename):
        """Générer un rapport de ventes PDF"""
        doc = SimpleDocTemplate(filename, pagesize=A4)
        elements = []
        
        # En-tête de l'entreprise
        elements.extend(self.create_company_header())
        
        # Titre
        title = Paragraph(f"RAPPORT DE VENTES - {period_name.upper()}", self.styles['CustomTitle'])
        elements.append(title)
        elements.append(Spacer(1, 20))
        
        # Date de génération
        date_para = Paragraph(f"Généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}", 
                             self.styles['Normal'])
        elements.append(date_para)
        elements.append(Spacer(1, 20))
        
        # Statistiques générales
        stats_title = Paragraph("Statistiques générales", self.styles['CustomSubtitle'])
        elements.append(stats_title)
        
        nb_ventes = sales_data.get('nb_ventes', 0)
        ca_total = sales_data.get('ca_total', 0)
        panier_moyen = sales_data.get('panier_moyen', 0)
        clients_actifs = sales_data.get('clients_actifs', 0)
        
        stats_data = [
            ['Nombre de ventes:', str(nb_ventes)],
            ['Chiffre d\'affaires:', f"{ca_total:.2f} DH"],
            ['Panier moyen:', f"{panier_moyen:.2f} DH"],
            ['Clients actifs:', str(clients_actifs)]
        ]
        
        stats_table = Table(stats_data, colWidths=[6*cm, 4*cm])
        stats_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        elements.append(stats_table)
        elements.append(Spacer(1, 30))
        
        # Top produits
        top_products = sales_data.get('top_products', [])
        if top_products:
            products_title = Paragraph("Top 10 des produits", self.styles['CustomSubtitle'])
            elements.append(products_title)
            
            products_data = [['Rang', 'Produit', 'Quantité vendue', 'CA']]
            
            for i, product in enumerate(top_products[:10], 1):
                products_data.append([
                    str(i),
                    product.get('nom', ''),
                    str(product.get('quantite_vendue', 0)),
                    f"{product.get('ca_produit', 0):.2f} DH"
                ])
            
            products_table = Table(products_data, colWidths=[1*cm, 6*cm, 3*cm, 3*cm])
            products_table.setStyle(TableStyle([
                # En-tête
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                
                # Corps du tableau
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('ALIGN', (1, 1), (1, -1), 'LEFT'),  # Nom du produit à gauche
                ('ALIGN', (2, 1), (-1, -1), 'RIGHT'),  # Quantité et CA à droite
                
                # Bordures
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))
            
            elements.append(products_table)
            elements.append(Spacer(1, 30))
        
        # Top clients
        top_clients = sales_data.get('top_clients', [])
        if top_clients:
            clients_title = Paragraph("Top 10 des clients", self.styles['CustomSubtitle'])
            elements.append(clients_title)
            
            clients_data = [['Rang', 'Client', 'Nb achats', 'CA']]
            
            for i, client in enumerate(top_clients[:10], 1):
                clients_data.append([
                    str(i),
                    client.get('client_nom', ''),
                    str(client.get('nb_achats', 0)),
                    f"{client.get('ca_client', 0):.2f} DH"
                ])
            
            clients_table = Table(clients_data, colWidths=[1*cm, 6*cm, 3*cm, 3*cm])
            clients_table.setStyle(TableStyle([
                # En-tête
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                
                # Corps du tableau
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('ALIGN', (1, 1), (1, -1), 'LEFT'),  # Nom du client à gauche
                ('ALIGN', (2, 1), (-1, -1), 'RIGHT'),  # Nb achats et CA à droite
                
                # Bordures
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))
            
            elements.append(clients_table)
        
        # Pied de page
        elements.extend(self.create_footer())
        
        # Générer le PDF
        doc.build(elements)
        return True


def create_pdf_directory():
    """Créer le dossier pour les PDF s'il n'existe pas"""
    pdf_dir = Path("exports/pdf")
    pdf_dir.mkdir(parents=True, exist_ok=True)
    return pdf_dir


def generate_invoice_pdf(invoice_data, items, company_info=None):
    """Fonction utilitaire pour générer une facture PDF"""
    try:
        pdf_dir = create_pdf_directory()
        filename = pdf_dir / f"facture_{invoice_data.get('numero_facture', 'XXXX')}.pdf"
        
        generator = PDFGenerator(company_info)
        success = generator.generate_invoice_pdf(invoice_data, items, str(filename))
        
        return str(filename) if success else None
    
    except Exception as e:
        print(f"Erreur lors de la génération de la facture PDF: {e}")
        return None


def generate_product_catalog_pdf(products, company_info=None):
    """Fonction utilitaire pour générer un catalogue produits PDF"""
    try:
        pdf_dir = create_pdf_directory()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = pdf_dir / f"catalogue_produits_{timestamp}.pdf"
        
        generator = PDFGenerator(company_info)
        success = generator.generate_product_catalog_pdf(products, str(filename))
        
        return str(filename) if success else None
    
    except Exception as e:
        print(f"Erreur lors de la génération du catalogue PDF: {e}")
        return None


def generate_sales_report_pdf(sales_data, period_name, company_info=None):
    """Fonction utilitaire pour générer un rapport de ventes PDF"""
    try:
        pdf_dir = create_pdf_directory()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = pdf_dir / f"rapport_ventes_{timestamp}.pdf"
        
        generator = PDFGenerator(company_info)
        success = generator.generate_sales_report_pdf(sales_data, period_name, str(filename))
        
        return str(filename) if success else None
    
    except Exception as e:
        print(f"Erreur lors de la génération du rapport PDF: {e}")
        return None