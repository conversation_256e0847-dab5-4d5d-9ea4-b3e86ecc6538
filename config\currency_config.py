#!/usr/bin/env python3
"""
Configuration des devises pour GesComPro_LibTam
"""

# Configuration de la devise principale
CURRENCY_CONFIG = {
    'code': 'MAD',
    'symbol': 'DH',
    'name': '<PERSON><PERSON><PERSON> Mar<PERSON>',
    'decimal_places': 2,
    'symbol_position': 'after',  # 'before' ou 'after'
    'thousands_separator': ' ',
    'decimal_separator': '.',
}

# Autres devises supportées (pour extension future)
SUPPORTED_CURRENCIES = {
    'MAD': {
        'code': 'MAD',
        'symbol': 'DH',
        'name': '<PERSON>rham Marocain',
        'decimal_places': 2,
        'symbol_position': 'after',
        'thousands_separator': ' ',
        'decimal_separator': '.',
    },
    'MAD': {
        'code': 'MAD',
        'symbol': 'DH',
        'name': 'Dirham Marocain',
        'decimal_places': 2,
        'symbol_position': 'after',
        'thousands_separator': ' ',
        'decimal_separator': '.',
    },
    'USD': {
        'code': 'USD',
        'symbol': '$',
        'name': 'Dollar Américain',
        'decimal_places': 2,
        'symbol_position': 'before',
        'thousands_separator': ',',
        'decimal_separator': '.',
    }
}

def get_current_currency():
    """Retourne la configuration de la devise actuelle"""
    return CURRENCY_CONFIG

def format_currency(amount, currency_config=None):
    """
    Formate un montant selon la configuration de devise
    
    Args:
        amount (float): Montant à formater
        currency_config (dict): Configuration de devise (optionnel)
    
    Returns:
        str: Montant formaté avec la devise
    """
    if currency_config is None:
        currency_config = CURRENCY_CONFIG
    
    # Arrondir selon le nombre de décimales
    decimal_places = currency_config.get('decimal_places', 2)
    rounded_amount = round(float(amount), decimal_places)
    
    # Formater le nombre
    if decimal_places == 0:
        formatted_number = f"{rounded_amount:,.0f}"
    else:
        formatted_number = f"{rounded_amount:,.{decimal_places}f}"
    
    # Remplacer les séparateurs selon la configuration
    thousands_sep = currency_config.get('thousands_separator', ' ')
    decimal_sep = currency_config.get('decimal_separator', '.')
    
    if thousands_sep != ',':
        formatted_number = formatted_number.replace(',', '|TEMP|')
        formatted_number = formatted_number.replace('.', decimal_sep)
        formatted_number = formatted_number.replace('|TEMP|', thousands_sep)
    elif decimal_sep != '.':
        formatted_number = formatted_number.replace('.', decimal_sep)
    
    # Ajouter le symbole de devise
    symbol = currency_config.get('symbol', 'DH')
    symbol_position = currency_config.get('symbol_position', 'after')
    
    if symbol_position == 'before':
        return f"{symbol} {formatted_number}"
    else:
        return f"{formatted_number} {symbol}"

def get_currency_symbol():
    """Retourne le symbole de la devise actuelle"""
    return CURRENCY_CONFIG.get('symbol', 'DH')

def get_currency_code():
    """Retourne le code de la devise actuelle"""
    return CURRENCY_CONFIG.get('code', 'MAD')

def get_currency_name():
    """Retourne le nom de la devise actuelle"""
    return CURRENCY_CONFIG.get('name', 'Dirham Marocain')

# Fonction de compatibilité pour l'ancien système
def format_amount(amount):
    """Fonction de compatibilité - formate un montant avec la devise actuelle"""
    return format_currency(amount)

# Test de la configuration
if __name__ == "__main__":
    print("🪙 Configuration des Devises - GesComPro_LibTam")
    print("=" * 50)
    
    # Test de formatage
    test_amounts = [0, 1.5, 123.45, 1234.56, 12345.67, 123456.78]
    
    print(f"💰 Devise actuelle: {get_currency_name()} ({get_currency_code()})")
    print(f"🔣 Symbole: {get_currency_symbol()}")
    print()
    
    print("📊 Tests de formatage:")
    for amount in test_amounts:
        formatted = format_currency(amount)
        print(f"   {amount:>10} → {formatted}")
    
    print("\n✅ Configuration des devises prête!")