#!/usr/bin/env python3
"""
Lancement direct du module de ventes
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton, MDFlatButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from database.db_manager import DatabaseManager

class VentesDirectApp(MDApp):
    def build(self):
        self.title = "🛒 GesComPro_LibTam - Module Ventes"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Initialiser la base de données
        self.db_manager = DatabaseManager()
        
        screen = MDScreen()
        layout = MDBoxLayout(
            orientation='vertical', 
            padding="20dp", 
            spacing="20dp",
            pos_hint={'center_x': 0.5, 'center_y': 0.5}
        )
        
        # Titre
        title = MDLabel(
            text="🛒 Module de Ventes",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        # Informations
        info_text = """
✅ Système de Ventes Activé !

Fonctionnalités disponibles:
• Création de nouvelles ventes
• Sélection de clients
• Ajout de produits au panier
• Calcul automatique des totaux
• Gestion des stocks
• Affichage en Dirham (DH)
• Statuts de ventes
• Recherche et filtrage
        """
        
        info_label = MDLabel(
            text=info_text.strip(),
            theme_text_color="Primary",
            size_hint_y=None,
            height="200dp",
            halign="center"
        )
        
        # Boutons
        buttons_layout = MDBoxLayout(
            orientation='horizontal',
            spacing="20dp",
            size_hint_y=None,
            height="60dp"
        )
        
        open_sales_btn = MDRaisedButton(
            text="🚀 Ouvrir Module Ventes",
            size_hint=(0.5, 1),
            on_release=self.open_sales_screen
        )
        
        back_btn = MDFlatButton(
            text="🏠 Menu Principal",
            size_hint=(0.5, 1),
            on_release=self.back_to_main
        )
        
        buttons_layout.add_widget(open_sales_btn)
        buttons_layout.add_widget(back_btn)
        
        # Statistiques
        self.stats_label = MDLabel(
            text="Chargement des statistiques...",
            halign="center",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="60dp"
        )
        
        layout.add_widget(title)
        layout.add_widget(info_label)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.stats_label)
        
        screen.add_widget(layout)
        
        # Charger les statistiques
        self.load_stats()
        
        return screen
    
    def load_stats(self):
        """Charger les statistiques de ventes"""
        try:
            from database.db_manager import get_sales_statistics, get_all_sales
            
            # Récupérer les données
            stats = get_sales_statistics(self.db_manager)
            sales = get_all_sales(self.db_manager)
            
            # Formater les statistiques
            nb_ventes = stats.get('nombre_ventes', 0)
            ca_total = stats.get('chiffre_affaires', 0)
            
            from utils.helpers import format_currency
            
            stats_text = f"📊 Ventes: {nb_ventes} | CA: {format_currency(ca_total)} | Dernière vente: {len(sales)} total"
            self.stats_label.text = stats_text
            
        except Exception as e:
            self.stats_label.text = f"❌ Erreur stats: {str(e)}"
            print(f"Erreur stats: {e}")
    
    def open_sales_screen(self, *args):
        """Ouvrir l'écran des ventes"""
        try:
            print("🛒 Ouverture du module de ventes...")
            
            # Import de l'écran
            from screens.sales_screen import SalesScreen
            
            # Créer l'écran
            sales_screen = SalesScreen()
            
            # Changer vers cet écran
            self.root.clear_widgets()
            self.root.add_widget(sales_screen)
            
            print("✅ Module de ventes ouvert avec succès")
            
        except Exception as e:
            print(f"❌ Erreur ouverture module ventes: {e}")
            import traceback
            traceback.print_exc()
    
    def back_to_main(self, *args):
        """Retourner au menu principal"""
        try:
            # Relancer le lanceur principal
            os.system('python launch_simple.py')
            self.stop()
        except Exception as e:
            print(f"❌ Erreur retour menu: {e}")

if __name__ == "__main__":
    print("🛒 LANCEMENT DIRECT - MODULE VENTES")
    print("=" * 50)
    print("✅ Système de ventes entièrement activé")
    print("🪙 Devise: Dirham Marocain (DH)")
    print("🎯 Accès direct aux fonctionnalités de vente")
    print("=" * 50)
    
    try:
        app = VentesDirectApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()
        input("\nAppuyez sur Entrée pour quitter...")