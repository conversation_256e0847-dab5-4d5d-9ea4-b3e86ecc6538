"""
Script de test pour vérifier le bon fonctionnement de l'application
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Tester les imports des modules"""
    print("🔍 Test des imports...")
    
    try:
        # Test des imports principaux
        from database.db_manager import DatabaseManager
        print("✅ DatabaseManager importé")
        
        from utils.barcode_utils import BarcodeGenerator, BarcodeValidator
        print("✅ Utilitaires code-barres importés")
        
        from utils.helpers import format_currency, validate_email
        print("✅ Fonctions utilitaires importées")
        
        from utils.pdf_generator import PDFGenerator
        print("✅ Générateur PDF importé")
        
        # Test des imports d'écrans
        from screens.dashboard_screen import DashboardScreen
        from screens.clients_screen import ClientsScreen
        from screens.products_screen import ProductsScreen
        from screens.sales_screen import SalesScreen
        from screens.reports_screen import ReportsScreen
        from screens.settings_screen import SettingsScreen
        print("✅ Tous les écrans importés")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False


def test_database():
    """Tester la base de données"""
    print("\n🗄️ Test de la base de données...")
    
    try:
        from database.db_manager import DatabaseManager
        
        # Créer une instance de test
        db_manager = DatabaseManager(":memory:")  # Base de données en mémoire pour les tests
        
        # Se connecter d'abord
        if not db_manager.connect():
            print("❌ Impossible de se connecter à la base de données")
            return False
        
        # Initialiser la base de données
        if db_manager.initialize_database():
            print("✅ Base de données initialisée")
        else:
            print("❌ Erreur lors de l'initialisation de la base de données")
            return False
        
        # Reconnecter pour les tests
        if not db_manager.connect():
            print("❌ Impossible de se reconnecter à la base de données")
            return False
        
        # Test d'insertion d'un client
        client_query = """
            INSERT INTO clients (nom, prenom, email, telephone, actif)
            VALUES (?, ?, ?, ?, ?)
        """
        if db_manager.execute_update(client_query, ("Test", "Client", "<EMAIL>", "0123456789", 1)):
            print("✅ Insertion client réussie")
        else:
            print("❌ Erreur lors de l'insertion client")
            return False
        
        # Test de lecture
        clients = db_manager.execute_query("SELECT * FROM clients WHERE nom = ?", ("Test",))
        if clients and len(clients) > 0:
            print("✅ Lecture client réussie")
        else:
            print("❌ Erreur lors de la lecture client")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur de base de données: {e}")
        return False


def test_barcode_utils():
    """Tester les utilitaires de code-barres"""
    print("\n📊 Test des utilitaires code-barres...")
    
    try:
        from utils.barcode_utils import BarcodeGenerator, BarcodeValidator, validate_barcode_input
        
        # Test de génération EAN-13
        ean13 = BarcodeGenerator.generate_ean13()
        if len(ean13) == 13 and ean13.isdigit():
            print(f"✅ EAN-13 généré: {ean13}")
        else:
            print(f"❌ EAN-13 invalide: {ean13}")
            return False
        
        # Test de validation EAN-13
        if BarcodeValidator.validate_ean13(ean13):
            print("✅ Validation EAN-13 réussie")
        else:
            print("❌ Validation EAN-13 échouée")
            return False
        
        # Test de validation d'entrée
        validation = validate_barcode_input("1234567890123")
        if validation['type'] == 'EAN-13':
            print("✅ Validation d'entrée réussie")
        else:
            print(f"❌ Validation d'entrée échouée: {validation}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur utilitaires code-barres: {e}")
        return False


def test_helpers():
    """Tester les fonctions utilitaires"""
    print("\n🛠️ Test des fonctions utilitaires...")
    
    try:
        from utils.helpers import format_currency, validate_email, calculate_tva
        
        # Test de formatage de devise
        formatted = format_currency(123.45)
        if formatted == "123.45 DH":
            print("✅ Formatage devise réussi")
        else:
            print(f"❌ Formatage devise échoué: {formatted}")
            return False
        
        # Test de validation email
        if validate_email("<EMAIL>"):
            print("✅ Validation email réussie")
        else:
            print("❌ Validation email échouée")
            return False
        
        # Test de calcul TVA
        tva_result = calculate_tva(100, 20)
        if tva_result['montant_ttc'] == 120.0:
            print("✅ Calcul TVA réussi")
        else:
            print(f"❌ Calcul TVA échoué: {tva_result}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur fonctions utilitaires: {e}")
        return False


def test_pdf_generator():
    """Tester le générateur PDF"""
    print("\n📄 Test du générateur PDF...")
    
    try:
        from utils.pdf_generator import PDFGenerator
        
        # Créer une instance
        generator = PDFGenerator()
        print("✅ Générateur PDF créé")
        
        # Test de données factice
        invoice_data = {
            'numero_facture': 'TEST-001',
            'date_vente': '2024-01-01',
            'client_nom': 'Client Test',
            'mode_paiement': 'Espèces',
            'montant_ht': 100.0,
            'montant_tva': 20.0,
            'montant_ttc': 120.0
        }
        
        items = [
            {
                'nom': 'Produit Test',
                'quantite': 2,
                'prix_unitaire': 50.0,
                'montant_ligne': 100.0
            }
        ]
        
        # Créer le dossier de test
        test_dir = Path("test_exports")
        test_dir.mkdir(exist_ok=True)
        
        # Générer une facture de test
        test_file = test_dir / "test_invoice.pdf"
        if generator.generate_invoice_pdf(invoice_data, items, str(test_file)):
            print("✅ Génération PDF réussie")
            
            # Vérifier que le fichier existe
            if test_file.exists():
                print("✅ Fichier PDF créé")
                # Nettoyer
                test_file.unlink()
                test_dir.rmdir()
            else:
                print("❌ Fichier PDF non trouvé")
                return False
        else:
            print("❌ Génération PDF échouée")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur générateur PDF: {e}")
        return False


def test_kivy_imports():
    """Tester les imports Kivy/KivyMD"""
    print("\n🎨 Test des imports Kivy/KivyMD...")
    
    try:
        import kivy
        print(f"✅ Kivy {kivy.__version__} importé")
        
        import kivymd
        print(f"✅ KivyMD importé")
        
        from kivymd.app import MDApp
        from kivymd.uix.screen import MDScreen
        from kivymd.uix.button import MDRaisedButton
        print("✅ Composants KivyMD importés")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erreur import Kivy/KivyMD: {e}")
        return False


def test_matplotlib():
    """Tester matplotlib"""
    print("\n📈 Test de matplotlib...")
    
    try:
        import matplotlib
        print(f"✅ Matplotlib {matplotlib.__version__} importé")
        
        import matplotlib.pyplot as plt
        print("✅ Pyplot importé")
        
        # Test de création d'un graphique simple
        fig, ax = plt.subplots()
        ax.plot([1, 2, 3], [1, 4, 2])
        plt.close(fig)  # Fermer pour éviter l'affichage
        print("✅ Graphique de test créé")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur matplotlib: {e}")
        return False


def main():
    """Fonction principale de test"""
    print("🚀 Tests de l'application GesComPro_LibTam")
    print("=" * 50)
    
    tests = [
        ("Imports des modules", test_imports),
        ("Base de données", test_database),
        ("Utilitaires code-barres", test_barcode_utils),
        ("Fonctions utilitaires", test_helpers),
        ("Générateur PDF", test_pdf_generator),
        ("Imports Kivy/KivyMD", test_kivy_imports),
        ("Matplotlib", test_matplotlib)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur lors du test {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:<30} {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print("-" * 50)
    print(f"Total: {len(results)} tests")
    print(f"Réussis: {passed}")
    print(f"Échoués: {failed}")
    
    if failed == 0:
        print("\n🎉 Tous les tests sont réussis! L'application est prête.")
        return True
    else:
        print(f"\n⚠️ {failed} test(s) ont échoué. Vérifiez les erreurs ci-dessus.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)