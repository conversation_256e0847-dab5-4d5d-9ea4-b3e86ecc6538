"""
Test de comparaison des styles entre le formulaire client et le formulaire de vente
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

# Supprimer les avertissements
import warnings
warnings.filterwarnings("ignore")

# Configuration Kivy
os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
os.environ['KIVY_LOG_MODE'] = 'PYTHON'

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel

from forms.sales_form import SalesFormDialog
from screens.clients_screen import ClientFormDialog
from database.db_manager import DatabaseManager


class FormStyleComparisonApp(MDApp):
    """Application de test pour comparer les styles des formulaires"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Comparaison des Styles de Formulaires"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Initialiser la base de données
        self.db_manager = DatabaseManager()
        if self.db_manager.connect():
            self.db_manager.initialize_database()
            print("✅ Base de données initialisée")
        else:
            print("❌ Erreur d'initialisation de la base de données")
    
    def build(self):
        """Construire l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="20dp"
        )
        
        # Titre
        title = MDLabel(
            text="Comparaison des Styles de Formulaires",
            font_style="H5",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        # Description
        description = MDLabel(
            text="Testez les deux formulaires pour comparer leurs styles.\nLes champs doivent avoir un style similaire.",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='horizontal',
            spacing="20dp",
            size_hint_y=None,
            height="60dp",
            adaptive_width=True,
            pos_hint={"center_x": 0.5}
        )
        
        client_form_btn = MDRaisedButton(
            text="👤 Formulaire Client",
            size_hint=(None, None),
            size=("200dp", "50dp"),
            on_release=self.open_client_form
        )
        
        sales_form_btn = MDRaisedButton(
            text="🛒 Formulaire Vente",
            size_hint=(None, None),
            size=("200dp", "50dp"),
            on_release=self.open_sales_form
        )
        
        buttons_layout.add_widget(client_form_btn)
        buttons_layout.add_widget(sales_form_btn)
        
        # Instructions
        instructions = MDLabel(
            text="Instructions de comparaison:\n" +
                 "1. Ouvrez le formulaire client et observez le style des champs\n" +
                 "2. Ouvrez le formulaire de vente et comparez\n" +
                 "3. Vérifiez que les champs ont un style similaire:\n" +
                 "   - Même apparence des MDTextField\n" +
                 "   - Même espacement entre les champs\n" +
                 "   - Même style de hint_text\n" +
                 "   - Même comportement des champs readonly",
            font_style="Caption",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="140dp"
        )
        
        # Résultats attendus
        expected_results = MDLabel(
            text="✅ Résultats attendus:\n" +
                 "• Champs client et paiement: style dropdown avec focus\n" +
                 "• Champs montants: readonly avec helper_text\n" +
                 "• Champ notes: multiline avec max_height\n" +
                 "• Champ recherche: style standard MDTextField\n" +
                 "• Espacement uniforme de 10dp entre les champs",
            font_style="Caption",
            theme_text_color="Primary",
            halign="left",
            size_hint_y=None,
            height="120dp"
        )
        
        layout.add_widget(title)
        layout.add_widget(description)
        layout.add_widget(buttons_layout)
        layout.add_widget(instructions)
        layout.add_widget(expected_results)
        
        screen.add_widget(layout)
        return screen
    
    def open_client_form(self, *args):
        """Ouvrir le formulaire client"""
        try:
            dialog = ClientFormDialog(
                on_save_callback=self.on_client_saved
            )
            dialog.open()
            print("✅ Formulaire client ouvert")
        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du formulaire client: {e}")
    
    def open_sales_form(self, *args):
        """Ouvrir le formulaire de vente"""
        try:
            dialog = SalesFormDialog(
                on_save_callback=self.on_sale_saved
            )
            dialog.open()
            print("✅ Formulaire de vente ouvert")
        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du formulaire de vente: {e}")
    
    def on_client_saved(self, client_data):
        """Callback appelé quand un client est sauvegardé"""
        print(f"✅ Client sauvegardé: {client_data}")
    
    def on_sale_saved(self, sale_data):
        """Callback appelé quand une vente est sauvegardée"""
        print(f"✅ Vente sauvegardée: {sale_data}")


def main():
    """Fonction principale"""
    print("🚀 Démarrage du test de comparaison des styles...")
    
    try:
        app = FormStyleComparisonApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()