"""
Test simple du formulaire de catégories corrigé
"""

import os
import sys
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

# Supprimer les avertissements
import warnings
warnings.filterwarnings("ignore")

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from new_category_form import CategoryFormDialog


class SimpleFormTestApp(MDApp):
    """Application simple pour tester le formulaire"""
    
    def build(self):
        """Interface minimale"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="50dp"
        )
        
        title = MDLabel(
            text="🔧 Test Formulaire Catégories",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        test_btn = MDRaisedButton(
            text="🧪 Tester Formulaire",
            size_hint=(None, None),
            size=("250dp", "60dp"),
            pos_hint={'center_x': 0.5},
            on_release=self.test_form
        )
        
        self.result_label = MDLabel(
            text="Cliquez pour tester",
            halign="center",
            size_hint_y=None,
            height="40dp"
        )
        
        layout.add_widget(title)
        layout.add_widget(test_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_form(self, *args):
        """Tester le formulaire"""
        try:
            self.result_label.text = "🔄 Ouverture du formulaire..."
            
            dialog = CategoryFormDialog(
                on_save_callback=self.on_save
            )
            dialog.open()
            
            self.result_label.text = "✅ Formulaire ouvert - Vérifiez les champs"
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur: {str(e)}"
            print(f"Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def on_save(self, data):
        """Callback de sauvegarde"""
        self.result_label.text = f"💾 Sauvegardé: {data.get('nom', 'N/A')}"


if __name__ == '__main__':
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    SimpleFormTestApp().run()
