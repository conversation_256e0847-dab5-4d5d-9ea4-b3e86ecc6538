#!/usr/bin/env python3
"""
Analyse de la base de données actuelle et recommandations
"""

import os
import sys
import sqlite3
from pathlib import Path

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def analyser_base_actuelle():
    """Analyser la base de données SQLite actuelle"""
    print("🔍 ANALYSE DE LA BASE DE DONNÉES ACTUELLE")
    print("=" * 60)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    
    if not db_manager.connect():
        print("❌ Impossible de se connecter à la base de données")
        return
    
    if not db_manager.initialize_database():
        print("❌ Impossible d'initialiser la base de données")
        return
    
    print("✅ Connexion à la base de données SQLite établie")
    
    try:
        # 1. Informations générales
        print("\n📊 1. INFORMATIONS GÉNÉRALES")
        print("-" * 40)
        
        db_path = Path(db_manager.db_path)
        if db_path.exists():
            size_mb = db_path.stat().st_size / (1024 * 1024)
            print(f"📁 Chemin: {db_path}")
            print(f"📏 Taille: {size_mb:.2f} MB")
        else:
            print("📁 Base de données en mémoire ou non créée")
        
        print(f"🔧 Type: SQLite")
        print(f"🔗 Connexion: sqlite3")
        
        # 2. Structure des tables
        print("\n🏗️ 2. STRUCTURE DES TABLES")
        print("-" * 40)
        
        cursor = db_manager.connection.cursor()
        
        # Lister toutes les tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📋 Nombre de tables: {len(tables)}")
        
        for table in tables:
            table_name = table[0]
            print(f"\n📦 Table: {table_name}")
            
            # Obtenir la structure de la table
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print(f"   📊 Colonnes ({len(columns)}):")
            for col in columns:
                col_id, name, type_name, not_null, default, pk = col
                pk_str = " (PK)" if pk else ""
                null_str = " NOT NULL" if not_null else ""
                default_str = f" DEFAULT {default}" if default else ""
                print(f"      • {name}: {type_name}{pk_str}{null_str}{default_str}")
            
            # Obtenir les clés étrangères
            cursor.execute(f"PRAGMA foreign_key_list({table_name})")
            foreign_keys = cursor.fetchall()
            
            if foreign_keys:
                print(f"   🔗 Clés étrangères ({len(foreign_keys)}):")
                for fk in foreign_keys:
                    id_fk, seq, table_ref, from_col, to_col, on_update, on_delete, match = fk
                    print(f"      • {from_col} → {table_ref}.{to_col}")
            
            # Compter les enregistrements
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   📊 Enregistrements: {count}")
        
        # 3. Index et performances
        print("\n⚡ 3. INDEX ET PERFORMANCES")
        print("-" * 40)
        
        cursor.execute("SELECT name, tbl_name FROM sqlite_master WHERE type='index'")
        indexes = cursor.fetchall()
        
        print(f"📋 Nombre d'index: {len(indexes)}")
        for index in indexes:
            if not index[0].startswith('sqlite_'):  # Ignorer les index système
                print(f"   📊 {index[0]} sur table {index[1]}")
        
        # 4. Contraintes et intégrité
        print("\n🛡️ 4. CONTRAINTES ET INTÉGRITÉ")
        print("-" * 40)
        
        # Vérifier les contraintes UNIQUE
        unique_constraints = []
        for table in tables:
            table_name = table[0]
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            for col in columns:
                if "UNIQUE" in str(col):
                    unique_constraints.append(f"{table_name}.{col[1]}")
        
        print(f"🔒 Contraintes UNIQUE: {len(unique_constraints)}")
        for constraint in unique_constraints:
            print(f"   • {constraint}")
        
        # Vérifier l'intégrité référentielle
        cursor.execute("PRAGMA foreign_keys")
        fk_enabled = cursor.fetchone()[0]
        print(f"🔗 Clés étrangères activées: {'✅ Oui' if fk_enabled else '❌ Non'}")
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db_manager.disconnect()

def recommander_bases_donnees():
    """Recommander les meilleures bases de données selon le contexte"""
    print("\n💡 RECOMMANDATIONS DE BASES DE DONNÉES")
    print("=" * 60)
    
    recommendations = [
        {
            "name": "SQLite",
            "current": True,
            "pros": [
                "✅ Simplicité d'installation et configuration",
                "✅ Pas de serveur requis",
                "✅ Fichier unique portable",
                "✅ Parfait pour développement et tests",
                "✅ Intégré à Python",
                "✅ Transactions ACID",
                "✅ Faible empreinte mémoire"
            ],
            "cons": [
                "❌ Pas de concurrence élevée",
                "❌ Limité pour applications multi-utilisateurs",
                "❌ Pas de réplication native",
                "❌ Types de données limités",
                "❌ Pas de procédures stockées"
            ],
            "use_case": "🎯 Idéal pour: PME, applications desktop, prototypage",
            "max_users": "👥 Utilisateurs simultanés: 1-10",
            "max_data": "📊 Volume de données: < 1 GB"
        },
        {
            "name": "PostgreSQL",
            "current": False,
            "pros": [
                "✅ Très robuste et fiable",
                "✅ Excellent pour concurrence élevée",
                "✅ Types de données avancés (JSON, Arrays)",
                "✅ Procédures stockées et fonctions",
                "✅ Réplication et haute disponibilité",
                "✅ Conformité SQL complète",
                "✅ Extensions riches",
                "✅ Open source"
            ],
            "cons": [
                "❌ Configuration plus complexe",
                "❌ Serveur requis",
                "❌ Plus de ressources nécessaires",
                "❌ Courbe d'apprentissage"
            ],
            "use_case": "🎯 Idéal pour: Grandes entreprises, applications web, analytics",
            "max_users": "👥 Utilisateurs simultanés: 100-1000+",
            "max_data": "📊 Volume de données: > 100 GB"
        },
        {
            "name": "MySQL/MariaDB",
            "current": False,
            "pros": [
                "✅ Très populaire et bien documenté",
                "✅ Performance élevée pour lecture",
                "✅ Réplication facile",
                "✅ Écosystème riche",
                "✅ Bon support communautaire",
                "✅ Compatible avec la plupart des hébergeurs"
            ],
            "cons": [
                "❌ Moins de fonctionnalités avancées que PostgreSQL",
                "❌ Conformité SQL partielle",
                "❌ Configuration serveur requise"
            ],
            "use_case": "🎯 Idéal pour: Applications web, e-commerce, CMS",
            "max_users": "👥 Utilisateurs simultanés: 50-500",
            "max_data": "📊 Volume de données: 10-100 GB"
        },
        {
            "name": "MongoDB",
            "current": False,
            "pros": [
                "✅ Base NoSQL flexible",
                "✅ Stockage de documents JSON",
                "✅ Scalabilité horizontale",
                "✅ Pas de schéma fixe",
                "✅ Bon pour données non-structurées"
            ],
            "cons": [
                "❌ Pas de transactions ACID complètes",
                "❌ Consommation mémoire élevée",
                "❌ Courbe d'apprentissage pour SQL users",
                "❌ Moins adapté aux données relationnelles"
            ],
            "use_case": "🎯 Idéal pour: Applications modernes, APIs, big data",
            "max_users": "👥 Utilisateurs simultanés: Variable",
            "max_data": "📊 Volume de données: > 1 TB"
        }
    ]
    
    for i, db in enumerate(recommendations, 1):
        current_marker = " (ACTUELLE)" if db["current"] else ""
        print(f"\n{i}. 🗄️ {db['name']}{current_marker}")
        print("-" * 50)
        
        print("✅ AVANTAGES:")
        for pro in db["pros"]:
            print(f"   {pro}")
        
        print("\n❌ INCONVÉNIENTS:")
        for con in db["cons"]:
            print(f"   {con}")
        
        print(f"\n{db['use_case']}")
        print(f"{db['max_users']}")
        print(f"{db['max_data']}")

def analyser_besoins_application():
    """Analyser les besoins spécifiques de l'application GesComPro"""
    print("\n🎯 ANALYSE DES BESOINS DE GESCOMPRO_LIBTAM")
    print("=" * 60)
    
    besoins = {
        "type_application": "🏪 Gestion commerciale (ERP/POS)",
        "utilisateurs": "👥 1-20 utilisateurs simultanés (PME)",
        "donnees": "📊 Produits, clients, ventes, stock (< 1 GB)",
        "transactions": "💳 Ventes fréquentes, gestion stock temps réel",
        "rapports": "📈 Analyses de ventes, statistiques",
        "deploiement": "💻 Application desktop locale",
        "maintenance": "🔧 Simplicité de maintenance requise",
        "budget": "💰 Solution économique préférée"
    }
    
    print("📋 CARACTÉRISTIQUES DE L'APPLICATION:")
    for key, value in besoins.items():
        print(f"   {value}")
    
    print("\n🎯 RECOMMANDATION BASÉE SUR L'ANALYSE:")
    print("-" * 50)
    
    print("🏆 BASE DE DONNÉES RECOMMANDÉE: SQLite (ACTUELLE)")
    print("\n✅ JUSTIFICATION:")
    justifications = [
        "🏪 Application de gestion commerciale pour PME",
        "👥 Nombre d'utilisateurs limité (1-20)",
        "📊 Volume de données modéré (< 1 GB)",
        "💻 Déploiement desktop local",
        "🔧 Simplicité de maintenance",
        "💰 Coût minimal (pas de serveur)",
        "⚡ Performance suffisante pour l'usage",
        "🔒 Intégrité des données garantie"
    ]
    
    for justification in justifications:
        print(f"   {justification}")
    
    print("\n🔄 MIGRATION FUTURE POSSIBLE VERS:")
    migrations = [
        "📈 PostgreSQL si croissance > 50 utilisateurs",
        "🌐 MySQL si passage en mode web/cloud",
        "📊 Solutions spécialisées si besoins analytics avancés"
    ]
    
    for migration in migrations:
        print(f"   {migration}")

def optimisations_sqlite():
    """Suggérer des optimisations pour SQLite"""
    print("\n⚡ OPTIMISATIONS SQLITE RECOMMANDÉES")
    print("=" * 60)
    
    optimisations = [
        {
            "category": "🔧 CONFIGURATION",
            "items": [
                "PRAGMA journal_mode = WAL; (Write-Ahead Logging)",
                "PRAGMA synchronous = NORMAL; (Performance/Sécurité)",
                "PRAGMA cache_size = 10000; (Cache mémoire)",
                "PRAGMA temp_store = MEMORY; (Stockage temporaire)",
                "PRAGMA foreign_keys = ON; (Intégrité référentielle)"
            ]
        },
        {
            "category": "📊 INDEX",
            "items": [
                "CREATE INDEX idx_produits_nom ON produits(nom);",
                "CREATE INDEX idx_produits_reference ON produits(reference);",
                "CREATE INDEX idx_produits_categorie ON produits(categorie_id);",
                "CREATE INDEX idx_ventes_date ON ventes(date_vente);",
                "CREATE INDEX idx_ventes_client ON ventes(client_id);"
            ]
        },
        {
            "category": "🚀 REQUÊTES",
            "items": [
                "Utiliser des requêtes préparées",
                "Éviter SELECT * quand possible",
                "Utiliser LIMIT pour les grandes listes",
                "Grouper les INSERT en transactions",
                "Utiliser des vues pour les requêtes complexes"
            ]
        },
        {
            "category": "💾 MAINTENANCE",
            "items": [
                "VACUUM régulier pour défragmenter",
                "ANALYZE pour mettre à jour les statistiques",
                "Sauvegarde automatique quotidienne",
                "Monitoring de la taille du fichier",
                "Archivage des anciennes données"
            ]
        }
    ]
    
    for opt in optimisations:
        print(f"\n{opt['category']}")
        print("-" * 40)
        for item in opt['items']:
            print(f"   • {item}")

def creer_script_optimisation():
    """Créer un script d'optimisation SQLite"""
    print("\n📝 CRÉATION DU SCRIPT D'OPTIMISATION")
    print("-" * 50)
    
    script_content = '''#!/usr/bin/env python3
"""
Script d'optimisation SQLite pour GesComPro_LibTam
"""

import sqlite3
import os
from database.db_manager import DatabaseManager

def optimiser_sqlite():
    """Appliquer les optimisations SQLite recommandées"""
    print("⚡ OPTIMISATION DE LA BASE DE DONNÉES SQLITE")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    
    if not db_manager.connect():
        print("❌ Impossible de se connecter à la base de données")
        return False
    
    try:
        cursor = db_manager.connection.cursor()
        
        # 1. Configuration optimale
        print("🔧 Application de la configuration optimale...")
        
        optimizations = [
            ("PRAGMA journal_mode = WAL", "Mode journal WAL activé"),
            ("PRAGMA synchronous = NORMAL", "Synchronisation normale"),
            ("PRAGMA cache_size = 10000", "Cache mémoire augmenté"),
            ("PRAGMA temp_store = MEMORY", "Stockage temporaire en mémoire"),
            ("PRAGMA foreign_keys = ON", "Clés étrangères activées")
        ]
        
        for pragma, description in optimizations:
            cursor.execute(pragma)
            print(f"   ✅ {description}")
        
        # 2. Création des index
        print("\\n📊 Création des index de performance...")
        
        indexes = [
            ("CREATE INDEX IF NOT EXISTS idx_produits_nom ON produits(nom)", "Index sur nom produit"),
            ("CREATE INDEX IF NOT EXISTS idx_produits_reference ON produits(reference)", "Index sur référence"),
            ("CREATE INDEX IF NOT EXISTS idx_produits_categorie ON produits(categorie_id)", "Index sur catégorie"),
            ("CREATE INDEX IF NOT EXISTS idx_ventes_date ON ventes(date_vente)", "Index sur date vente"),
            ("CREATE INDEX IF NOT EXISTS idx_ventes_client ON ventes(client_id)", "Index sur client"),
            ("CREATE INDEX IF NOT EXISTS idx_clients_nom ON clients(nom)", "Index sur nom client"),
            ("CREATE INDEX IF NOT EXISTS idx_categories_nom ON categories(nom)", "Index sur nom catégorie")
        ]
        
        for sql, description in indexes:
            try:
                cursor.execute(sql)
                print(f"   ✅ {description}")
            except sqlite3.Error as e:
                print(f"   ⚠️ {description}: {e}")
        
        # 3. Maintenance
        print("\\n💾 Maintenance de la base de données...")
        
        cursor.execute("VACUUM")
        print("   ✅ Défragmentation effectuée")
        
        cursor.execute("ANALYZE")
        print("   ✅ Statistiques mises à jour")
        
        db_manager.connection.commit()
        print("\\n🎉 Optimisation terminée avec succès!")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'optimisation: {e}")
        return False
    
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    optimiser_sqlite()
'''
    
    try:
        with open("optimiser_sqlite.py", "w", encoding="utf-8") as f:
            f.write(script_content)
        print("✅ Script d'optimisation créé: optimiser_sqlite.py")
    except Exception as e:
        print(f"❌ Erreur création script: {e}")

def main():
    """Fonction principale d'analyse"""
    print("🗄️ ANALYSE COMPLÈTE DE LA BASE DE DONNÉES")
    print("🎯 Application: GesComPro_LibTam")
    print("=" * 70)
    
    try:
        # 1. Analyser la base actuelle
        analyser_base_actuelle()
        
        # 2. Recommandations générales
        recommander_bases_donnees()
        
        # 3. Analyse des besoins spécifiques
        analyser_besoins_application()
        
        # 4. Optimisations SQLite
        optimisations_sqlite()
        
        # 5. Créer le script d'optimisation
        creer_script_optimisation()
        
        print("\n🎉 ANALYSE TERMINÉE")
        print("=" * 30)
        print("📋 Actions recommandées:")
        print("   1. Conserver SQLite pour cette application")
        print("   2. Exécuter le script d'optimisation: python optimiser_sqlite.py")
        print("   3. Implémenter les bonnes pratiques de requêtes")
        print("   4. Planifier des sauvegardes régulières")
        print("   5. Surveiller la croissance des données")
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()