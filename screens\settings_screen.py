"""
Écran des paramètres de l'application
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import MDText<PERSON>ield
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.list import MDList, OneLineAvatarIconListItem, IconLeftWidget, IconRightWidget
from kivymd.app import MDApp
from kivy.clock import Clock
import threading
import os
import json


class SettingCard(MDCard):
    """Carte pour un paramètre"""
    
    def __init__(self, title, description, widget, **kwargs):
        super().__init__(**kwargs)
        self.elevation = 1
        self.padding = "16dp"
        self.size_hint_y = None
        self.height = "100dp"
        
        layout = MDBoxLayout(orientation='horizontal', spacing="16dp")
        
        # Informations du paramètre
        info_layout = MDBoxLayout(orientation='vertical', size_hint_x=0.7)
        
        title_label = MDLabel(
            text=title,
            font_style="Subtitle1",
            theme_text_color="Primary"
        )
        
        desc_label = MDLabel(
            text=description,
            font_style="Caption",
            theme_text_color="Secondary"
        )
        
        info_layout.add_widget(title_label)
        info_layout.add_widget(desc_label)
        
        # Widget de contrôle
        control_layout = MDBoxLayout(size_hint_x=0.3)
        control_layout.add_widget(widget)
        
        layout.add_widget(info_layout)
        layout.add_widget(control_layout)
        
        self.add_widget(layout)


class CompanyInfoDialog(MDDialog):
    """Dialog pour modifier les informations de l'entreprise"""
    
    def __init__(self, company_info=None, on_save_callback=None, **kwargs):
        self.company_info = company_info or {}
        self.on_save_callback = on_save_callback
        
        # Création du formulaire
        form_layout = MDBoxLayout(orientation='vertical', spacing="16dp", adaptive_height=True)
        
        self.nom_field = MDTextField(
            hint_text="Nom de l'entreprise",
            text=self.company_info.get('nom', ''),
            required=True
        )
        
        self.adresse_field = MDTextField(
            hint_text="Adresse",
            text=self.company_info.get('adresse', ''),
            multiline=True,
            max_height="80dp"
        )
        
        self.telephone_field = MDTextField(
            hint_text="Téléphone",
            text=self.company_info.get('telephone', '')
        )
        
        self.email_field = MDTextField(
            hint_text="Email",
            text=self.company_info.get('email', '')
        )
        
        self.siret_field = MDTextField(
            hint_text="SIRET",
            text=self.company_info.get('siret', '')
        )
        
        self.tva_field = MDTextField(
            hint_text="Numéro de TVA",
            text=self.company_info.get('tva_numero', '')
        )
        
        # Ajout des champs au formulaire
        form_layout.add_widget(self.nom_field)
        form_layout.add_widget(self.adresse_field)
        form_layout.add_widget(self.telephone_field)
        form_layout.add_widget(self.email_field)
        form_layout.add_widget(self.siret_field)
        form_layout.add_widget(self.tva_field)
        
        # Boutons
        buttons = [
            MDFlatButton(
                text="ANNULER",
                on_release=self.dismiss
            ),
            MDRaisedButton(
                text="ENREGISTRER",
                on_release=self.save_info
            )
        ]
        
        super().__init__(
            title="Informations de l'entreprise",
            type="custom",
            content_cls=form_layout,
            buttons=buttons,
            size_hint=(0.9, None),
            height="500dp",
            **kwargs
        )
    
    def save_info(self, *args):
        """Enregistrer les informations"""
        company_data = {
            'nom': self.nom_field.text.strip(),
            'adresse': self.adresse_field.text.strip(),
            'telephone': self.telephone_field.text.strip(),
            'email': self.email_field.text.strip(),
            'siret': self.siret_field.text.strip(),
            'tva_numero': self.tva_field.text.strip()
        }
        
        if self.on_save_callback:
            self.on_save_callback(company_data)
        
        self.dismiss()


class BackupDialog(MDDialog):
    """Dialog pour la sauvegarde/restauration"""
    
    def __init__(self, on_backup_callback=None, on_restore_callback=None, **kwargs):
        self.on_backup_callback = on_backup_callback
        self.on_restore_callback = on_restore_callback
        
        # Création du contenu
        content_layout = MDBoxLayout(orientation='vertical', spacing="16dp", adaptive_height=True)
        
        # Section sauvegarde
        backup_label = MDLabel(
            text="Sauvegarde des données",
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        backup_desc = MDLabel(
            text="Créer une sauvegarde de toutes vos données (clients, produits, ventes, etc.)",
            font_style="Caption",
            theme_text_color="Secondary",
            text_size=(None, None)
        )
        
        backup_btn = MDRaisedButton(
            text="Créer une sauvegarde",
            icon="backup-restore",
            on_release=self.create_backup
        )
        
        # Section restauration
        restore_label = MDLabel(
            text="Restauration des données",
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        restore_desc = MDLabel(
            text="Restaurer les données à partir d'un fichier de sauvegarde",
            font_style="Caption",
            theme_text_color="Secondary",
            text_size=(None, None)
        )
        
        restore_btn = MDRaisedButton(
            text="Restaurer une sauvegarde",
            icon="restore",
            md_bg_color=(1, 0.3, 0.3, 1),  # Rouge
            on_release=self.restore_backup
        )
        
        # Ajout des éléments
        content_layout.add_widget(backup_label)
        content_layout.add_widget(backup_desc)
        content_layout.add_widget(backup_btn)
        content_layout.add_widget(MDLabel(size_hint_y=None, height="16dp"))  # Espacement
        content_layout.add_widget(restore_label)
        content_layout.add_widget(restore_desc)
        content_layout.add_widget(restore_btn)
        
        # Boutons
        buttons = [
            MDFlatButton(
                text="FERMER",
                on_release=self.dismiss
            )
        ]
        
        super().__init__(
            title="Sauvegarde et Restauration",
            type="custom",
            content_cls=content_layout,
            buttons=buttons,
            size_hint=(0.8, None),
            height="400dp",
            **kwargs
        )
    
    def create_backup(self, *args):
        """Créer une sauvegarde"""
        if self.on_backup_callback:
            self.on_backup_callback()
        self.dismiss()
    
    def restore_backup(self, *args):
        """Restaurer une sauvegarde"""
        if self.on_restore_callback:
            self.on_restore_callback()
        self.dismiss()


class SettingsScreen(MDScreen):
    """Écran des paramètres"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.settings_data = {}
        self.build_ui()
    
    def build_ui(self):
        """Construction de l'interface utilisateur"""
        main_layout = MDBoxLayout(orientation='vertical', padding="16dp", spacing="16dp")
        
        # Titre
        title_label = MDLabel(
            text="Paramètres",
            font_style="H5",
            theme_text_color="Primary",
            size_hint_y=None,
            height="48dp"
        )
        main_layout.add_widget(title_label)
        
        # ScrollView pour les paramètres
        scroll = MDScrollView()
        content_layout = MDBoxLayout(orientation='vertical', spacing="16dp", adaptive_height=True)
        
        # Section Entreprise
        self.create_company_section(content_layout)
        
        # Section Application
        self.create_app_section(content_layout)
        
        # Section Données
        self.create_data_section(content_layout)
        
        # Section À propos
        self.create_about_section(content_layout)
        
        scroll.add_widget(content_layout)
        main_layout.add_widget(scroll)
        
        self.add_widget(main_layout)
    
    def create_company_section(self, parent_layout):
        """Créer la section des paramètres de l'entreprise"""
        section_label = MDLabel(
            text="Informations de l'entreprise",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        parent_layout.add_widget(section_label)
        
        # Bouton pour modifier les informations
        company_btn = MDRaisedButton(
            text="Modifier les informations",
            icon="office-building",
            on_release=self.edit_company_info
        )
        
        company_card = SettingCard(
            "Informations de l'entreprise",
            "Nom, adresse, téléphone, email, SIRET, etc.",
            company_btn
        )
        parent_layout.add_widget(company_card)
        
        # TVA par défaut
        self.tva_field = MDTextField(
            hint_text="TVA par défaut (%)",
            text="20.0",
            input_filter="float",
            size_hint_x=None,
            width="100dp"
        )
        
        tva_card = SettingCard(
            "TVA par défaut",
            "Taux de TVA appliqué par défaut aux nouveaux produits",
            self.tva_field
        )
        parent_layout.add_widget(tva_card)
    
    def create_app_section(self, parent_layout):
        """Créer la section des paramètres de l'application"""
        section_label = MDLabel(
            text="Paramètres de l'application",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        parent_layout.add_widget(section_label)
        
        # Thème sombre
        self.dark_theme_switch = MDCheckbox(
            active=MDApp.get_running_app().theme_cls.theme_style == "Dark",
            size_hint=(None, None),
            size=("48dp", "48dp")
        )
        self.dark_theme_switch.bind(active=self.toggle_theme)
        
        theme_card = SettingCard(
            "Thème sombre",
            "Activer le mode sombre de l'application",
            self.dark_theme_switch
        )
        parent_layout.add_widget(theme_card)
        
        # Notifications
        self.notifications_switch = MDCheckbox(
            active=True,
            size_hint=(None, None),
            size=("48dp", "48dp")
        )
        
        notif_card = SettingCard(
            "Notifications",
            "Recevoir des notifications pour les stocks bas, etc.",
            self.notifications_switch
        )
        parent_layout.add_widget(notif_card)
        
        # Devise
        self.devise_field = MDTextField(
            hint_text="Devise",
            text="MAD",
            size_hint_x=None,
            width="80dp"
        )
        
        devise_card = SettingCard(
            "Devise",
            "Devise utilisée dans l'application",
            self.devise_field
        )
        parent_layout.add_widget(devise_card)
    
    def create_data_section(self, parent_layout):
        """Créer la section de gestion des données"""
        section_label = MDLabel(
            text="Gestion des données",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        parent_layout.add_widget(section_label)
        
        # Sauvegarde/Restauration
        backup_btn = MDRaisedButton(
            text="Sauvegarde/Restauration",
            icon="backup-restore",
            on_release=self.open_backup_dialog
        )
        
        backup_card = SettingCard(
            "Sauvegarde et restauration",
            "Sauvegarder ou restaurer vos données",
            backup_btn
        )
        parent_layout.add_widget(backup_card)
        
        # Export des données
        export_btn = MDRaisedButton(
            text="Exporter",
            icon="export",
            on_release=self.export_data
        )
        
        export_card = SettingCard(
            "Export des données",
            "Exporter les données vers Excel/CSV",
            export_btn
        )
        parent_layout.add_widget(export_card)
        
        # Nettoyage des données
        cleanup_btn = MDRaisedButton(
            text="Nettoyer",
            icon="delete-sweep",
            md_bg_color=(1, 0.3, 0.3, 1),  # Rouge
            on_release=self.cleanup_data
        )
        
        cleanup_card = SettingCard(
            "Nettoyage des données",
            "Supprimer les données anciennes ou inutiles",
            cleanup_btn
        )
        parent_layout.add_widget(cleanup_card)
    
    def create_about_section(self, parent_layout):
        """Créer la section À propos"""
        section_label = MDLabel(
            text="À propos",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        parent_layout.add_widget(section_label)
        
        # Informations sur l'application
        about_card = MDCard(
            elevation=1,
            padding="16dp",
            size_hint_y=None,
            height="140dp"
        )
        
        about_layout = MDBoxLayout(orientation='vertical', spacing="4dp")
        
        app_name_label = MDLabel(
            text="GesComPro_LibTam - Gestion Commerciale",
            font_style="Subtitle1",
            theme_text_color="Primary"
        )
        
        version_label = MDLabel(
            text="Version 1.0.0",
            font_style="Caption",
            theme_text_color="Secondary"
        )
        
        powered_by_label = MDLabel(
            text="Powered by: LKAIHAL LAHCEN_AIA",
            font_style="Caption",
            theme_text_color="Secondary",
            bold=True
        )
        
        description_label = MDLabel(
            text="Application de gestion commerciale développée avec Python et Kivy",
            font_style="Caption",
            theme_text_color="Secondary"
        )
        
        about_layout.add_widget(app_name_label)
        about_layout.add_widget(version_label)
        about_layout.add_widget(powered_by_label)
        about_layout.add_widget(description_label)
        
        about_card.add_widget(about_layout)
        parent_layout.add_widget(about_card)
    
    def on_enter(self):
        """Actions à effectuer lors de l'entrée sur l'écran"""
        self.load_settings()
    
    def load_settings(self):
        """Charger les paramètres depuis la base de données"""
        def load_data():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                # Récupérer tous les paramètres
                settings = db_manager.execute_query("SELECT * FROM parametres")
                
                self.settings_data = {}
                for setting in settings:
                    self.settings_data[setting['cle']] = setting['valeur']
                
                # Mettre à jour l'interface
                Clock.schedule_once(self.update_settings_ui, 0)
                
            except Exception as e:
                print(f"Erreur lors du chargement des paramètres: {e}")
        
        threading.Thread(target=load_data, daemon=True).start()
    
    def update_settings_ui(self, dt):
        """Mettre à jour l'interface avec les paramètres chargés"""
        # Mettre à jour les champs avec les valeurs de la base de données
        self.tva_field.text = self.settings_data.get('tva_defaut', '20.0')
        self.devise_field.text = self.settings_data.get('devise', 'MAD')
    
    def toggle_theme(self, instance, value):
        """Basculer le thème de l'application"""
        app = MDApp.get_running_app()
        if value:
            app.theme_cls.theme_style = "Dark"
        else:
            app.theme_cls.theme_style = "Light"
        
        # Sauvegarder le paramètre
        self.save_setting('theme_style', 'Dark' if value else 'Light')
    
    def save_setting(self, key, value):
        """Sauvegarder un paramètre"""
        def save_data():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                # Insérer ou mettre à jour le paramètre
                query = """
                    INSERT OR REPLACE INTO parametres (cle, valeur, date_modification)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                """
                db_manager.execute_update(query, (key, value))
                
            except Exception as e:
                print(f"Erreur lors de la sauvegarde du paramètre: {e}")
        
        threading.Thread(target=save_data, daemon=True).start()
    
    def edit_company_info(self, *args):
        """Modifier les informations de l'entreprise"""
        company_info = {
            'nom': self.settings_data.get('entreprise_nom', ''),
            'adresse': self.settings_data.get('entreprise_adresse', ''),
            'telephone': self.settings_data.get('entreprise_telephone', ''),
            'email': self.settings_data.get('entreprise_email', ''),
            'siret': self.settings_data.get('entreprise_siret', ''),
            'tva_numero': self.settings_data.get('entreprise_tva_numero', '')
        }
        
        dialog = CompanyInfoDialog(
            company_info=company_info,
            on_save_callback=self.save_company_info
        )
        dialog.open()
    
    def save_company_info(self, company_data):
        """Sauvegarder les informations de l'entreprise"""
        def save_data():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                # Sauvegarder chaque information
                settings_map = {
                    'entreprise_nom': company_data['nom'],
                    'entreprise_adresse': company_data['adresse'],
                    'entreprise_telephone': company_data['telephone'],
                    'entreprise_email': company_data['email'],
                    'entreprise_siret': company_data['siret'],
                    'entreprise_tva_numero': company_data['tva_numero']
                }
                
                for key, value in settings_map.items():
                    query = """
                        INSERT OR REPLACE INTO parametres (cle, valeur, date_modification)
                        VALUES (?, ?, CURRENT_TIMESTAMP)
                    """
                    db_manager.execute_update(query, (key, value))
                
                # Recharger les paramètres
                Clock.schedule_once(lambda dt: self.load_settings(), 0)
                
            except Exception as e:
                print(f"Erreur lors de la sauvegarde des informations: {e}")
        
        threading.Thread(target=save_data, daemon=True).start()
    
    def open_backup_dialog(self, *args):
        """Ouvrir le dialog de sauvegarde/restauration"""
        dialog = BackupDialog(
            on_backup_callback=self.create_backup,
            on_restore_callback=self.restore_backup
        )
        dialog.open()
    
    def create_backup(self):
        """Créer une sauvegarde"""
        # TODO: Implémenter la création de sauvegarde
        print("Création de sauvegarde...")
        
        # Afficher un message de confirmation
        confirm_dialog = MDDialog(
            title="Sauvegarde créée",
            text="La sauvegarde a été créée avec succès.",
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: confirm_dialog.dismiss()
                )
            ]
        )
        confirm_dialog.open()
    
    def restore_backup(self):
        """Restaurer une sauvegarde"""
        # TODO: Implémenter la restauration de sauvegarde
        print("Restauration de sauvegarde...")
        
        # Dialog de confirmation
        confirm_dialog = MDDialog(
            title="Attention",
            text="La restauration remplacera toutes vos données actuelles. Êtes-vous sûr ?",
            buttons=[
                MDFlatButton(
                    text="ANNULER",
                    on_release=lambda x: confirm_dialog.dismiss()
                ),
                MDRaisedButton(
                    text="RESTAURER",
                    md_bg_color=(1, 0.3, 0.3, 1),  # Rouge
                    on_release=lambda x: (self.perform_restore(), confirm_dialog.dismiss())
                )
            ]
        )
        confirm_dialog.open()
    
    def perform_restore(self):
        """Effectuer la restauration"""
        # TODO: Implémenter la restauration
        print("Restauration effectuée")
    
    def export_data(self, *args):
        """Exporter les données"""
        # TODO: Implémenter l'export des données
        print("Export des données...")
        
        # Afficher un message de confirmation
        confirm_dialog = MDDialog(
            title="Export terminé",
            text="Les données ont été exportées avec succès.",
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: confirm_dialog.dismiss()
                )
            ]
        )
        confirm_dialog.open()
    
    def cleanup_data(self, *args):
        """Nettoyer les données"""
        # Dialog de confirmation
        confirm_dialog = MDDialog(
            title="Nettoyage des données",
            text="Cette action supprimera définitivement les données anciennes. Continuer ?",
            buttons=[
                MDFlatButton(
                    text="ANNULER",
                    on_release=lambda x: confirm_dialog.dismiss()
                ),
                MDRaisedButton(
                    text="NETTOYER",
                    md_bg_color=(1, 0.3, 0.3, 1),  # Rouge
                    on_release=lambda x: (self.perform_cleanup(), confirm_dialog.dismiss())
                )
            ]
        )
        confirm_dialog.open()
    
    def perform_cleanup(self):
        """Effectuer le nettoyage"""
        # TODO: Implémenter le nettoyage des données
        print("Nettoyage effectué")