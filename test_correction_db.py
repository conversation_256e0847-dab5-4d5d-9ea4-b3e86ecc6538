#!/usr/bin/env python3
"""
Test des corrections de la base de données
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager, create_sale, update_sale_status, get_all_sales, get_sale_by_id

def test_database_corrections():
    """Tester les corrections de la base de données"""
    print("🔧 TEST DES CORRECTIONS BASE DE DONNÉES")
    print("=" * 50)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    
    try:
        # Test 1: Connexion
        print("1. Test de connexion...")
        if db_manager.connect():
            print("✅ Connexion réussie")
        else:
            print("❌ Échec de connexion")
            return
        
        # Test 2: Initialisation
        print("2. Test d'initialisation...")
        if db_manager.initialize_database():
            print("✅ Initialisation réussie")
        else:
            print("❌ Échec d'initialisation")
            return
        
        # Test 3: Vérifier que la connexion reste active
        print("3. Test de persistance de connexion...")
        if db_manager.connection:
            print("✅ Connexion active")
        else:
            print("❌ Connexion fermée")
            return
        
        # Test 4: Créer des données de test
        print("4. Création de données de test...")
        
        # Ajouter un client de test
        from database.db_manager import add_client, add_product
        
        client_data = {
            'nom': 'Test',
            'prenom': 'Client',
            'email': '<EMAIL>',
            'telephone': '0123456789',
            'actif': True
        }
        
        try:
            client_id = add_client(db_manager, client_data)
            print(f"✅ Client créé (ID: {client_id})")
        except Exception as e:
            print(f"⚠️ Client déjà existant: {e}")
            # Récupérer l'ID du client existant
            clients = db_manager.execute_query("SELECT id FROM clients WHERE email = ?", ('<EMAIL>',))
            client_id = clients[0]['id'] if clients else 1
        
        # Ajouter un produit de test
        product_data = {
            'nom': 'Produit Test',
            'description': 'Produit pour test',
            'reference': 'TEST-001',
            'prix_achat': 50.0,
            'prix_vente': 100.0,
            'stock_actuel': 10,
            'stock_minimum': 2,
            'tva': 20.0,
            'actif': True
        }
        
        try:
            product_id = add_product(db_manager, product_data)
            print(f"✅ Produit créé (ID: {product_id})")
        except Exception as e:
            print(f"⚠️ Produit déjà existant: {e}")
            # Récupérer l'ID du produit existant
            products = db_manager.execute_query("SELECT id FROM produits WHERE reference = ?", ('TEST-001',))
            product_id = products[0]['id'] if products else 1
        
        # Test 5: Créer une vente
        print("5. Test de création de vente...")
        
        vente_data = {
            'client_id': client_id,
            'mode_paiement': 'Test',
            'notes': 'Vente de test pour correction DB',
            'statut': 'En cours'
        }
        
        sale_items = [
            {
                'product': {'id': product_id, 'tva': 20.0},
                'quantite': 2,
                'prix_unitaire': 100.0
            }
        ]
        
        vente_id = create_sale(db_manager, vente_data, sale_items)
        
        if vente_id:
            print(f"✅ Vente créée (ID: {vente_id})")
        else:
            print("❌ Échec création vente")
            return
        
        # Test 6: Vérifier que la connexion est toujours active
        print("6. Test de connexion après création...")
        if db_manager.connection:
            print("✅ Connexion toujours active")
        else:
            print("❌ Connexion fermée après création")
            return
        
        # Test 7: Récupérer la vente
        print("7. Test de récupération de vente...")
        sale_details = get_sale_by_id(db_manager, vente_id)
        
        if sale_details:
            print(f"✅ Vente récupérée: {sale_details.get('numero_facture')}")
        else:
            print("❌ Échec récupération vente")
            return
        
        # Test 8: Vérifier la connexion après récupération
        print("8. Test de connexion après récupération...")
        if db_manager.connection:
            print("✅ Connexion toujours active")
        else:
            print("❌ Connexion fermée après récupération")
            return
        
        # Test 9: Mettre à jour le statut
        print("9. Test de mise à jour de statut...")
        success = update_sale_status(db_manager, vente_id, 'Payée')
        
        if success:
            print("✅ Statut mis à jour avec succès")
        else:
            print("❌ Échec mise à jour statut")
            return
        
        # Test 10: Vérifier la connexion après mise à jour
        print("10. Test de connexion après mise à jour...")
        if db_manager.connection:
            print("✅ Connexion toujours active")
        else:
            print("❌ Connexion fermée après mise à jour")
            return
        
        # Test 11: Test d'annulation avec restauration de stock
        print("11. Test d'annulation avec restauration de stock...")
        
        # Vérifier le stock avant annulation
        stock_avant = db_manager.execute_query("SELECT stock_actuel FROM produits WHERE id = ?", (product_id,))
        stock_avant_val = stock_avant[0]['stock_actuel'] if stock_avant else 0
        print(f"   Stock avant annulation: {stock_avant_val}")
        
        # Annuler la vente
        success = update_sale_status(db_manager, vente_id, 'Annulée')
        
        if success:
            print("✅ Vente annulée avec succès")
            
            # Vérifier le stock après annulation (devrait être restauré)
            stock_apres = db_manager.execute_query("SELECT stock_actuel FROM produits WHERE id = ?", (product_id,))
            stock_apres_val = stock_apres[0]['stock_actuel'] if stock_apres else 0
            print(f"   Stock après annulation: {stock_apres_val}")
            
            if stock_apres_val > stock_avant_val:
                print("✅ Stock restauré correctement")
            else:
                print("⚠️ Stock non restauré")
        else:
            print("❌ Échec annulation vente")
        
        # Test 12: Test final de connexion
        print("12. Test final de connexion...")
        if db_manager.connection:
            print("✅ Connexion finale active")
        else:
            print("❌ Connexion finale fermée")
        
        print("\n🎉 TOUS LES TESTS TERMINÉS")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Fermer proprement la connexion
        if db_manager.connection:
            db_manager.disconnect()
            print("🔒 Connexion fermée proprement")

if __name__ == "__main__":
    test_database_corrections()