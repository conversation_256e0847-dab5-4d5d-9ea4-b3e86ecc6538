#!/usr/bin/env python3
"""
Script pour ajouter uniquement des ventes de test avec numéros uniques
"""

import os
import sys
from datetime import datetime, timedelta
import random
import time

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager, create_sale

def ajouter_ventes_test():
    """Ajouter des ventes de test avec numéros uniques"""
    print("🛒 AJOUT DE VENTES DE TEST")
    print("=" * 40)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    
    if not db_manager.connect():
        print("❌ Impossible de se connecter à la base de données")
        return
    
    try:
        # Récupérer les clients existants
        clients = db_manager.execute_query("SELECT id FROM clients WHERE actif = 1")
        if not clients:
            print("❌ Aucun client trouvé. Ajoutez d'abord des clients.")
            return
        
        clients_ids = [client['id'] for client in clients]
        print(f"✅ {len(clients_ids)} clients trouvés")
        
        # Récupérer les produits existants
        produits = db_manager.execute_query("SELECT id, nom, prix_vente, tva FROM produits WHERE actif = 1")
        if not produits:
            print("❌ Aucun produit trouvé. Ajoutez d'abord des produits.")
            return
        
        print(f"✅ {len(produits)} produits trouvés")
        
        # Modes de paiement et statuts
        modes_paiement = ['Espèces', 'Carte bancaire', 'Chèque', 'Virement bancaire', 'Électronique']
        statuts = ['En cours', 'Payée', 'Annulée']
        
        ventes_creees = 0
        
        # Créer 15 ventes de test
        for i in range(15):
            try:
                # Attendre un peu pour éviter les conflits de timestamp
                time.sleep(0.1)
                
                # Sélectionner un client aléatoire
                client_id = random.choice(clients_ids)
                
                # Sélectionner un mode de paiement aléatoire
                mode_paiement = random.choice(modes_paiement)
                
                # Statut aléatoire (plus de chances d'être payée)
                statut = random.choices(statuts, weights=[25, 65, 10])[0]  # 65% payée, 25% en cours, 10% annulée
                
                vente_data = {
                    'client_id': client_id,
                    'mode_paiement': mode_paiement,
                    'notes': f'Vente de test automatique #{i+1} - {datetime.now().strftime("%d/%m/%Y %H:%M")}',
                    'statut': statut
                }
                
                # Sélectionner 1 à 3 produits aléatoires
                nb_produits = random.randint(1, 3)
                produits_selectionnes = random.sample(produits, min(nb_produits, len(produits)))
                
                sale_items = []
                total_vente = 0
                
                for produit in produits_selectionnes:
                    quantite = random.randint(1, 2)  # 1 ou 2 pour éviter les gros stocks
                    prix_unitaire = produit['prix_vente']
                    
                    sale_items.append({
                        'product': {
                            'id': produit['id'],
                            'tva': produit['tva']
                        },
                        'quantite': quantite,
                        'prix_unitaire': prix_unitaire
                    })
                    
                    total_vente += quantite * prix_unitaire * 1.2  # Avec TVA approximative
                
                # Créer la vente
                vente_id = create_sale(db_manager, vente_data, sale_items)
                
                if vente_id:
                    ventes_creees += 1
                    produits_noms = [p['nom'] for p in produits_selectionnes]
                    print(f"✅ Vente {ventes_creees}: {total_vente:.0f} DH - {mode_paiement} - {statut}")
                    print(f"   Produits: {', '.join(produits_noms[:2])}{'...' if len(produits_noms) > 2 else ''}")
                else:
                    print(f"❌ Échec création vente {i+1}")
                    
            except Exception as e:
                print(f"❌ Erreur vente {i+1}: {e}")
                # Continuer avec la suivante
                continue
        
        print(f"\n📊 RÉSULTAT: {ventes_creees} ventes créées avec succès")
        
        # Afficher les statistiques finales
        ventes_stats = db_manager.execute_query("""
            SELECT 
                COUNT(*) as total_ventes,
                SUM(CASE WHEN statut = 'En cours' THEN 1 ELSE 0 END) as en_cours,
                SUM(CASE WHEN statut = 'Payée' THEN 1 ELSE 0 END) as payees,
                SUM(CASE WHEN statut = 'Annulée' THEN 1 ELSE 0 END) as annulees,
                SUM(montant_ttc) as ca_total
            FROM ventes
        """)
        
        if ventes_stats:
            stats = ventes_stats[0]
            print(f"\n📈 STATISTIQUES TOTALES:")
            print(f"🛒 Total ventes: {stats['total_ventes']}")
            print(f"   • 🔄 En cours: {stats['en_cours']}")
            print(f"   • ✅ Payées: {stats['payees']}")
            print(f"   • ❌ Annulées: {stats['annulees']}")
            print(f"💰 Chiffre d'affaires: {stats['ca_total']:.2f} DH")
        
        print("\n🎉 VENTES DE TEST AJOUTÉES AVEC SUCCÈS !")
        print("🚀 Testez maintenant avec: python launch_simple.py")
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if db_manager.connection:
            db_manager.disconnect()

if __name__ == "__main__":
    ajouter_ventes_test()