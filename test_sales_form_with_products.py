#!/usr/bin/env python3
"""
Test du formulaire de vente avec sélection de produits
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

# Configuration pour Windows
if sys.platform == 'win32':
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel

# Import du formulaire avec produits
from forms.sales_form import SalesFormDialog


class TestSalesFormWithProductsApp(MDApp):
    """Test du formulaire de vente avec produits"""
    
    def build(self):
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        title = MDLabel(
            text="🛍️ Test Formulaire de Vente\navec Sélection de Produits",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Test formulaire avec produits
        products_btn = MDRaisedButton(
            text="🛍️ Formulaire avec Produits",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_products_form
        )
        
        self.result_label = MDLabel(
            text="Test du formulaire de vente avec produits\n\n" \
                 "Fonctionnalités à tester :\n" \
                 "• Section Client (liste déroulante)\n" \
                 "• Section Produits (sélection + quantités)\n" \
                 "• Section Montants (calculés automatiquement)\n" \
                 "• Section Paiement\n" \
                 "• Section Notes\n" \
                 "• Sauvegarde avec produits",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(products_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_products_form(self, *args):
        """Tester le formulaire avec produits"""
        try:
            dialog = SalesFormDialog(
                on_save_callback=self.on_save
            )
            dialog.open()
            
            self.result_label.text = "🛍️ Formulaire avec produits ouvert !\n\n" \
                                   "VÉRIFICATIONS :\n" \
                                   "✅ Section Client : Liste déroulante\n" \
                                   "✅ Section Produits : Bouton + pour ajouter\n" \
                                   "✅ Sélection produits : Menu déroulant\n" \
                                   "✅ Gestion quantités : +/- et champ\n" \
                                   "✅ Calcul automatique : Montants HT/TTC\n" \
                                   "✅ Suppression produits : Bouton poubelle\n" \
                                   "✅ Validation : Client + produits obligatoires\n\n" \
                                   "Testez l'ajout de produits et la sauvegarde !"
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur formulaire avec produits :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def on_save(self, data):
        """Callback de test"""
        client_info = "Non sélectionné"
        if 'client_id' in data:
            client_info = f"ID {data['client_id']}"
        
        products_info = "Aucun"
        if 'produits' in data and data['produits']:
            products_info = f"{len(data['produits'])} produit(s)"
            
        montant_info = "0.00 DH"
        if 'montant_ttc' in data:
            montant_info = f"{data['montant_ttc']:.2f} DH"
        
        facture_info = data.get('numero_facture', 'N/A')
        
        self.result_label.text = f"🎉 Vente sauvegardée avec succès !\n\n" \
                               f"📄 Facture : {facture_info}\n" \
                               f"👤 Client : {client_info}\n" \
                               f"🛍️ Produits : {products_info}\n" \
                               f"💰 Montant : {montant_info}\n\n" \
                               f"Le formulaire avec produits fonctionne parfaitement !"


def main():
    print("🛍️ Test Formulaire de Vente avec Produits")
    print("OBJECTIF: Vérifier la sélection de produits et le calcul automatique")
    
    try:
        app = TestSalesFormWithProductsApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()