#!/usr/bin/env python3
"""
Test complet de l'écran de vente avec formulaire amélioré
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from sales_screen_improved import ImprovedSalesScreen


class TestSalesScreenCompleteApp(MDApp):
    """Application de test pour l'écran de vente complet"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Écran de Vente Complet"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construction de l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        # Titre
        title = MDLabel(
            text="🛒 Écran de Vente Complet",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Instructions
        instructions = MDLabel(
            text="ÉCRAN DE VENTE AVEC FORMULAIRE AMÉLIORÉ :\n\n"
                 "✅ Écran principal avec liste des ventes\n"
                 "✅ Cartes d'affichage optimisées\n"
                 "✅ Formulaire avec listes déroulantes intégré\n"
                 "✅ Champ client : Sélection dans la liste\n"
                 "✅ Champ paiement : Modes avec icônes\n"
                 "✅ Validation automatique\n"
                 "✅ Gestion complète CRUD\n\n"
                 "FONCTIONNALITÉS DISPONIBLES :\n"
                 "➕ Créer une nouvelle vente\n"
                 "✏️ Modifier une vente existante\n"
                 "🗑️ Supprimer une vente\n"
                 "🔄 Actualiser la liste\n"
                 "👤 Sélection client automatique\n"
                 "💳 Modes de paiement complets",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="320dp"
        )
        
        # Bouton de test
        test_btn = MDRaisedButton(
            text="🚀 Ouvrir l'Écran de Vente Complet",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_sales_screen
        )
        
        # Résultats
        self.result_label = MDLabel(
            text="Prêt pour le test de l'écran de vente complet !",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(instructions)
        layout.add_widget(test_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_sales_screen(self, *args):
        """Tester l'écran complet des ventes"""
        self.result_label.text = "🚀 ÉCRAN DE VENTE COMPLET OUVERT !\n\n" \
                                "TESTEZ MAINTENANT :\n" \
                                "➕ Créer une nouvelle vente\n" \
                                "   → Sélectionner un client dans la liste\n" \
                                "   → Choisir un mode de paiement\n" \
                                "   → Ajouter des produits\n" \
                                "   → Enregistrer la vente\n\n" \
                                "✏️ Modifier une vente existante\n" \
                                "🗑️ Supprimer une vente\n" \
                                "🔄 Actualiser la liste\n\n" \
                                "Le formulaire avec listes déroulantes\n" \
                                "est maintenant intégré !"
        
        # Créer et afficher l'écran des ventes
        sales_screen = ImprovedSalesScreen()
        
        # Remplacer l'écran actuel
        self.root.clear_widgets()
        self.root.add_widget(sales_screen)
        
        print("🚀 Écran de vente complet avec formulaire amélioré ouvert")
        print("✅ Intégration réussie !")


def main():
    """Fonction principale"""
    print("🛒 Test - Écran de Vente Complet")
    print("=" * 60)
    print("ÉCRAN DE VENTE AVEC FORMULAIRE AMÉLIORÉ :")
    print()
    print("1. ÉCRAN PRINCIPAL :")
    print("   ✅ Liste des ventes avec cartes optimisées")
    print("   ✅ Barre d'outils avec actions (Créer, Actualiser)")
    print("   ✅ Affichage des informations complètes")
    print("   ✅ Statuts colorés (En cours, Payée, Annulée)")
    print()
    print("2. FORMULAIRE INTÉGRÉ :")
    print("   ✅ Champ client : Liste déroulante des clients")
    print("   ✅ Champ paiement : Modes avec icônes")
    print("   ✅ Validation automatique des champs obligatoires")
    print("   ✅ Couleurs distinctives pour les listes")
    print("   ✅ Sauvegarde directe dans la table ventes")
    print()
    print("3. FONCTIONNALITÉS CRUD :")
    print("   ✅ Créer une nouvelle vente")
    print("   ✅ Modifier une vente existante")
    print("   ✅ Supprimer avec confirmation")
    print("   ✅ Actualiser la liste")
    print()
    print("4. STRUCTURE DE LA TABLE VENTES :")
    print("   - id INTEGER PRIMARY KEY AUTOINCREMENT")
    print("   - numero_facture TEXT UNIQUE NOT NULL")
    print("   - client_id INTEGER REFERENCES clients(id)")
    print("   - date_vente TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    print("   - montant_ht DECIMAL(10,2) NOT NULL")
    print("   - montant_ttc DECIMAL(10,2) NOT NULL")
    print("   - mode_paiement TEXT NOT NULL")
    print("   - statut TEXT DEFAULT 'En cours'")
    print("   - notes TEXT")
    print()
    print("5. MODES DE PAIEMENT DISPONIBLES :")
    print("   💰 Espèces")
    print("   💳 Carte bancaire")
    print("   📄 Chèque")
    print("   🏦 Virement bancaire")
    print("   📱 Paiement électronique")
    print("   💸 Crédit")
    print("   🔄 Paiement échelonné")
    print("   🎁 Bon cadeau")
    print("   🤝 Compensation")
    print("   ❓ Autre")
    print()
    print("🎯 OBJECTIF ATTEINT : Champs client et paiement en listes déroulantes !")
    print("=" * 60)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestSalesScreenCompleteApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()