"""
Écran du tableau de bord principal
"""

from kivy.clock import Clock
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MD<PERSON>abel
from kivymd.uix.button import MDRaisedButton, MDIconButton
from kivymd.uix.scrollview import MDScrollView
from kivymd.app import MDApp
from datetime import datetime, timedelta
import threading


class StatCard(MDCard):
    """Carte pour afficher une statistique"""
    
    def __init__(self, title, value, icon, color="primary", **kwargs):
        super().__init__(**kwargs)
        self.elevation = 2
        self.padding = "16dp"
        self.size_hint_y = None
        self.height = "120dp"
        self.md_bg_color = MDApp.get_running_app().theme_cls.primary_color if color == "primary" else color
        
        layout = MDBoxLayout(orientation='vertical', spacing="8dp")
        
        # Titre et icône
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="32dp")
        
        title_label = MDLabel(
            text=title,
            theme_text_color="Custom",
            text_color=(1, 1, 1, 1),
            font_style="Subtitle1",
            size_hint_x=0.8
        )
        
        icon_button = MDIconButton(
            icon=icon,
            theme_icon_color="Custom",
            icon_color=(1, 1, 1, 1),
            size_hint_x=0.2
        )
        
        header_layout.add_widget(title_label)
        header_layout.add_widget(icon_button)
        
        # Valeur
        value_label = MDLabel(
            text=str(value),
            theme_text_color="Custom",
            text_color=(1, 1, 1, 1),
            font_style="H4",
            halign="center"
        )
        
        layout.add_widget(header_layout)
        layout.add_widget(value_label)
        self.add_widget(layout)


class QuickActionCard(MDCard):
    """Carte pour les actions rapides"""
    
    def __init__(self, title, icon, action_callback, **kwargs):
        super().__init__(**kwargs)
        self.elevation = 1
        self.padding = "16dp"
        self.size_hint_y = None
        self.height = "100dp"
        self.ripple_behavior = True
        self.on_release = action_callback
        
        layout = MDBoxLayout(orientation='vertical', spacing="8dp")
        
        # Icône
        icon_button = MDIconButton(
            icon=icon,
            theme_icon_color="Primary",
            size_hint_y=0.6
        )
        
        # Titre
        title_label = MDLabel(
            text=title,
            theme_text_color="Primary",
            font_style="Caption",
            halign="center",
            size_hint_y=0.4
        )
        
        layout.add_widget(icon_button)
        layout.add_widget(title_label)
        self.add_widget(layout)


class DashboardScreen(MDScreen):
    """Écran principal du tableau de bord"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.stats_data = {
            'ventes_jour': 0,
            'ca_mois': 0,
            'clients_total': 0,
            'produits_stock_bas': 0
        }
        self.build_ui()
        
    def build_ui(self):
        """Construction de l'interface utilisateur"""
        main_layout = MDBoxLayout(orientation='vertical', padding="16dp", spacing="16dp")
        
        # Titre
        title_label = MDLabel(
            text="GesComPro_LibTam - Tableau de Bord",
            font_style="H5",
            theme_text_color="Primary",
            size_hint_y=None,
            height="48dp"
        )
        main_layout.add_widget(title_label)
        
        # Sous-titre avec crédit
        subtitle_label = MDLabel(
            text="Powered by: LKAIHAL LAHCEN_AIA",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="24dp"
        )
        main_layout.add_widget(subtitle_label)
        
        # ScrollView pour le contenu
        scroll = MDScrollView()
        content_layout = MDBoxLayout(orientation='vertical', spacing="16dp", adaptive_height=True)
        
        # Section des statistiques
        stats_section = self.create_stats_section()
        content_layout.add_widget(stats_section)
        
        # Section des actions rapides
        actions_section = self.create_quick_actions_section()
        content_layout.add_widget(actions_section)
        
        # Section des alertes
        alerts_section = self.create_alerts_section()
        content_layout.add_widget(alerts_section)
        
        scroll.add_widget(content_layout)
        main_layout.add_widget(scroll)
        
        self.add_widget(main_layout)
    
    def create_stats_section(self):
        """Créer la section des statistiques"""
        section_layout = MDBoxLayout(orientation='vertical', spacing="8dp", adaptive_height=True)
        
        # Titre de section
        section_title = MDLabel(
            text="Statistiques du jour",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        section_layout.add_widget(section_title)
        
        # Grille des cartes statistiques
        stats_grid = MDGridLayout(cols=2, spacing="8dp", adaptive_height=True)
        
        # Cartes de statistiques
        self.ventes_card = StatCard(
            "Ventes du jour",
            self.stats_data['ventes_jour'],
            "cart",
            color=MDApp.get_running_app().theme_cls.primary_color
        )
        
        self.ca_card = StatCard(
            "CA du mois",
            f"{self.stats_data['ca_mois']:.2f} DH",
            "currency-mad",
            color=MDApp.get_running_app().theme_cls.accent_color
        )
        
        self.clients_card = StatCard(
            "Clients total",
            self.stats_data['clients_total'],
            "account-group",
            color=(0.2, 0.7, 0.3, 1)
        )
        
        self.stock_card = StatCard(
            "Stock bas",
            self.stats_data['produits_stock_bas'],
            "alert-circle",
            color=(0.9, 0.4, 0.2, 1)
        )
        
        stats_grid.add_widget(self.ventes_card)
        stats_grid.add_widget(self.ca_card)
        stats_grid.add_widget(self.clients_card)
        stats_grid.add_widget(self.stock_card)
        
        section_layout.add_widget(stats_grid)
        return section_layout
    
    def create_quick_actions_section(self):
        """Créer la section des actions rapides"""
        section_layout = MDBoxLayout(orientation='vertical', spacing="8dp", adaptive_height=True)
        
        # Titre de section
        section_title = MDLabel(
            text="Actions rapides",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        section_layout.add_widget(section_title)
        
        # Grille des actions rapides
        actions_grid = MDGridLayout(cols=3, spacing="8dp", adaptive_height=True)
        
        # Actions rapides
        actions = [
            ("Nouvelle vente", "plus-circle", self.nouvelle_vente),
            ("Ajouter client", "account-plus", self.nouveau_client),
            ("Ajouter produit", "package-variant-plus", self.nouveau_produit),
            ("Voir rapports", "chart-line", self.voir_rapports),
            ("Gestion stock", "warehouse", self.gestion_stock),
            ("Paramètres", "cog", self.parametres)
        ]
        
        for title, icon, callback in actions:
            action_card = QuickActionCard(title, icon, callback)
            actions_grid.add_widget(action_card)
        
        section_layout.add_widget(actions_grid)
        return section_layout
    
    def create_alerts_section(self):
        """Créer la section des alertes"""
        section_layout = MDBoxLayout(orientation='vertical', spacing="8dp", adaptive_height=True)
        
        # Titre de section
        section_title = MDLabel(
            text="Alertes et notifications",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        section_layout.add_widget(section_title)
        
        # Carte d'alertes
        alerts_card = MDCard(
            elevation=1,
            padding="16dp",
            size_hint_y=None,
            height="120dp"
        )
        
        self.alerts_label = MDLabel(
            text="Chargement des alertes...",
            theme_text_color="Secondary",
            font_style="Body2"
        )
        
        alerts_card.add_widget(self.alerts_label)
        section_layout.add_widget(alerts_card)
        
        return section_layout
    
    def on_enter(self):
        """Actions à effectuer lors de l'entrée sur l'écran"""
        # Charger les données en arrière-plan
        Clock.schedule_once(self.load_dashboard_data, 0.1)
    
    def load_dashboard_data(self, dt):
        """Charger les données du tableau de bord"""
        def load_data():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                # Statistiques des ventes du jour
                today = datetime.now().strftime('%Y-%m-%d')
                ventes_jour = db_manager.execute_query(
                    "SELECT COUNT(*) as count FROM ventes WHERE DATE(date_vente) = ?",
                    (today,)
                )
                self.stats_data['ventes_jour'] = ventes_jour[0]['count'] if ventes_jour else 0
                
                # Chiffre d'affaires du mois
                start_month = datetime.now().replace(day=1).strftime('%Y-%m-%d')
                ca_mois = db_manager.execute_query(
                    "SELECT SUM(montant_ttc) as total FROM ventes WHERE date_vente >= ?",
                    (start_month,)
                )
                self.stats_data['ca_mois'] = ca_mois[0]['total'] or 0 if ca_mois else 0
                
                # Nombre total de clients
                clients_total = db_manager.execute_query(
                    "SELECT COUNT(*) as count FROM clients WHERE actif = 1"
                )
                self.stats_data['clients_total'] = clients_total[0]['count'] if clients_total else 0
                
                # Produits en stock bas
                stock_bas = db_manager.execute_query(
                    "SELECT COUNT(*) as count FROM produits WHERE stock_actuel <= stock_minimum AND actif = 1"
                )
                self.stats_data['produits_stock_bas'] = stock_bas[0]['count'] if stock_bas else 0
                
                # Mettre à jour l'interface utilisateur
                Clock.schedule_once(self.update_stats_ui, 0)
                
                # Charger les alertes
                self.load_alerts()
                
            except Exception as e:
                print(f"Erreur lors du chargement des données: {e}")
        
        # Exécuter en arrière-plan
        threading.Thread(target=load_data, daemon=True).start()
    
    def update_stats_ui(self, dt):
        """Mettre à jour l'interface des statistiques"""
        # Mettre à jour les cartes de statistiques
        self.ventes_card.children[0].children[0].text = str(self.stats_data['ventes_jour'])
        self.ca_card.children[0].children[0].text = f"{self.stats_data['ca_mois']:.2f} DH"
        self.clients_card.children[0].children[0].text = str(self.stats_data['clients_total'])
        self.stock_card.children[0].children[0].text = str(self.stats_data['produits_stock_bas'])
    
    def load_alerts(self):
        """Charger les alertes"""
        def load_alerts_data():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                alerts = []
                
                # Vérifier les produits en stock bas
                if self.stats_data['produits_stock_bas'] > 0:
                    alerts.append(f"⚠️ {self.stats_data['produits_stock_bas']} produit(s) en stock bas")
                
                # Vérifier les ventes récentes
                if self.stats_data['ventes_jour'] == 0:
                    alerts.append("ℹ️ Aucune vente aujourd'hui")
                else:
                    alerts.append(f"✅ {self.stats_data['ventes_jour']} vente(s) aujourd'hui")
                
                # Mettre à jour l'interface
                alerts_text = "\n".join(alerts) if alerts else "Aucune alerte"
                Clock.schedule_once(lambda dt: setattr(self.alerts_label, 'text', alerts_text), 0)
                
            except Exception as e:
                print(f"Erreur lors du chargement des alertes: {e}")
        
        threading.Thread(target=load_alerts_data, daemon=True).start()
    
    # Callbacks pour les actions rapides
    def nouvelle_vente(self):
        """Ouvrir l'écran de nouvelle vente"""
        app = MDApp.get_running_app()
        app.screen_manager.current = 'sales'
    
    def nouveau_client(self):
        """Ouvrir l'écran d'ajout de client"""
        app = MDApp.get_running_app()
        app.screen_manager.current = 'clients'
    
    def nouveau_produit(self):
        """Ouvrir l'écran d'ajout de produit"""
        app = MDApp.get_running_app()
        app.screen_manager.current = 'products'
    
    def voir_rapports(self):
        """Ouvrir l'écran des rapports"""
        app = MDApp.get_running_app()
        app.screen_manager.current = 'reports'
    
    def gestion_stock(self):
        """Ouvrir l'écran de gestion des stocks"""
        app = MDApp.get_running_app()
        app.screen_manager.current = 'products'
    
    def parametres(self):
        """Ouvrir l'écran des paramètres"""
        app = MDApp.get_running_app()
        app.screen_manager.current = 'settings'