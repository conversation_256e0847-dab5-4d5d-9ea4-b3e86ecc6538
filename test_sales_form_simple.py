#!/usr/bin/env python3
"""
Test simple du formulaire de vente pour identifier le problème
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from kivymd.uix.dialog import MDDialog
from kivymd.uix.textfield import MDTextField
from kivymd.uix.button import MDFlatButton


class SimpleSaleFormDialog(MDDialog):
    """Formulaire de vente simplifié pour test"""
    
    def __init__(self, **kwargs):
        # Créer le contenu d'abord
        content = self.create_content()
        
        # Créer les boutons
        cancel_btn = MDFlatButton(
            text="❌ Annuler",
            on_release=self.dismiss
        )
        
        save_btn = MDRaisedButton(
            text="💾 Enregistrer",
            on_release=self.dismiss
        )
        
        super().__init__(
            title="🛒 Test Formulaire Vente Simple",
            type="custom",
            content_cls=content,
            size_hint=(0.8, None),
            height="400dp",
            buttons=[cancel_btn, save_btn],
            **kwargs
        )
    
    def create_content(self):
        """Créer le contenu du formulaire"""
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="300dp"
        )
        
        # Titre
        title = MDLabel(
            text="🛒 Formulaire de Vente Simplifié",
            font_style="H6",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="40dp"
        )
        
        # Champ client
        client_field = MDTextField(
            hint_text="Nom du client",
            mode="rectangle",
            size_hint_y=None,
            height="56dp"
        )
        
        # Champ montant
        montant_field = MDTextField(
            hint_text="Montant TTC",
            mode="rectangle",
            size_hint_y=None,
            height="56dp"
        )
        
        # Champ paiement
        paiement_field = MDTextField(
            hint_text="Mode de paiement",
            text="Espèces",
            mode="rectangle",
            size_hint_y=None,
            height="56dp"
        )
        
        # Champ notes
        notes_field = MDTextField(
            hint_text="Notes (optionnel)",
            mode="rectangle",
            multiline=True,
            size_hint_y=None,
            height="80dp"
        )
        
        layout.add_widget(title)
        layout.add_widget(client_field)
        layout.add_widget(montant_field)
        layout.add_widget(paiement_field)
        layout.add_widget(notes_field)
        
        return layout


class TestSalesFormSimpleApp(MDApp):
    """Test simple du formulaire de vente"""
    
    def build(self):
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        title = MDLabel(
            text="🧪 Test Simple\nFormulaire de Vente",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Test formulaire simple
        simple_btn = MDRaisedButton(
            text="📝 Formulaire Simple",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_simple_form
        )
        
        # Test formulaire complet
        complete_btn = MDRaisedButton(
            text="🛒 Formulaire Complet",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_complete_form
        )
        
        self.result_label = MDLabel(
            text="Cliquez pour tester les formulaires",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(simple_btn)
        layout.add_widget(complete_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_simple_form(self, *args):
        """Tester le formulaire simple"""
        try:
            dialog = SimpleSaleFormDialog()
            dialog.open()
            
            self.result_label.text = "✅ Formulaire simple ouvert !\n\n" \
                                   "Si vous voyez les champs, le problème\n" \
                                   "vient du formulaire complet."
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur formulaire simple :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def test_complete_form(self, *args):
        """Tester le formulaire complet"""
        try:
            from sales_form_improved import ImprovedSaleFormDialog
            
            dialog = ImprovedSaleFormDialog()
            dialog.open()
            
            self.result_label.text = "✅ Formulaire complet ouvert !\n\n" \
                                   "Vérifiez si le contenu s'affiche."
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur formulaire complet :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()


def main():
    print("🧪 Test Simple - Formulaire de Vente")
    print("Objectif: Identifier le problème d'affichage")
    
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestSalesFormSimpleApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()