#!/usr/bin/env python3
"""
Test du formulaire actuel de catégories
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel

# Importer le formulaire actuel
from screens.categories_screen import CategoryFormDialog


class TestFormulaireActuelApp(MDApp):
    """Application de test pour le formulaire actuel"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Formulaire Actuel"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construction de l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        # Titre
        title = MDLabel(
            text="🧪 Test - Formulaire Actuel",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Instructions
        instructions = MDLabel(
            text="Ce test utilise le formulaire CategoryFormDialog actuel.\n\n"
                 "PROBLÈME RAPPORTÉ :\n"
                 "❌ Vous ne voyez que les boutons 'Annuler' et 'Enregistrer'\n"
                 "❌ Les champs 'Nom' et 'Description' ne sont pas visibles\n\n"
                 "VÉRIFICATION :\n"
                 "Ouvrez le formulaire et dites-moi exactement ce que vous voyez.",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="180dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="140dp"
        )
        
        # Test nouveau formulaire
        new_btn = MDRaisedButton(
            text="🆕 Nouveau Formulaire",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_new_form
        )
        
        # Test modification
        edit_btn = MDRaisedButton(
            text="✏️ Formulaire Modification",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_edit_form
        )
        
        buttons_layout.add_widget(new_btn)
        buttons_layout.add_widget(edit_btn)
        
        # Résultats
        self.result_label = MDLabel(
            text="Cliquez sur un bouton pour ouvrir le formulaire actuel.\n"
                 "Décrivez exactement ce que vous voyez !",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(instructions)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_new_form(self, *args):
        """Tester le nouveau formulaire"""
        self.result_label.text = "🆕 FORMULAIRE NOUVEAU OUVERT\n\n" \
                                "DITES-MOI CE QUE VOUS VOYEZ :\n" \
                                "1. Titre du dialog ?\n" \
                                "2. Labels de champs ?\n" \
                                "3. Champs de saisie ?\n" \
                                "4. Boutons en bas ?\n\n" \
                                "Si vous ne voyez que les boutons,\n" \
                                "nous devons corriger le problème !"
        
        print("🆕 Ouverture du formulaire nouveau...")
        print("📝 Vérifiez ce qui s'affiche dans le dialog")
        
        dialog = CategoryFormDialog(
            on_save_callback=self.on_save_callback
        )
        dialog.open()
    
    def test_edit_form(self, *args):
        """Tester le formulaire de modification"""
        self.result_label.text = "✏️ FORMULAIRE MODIFICATION OUVERT\n\n" \
                                "AVEC DONNÉES DE TEST :\n" \
                                "- Nom: 'Test Électronique'\n" \
                                "- Description: 'Catégorie de test'\n" \
                                "- ID: 1, Produits: 3\n\n" \
                                "VÉRIFIEZ si vous voyez ces données\n" \
                                "dans les champs du formulaire !"
        
        # Données de test
        test_data = {
            'id': 1,
            'nom': 'Test Électronique',
            'description': 'Catégorie de test pour vérifier la visibilité',
            'products_count': 3,
            'date_creation': '2024-01-15T10:30:00'
        }
        
        print("✏️ Ouverture du formulaire de modification...")
        print("📝 Données de test chargées")
        print(f"   - Nom: {test_data['nom']}")
        print(f"   - Description: {test_data['description']}")
        
        dialog = CategoryFormDialog(
            category_data=test_data,
            on_save_callback=self.on_save_callback
        )
        dialog.open()
    
    def on_save_callback(self, category_data):
        """Callback de sauvegarde"""
        nom = category_data.get('nom', 'Sans nom')
        description = category_data.get('description', 'Aucune')
        
        self.result_label.text = f"🎉 SAUVEGARDE RÉUSSIE !\n\n" \
                                f"Cela signifie que les champs étaient visibles\n" \
                                f"et que vous avez pu saisir :\n\n" \
                                f"📂 Nom: {nom}\n" \
                                f"📝 Description: {description[:50]}{'...' if len(description) > 50 else ''}\n\n" \
                                f"✅ Le formulaire fonctionne correctement !"
        
        print("🎉 SUCCÈS - Sauvegarde réussie !")
        print(f"  - Nom saisi: {nom}")
        print(f"  - Description: {description}")
        print("✅ Cela confirme que les champs étaient visibles et fonctionnels")


def main():
    """Fonction principale"""
    print("🧪 Test - Formulaire CategoryFormDialog Actuel")
    print("=" * 60)
    print("OBJECTIF: Tester le formulaire actuel pour identifier le problème")
    print()
    print("PROBLÈME RAPPORTÉ:")
    print("❌ L'utilisateur ne voit que les boutons dans le formulaire")
    print("❌ Les champs de saisie ne sont pas visibles")
    print()
    print("CE TEST VA:")
    print("1. Ouvrir le formulaire CategoryFormDialog actuel")
    print("2. Vous permettre de vérifier ce qui s'affiche")
    print("3. Tester avec des données pré-remplies")
    print("4. Confirmer si le problème existe vraiment")
    print()
    print("INSTRUCTIONS:")
    print("- Ouvrez le formulaire")
    print("- Décrivez exactement ce que vous voyez")
    print("- Testez la saisie si les champs sont visibles")
    print("=" * 60)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestFormulaireActuelApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()