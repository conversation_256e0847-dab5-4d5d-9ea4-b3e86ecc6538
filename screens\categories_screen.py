"""
Écran de gestion des catégories avec CRUD complet
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import MDText<PERSON>ield
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.snackbar import MDSnackbar
from kivymd.uix.dialog import MDDialog
from kivymd.app import MDApp
from kivy.clock import Clock
from datetime import datetime
import threading
from models.category_model import CategoryModel
from forms.category_form import CategoryFormDialog


class CategoryCard(MDCard):
    """Carte d'affichage d'une catégorie"""
    
    def __init__(self, category_data, on_edit_callback=None, on_delete_callback=None, **kwargs):
        super().__init__(**kwargs)
        self.category_data = category_data
        self.on_edit_callback = on_edit_callback
        self.on_delete_callback = on_delete_callback
        
        # Style de la carte
        self.elevation = 2
        self.radius = [8]
        self.size_hint_y = None
        self.height = "120dp"
        self.padding = "16dp"
        self.spacing = "8dp"
        
        self.create_content()
    
    def create_content(self):
        """Créer le contenu de la carte"""
        layout = MDBoxLayout(orientation='vertical')
        
        # En-tête avec nom et statut
        header_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="40dp"
        )
        
        # Nom de la catégorie
        nom = self.category_data.get('nom', 'Sans nom')
        nom_label = MDLabel(
            text=f"📁 {nom}",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_x=0.5
        )
        
        # Statut par défaut (colonne statut n'existe pas)
        statut_label = MDLabel(
            text="✅ Actif",
            font_style="Subtitle2",
            theme_text_color="Custom",
            text_color=[0, 0.8, 0, 1],
            size_hint_x=0.3,
            bold=True
        )
        
        # Boutons d'action
        buttons_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_x=0.2,
            spacing="8dp"
        )
        
        edit_btn = MDIconButton(
            icon="pencil",
            theme_icon_color="Custom",
            icon_color=[0.2, 0.6, 1, 1],
            on_release=self.edit_category
        )
        
        delete_btn = MDIconButton(
            icon="delete",
            theme_icon_color="Custom",
            icon_color=[1, 0.3, 0.3, 1],
            on_release=self.delete_category
        )
        
        buttons_layout.add_widget(edit_btn)
        buttons_layout.add_widget(delete_btn)
        
        header_layout.add_widget(nom_label)
        header_layout.add_widget(statut_label)
        header_layout.add_widget(buttons_layout)
        
        # Description
        description_layout = MDBoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height="32dp"
        )
        
        description = self.category_data.get('description', '')
        if description:
            if len(description) > 80:
                description = description[:80] + "..."
            
            description_label = MDLabel(
                text=f"📄 {description}",
                font_style="Body2",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="32dp"
            )
            description_layout.add_widget(description_label)
        else:
            description_label = MDLabel(
                text="📄 Aucune description",
                font_style="Body2",
                theme_text_color="Hint",
                size_hint_y=None,
                height="32dp"
            )
            description_layout.add_widget(description_label)
        
        # Informations supplémentaires
        info_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="32dp"
        )
        
        # Date de création
        date_creation = self.category_data.get('date_creation', '')
        if date_creation:
            try:
                if isinstance(date_creation, str):
                    date_obj = datetime.fromisoformat(date_creation.replace('Z', '+00:00'))
                    date_formatted = date_obj.strftime('%d/%m/%Y')
                else:
                    date_formatted = str(date_creation)[:10]
            except:
                date_formatted = str(date_creation)[:10]
        else:
            date_formatted = "Date inconnue"
        
        date_info = MDLabel(
            text=f"📅 Créée: {date_formatted}",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.4
        )
        
        # ID
        id_info = MDLabel(
            text=f"🆔 ID: {self.category_data.get('id', 'N/A')}",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.2
        )
        
        # Statistiques (placeholder)
        stats_info = MDLabel(
            text="📊 Voir stats",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.4
        )
        
        info_layout.add_widget(date_info)
        info_layout.add_widget(id_info)
        info_layout.add_widget(stats_info)
        
        layout.add_widget(header_layout)
        layout.add_widget(description_layout)
        layout.add_widget(info_layout)
        
        self.add_widget(layout)
    
    def edit_category(self, *args):
        """Modifier la catégorie"""
        if self.on_edit_callback:
            self.on_edit_callback(self.category_data)
    
    def delete_category(self, *args):
        """Supprimer la catégorie"""
        if self.on_delete_callback:
            self.on_delete_callback(self.category_data)


class CategoriesScreen(MDScreen):
    """Écran de gestion des catégories"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.category_model = CategoryModel()
        self.categories_data = []
        self.filtered_categories = []
        self.create_interface()
        self.load_categories()
    
    def create_interface(self):
        """Créer l'interface"""
        main_layout = MDBoxLayout(orientation='vertical')
        
        # Barre d'outils
        toolbar = MDTopAppBar(
            title="📁 Gestion des Catégories",
            elevation=2
            # left_action_items supprimés temporairement pour éviter l'avertissement width_mult
        )
        
        # Contenu principal
        content_layout = MDBoxLayout(
            orientation='vertical',
            padding="16dp",
            spacing="16dp"
        )
        
        # Bouton de retour (remplace left_action_items)
        back_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="48dp",
            padding="8dp"
        )
        
        back_btn = MDIconButton(
            icon="arrow-left",
            theme_icon_color="Primary",
            on_release=lambda x: self.go_back()
        )
        
        back_layout.add_widget(back_btn)
        
        # Boutons d'action (remplacent les right_action_items)
        action_layout = MDBoxLayout(
            orientation='horizontal',
            spacing="16dp",
            size_hint_y=None,
            height="48dp"
        )
        
        add_btn = MDRaisedButton(
            text="➕ Ajouter",
            on_release=self.add_category,
            size_hint_x=0.5
        )
        
        refresh_btn = MDRaisedButton(
            text="🔄 Actualiser",
            on_release=self.refresh_categories,
            size_hint_x=0.5
        )
        
        action_layout.add_widget(add_btn)
        action_layout.add_widget(refresh_btn)
        
        # Barre de recherche
        search_layout = MDBoxLayout(
            orientation='horizontal',
            spacing="16dp",
            size_hint_y=None,
            height="56dp"
        )
        
        self.search_field = MDTextField(
            hint_text="🔍 Rechercher une catégorie...",
            size_hint_x=0.7,
            mode="rectangle",
            on_text=self.filter_categories
        )
        
        clear_search_btn = MDIconButton(
            icon="close",
            theme_icon_color="Custom",
            icon_color=[0.6, 0.6, 0.6, 1],
            size_hint_x=0.1,
            on_release=self.clear_search
        )
        
        stats_btn = MDRaisedButton(
            text="📊 Stats",
            size_hint_x=0.2,
            on_release=self.show_stats
        )
        
        search_layout.add_widget(self.search_field)
        search_layout.add_widget(clear_search_btn)
        search_layout.add_widget(stats_btn)
        
        # Zone de défilement pour les catégories
        self.scroll_view = MDScrollView()
        self.categories_container = MDBoxLayout(
            orientation='vertical',
            spacing="12dp",
            size_hint_y=None,
            height="0dp"
        )
        self.categories_container.bind(minimum_height=self.categories_container.setter('height'))
        
        self.scroll_view.add_widget(self.categories_container)
        
        content_layout.add_widget(action_layout)
        content_layout.add_widget(search_layout)
        content_layout.add_widget(self.scroll_view)
        
        main_layout.add_widget(toolbar)
        main_layout.add_widget(back_layout)
        main_layout.add_widget(content_layout)
        
        self.add_widget(main_layout)
    
    def load_categories(self):
        """Charger les catégories depuis la base de données"""
        def load_in_background():
            try:
                self.categories_data = self.category_model.get_all_categories()
                self.filtered_categories = self.categories_data.copy()
                Clock.schedule_once(lambda dt: self.update_categories_display())
                
            except Exception as e:
                Clock.schedule_once(lambda dt: self.show_error(f"Erreur lors du chargement: {str(e)}"))
        
        threading.Thread(target=load_in_background, daemon=True).start()
    
    def update_categories_display(self):
        """Mettre à jour l'affichage des catégories"""
        # Vider le conteneur
        self.categories_container.clear_widgets()
        
        if not self.filtered_categories:
            # Message si aucune catégorie
            no_data_label = MDLabel(
                text="📁 Aucune catégorie trouvée\n\nCliquez sur ➕ pour créer votre première catégorie",
                font_style="H6",
                theme_text_color="Secondary",
                halign="center",
                size_hint_y=None,
                height="200dp"
            )
            self.categories_container.add_widget(no_data_label)
        else:
            # Ajouter les cartes de catégories
            for category in self.filtered_categories:
                card = CategoryCard(
                    category_data=category,
                    on_edit_callback=self.edit_category,
                    on_delete_callback=self.confirm_delete_category
                )
                self.categories_container.add_widget(card)
    
    def add_category(self, *args):
        """Ajouter une nouvelle catégorie"""
        dialog = CategoryFormDialog(
            on_save_callback=self.on_category_saved
        )
        dialog.open()
    
    def edit_category(self, category_data):
        """Modifier une catégorie existante"""
        dialog = CategoryFormDialog(
            category_data=category_data,
            on_save_callback=self.on_category_saved
        )
        dialog.open()
    
    def on_category_saved(self, category_data):
        """Callback appelé après sauvegarde d'une catégorie"""
        nom = category_data.get('nom', 'N/A')
        self.show_success(f"Catégorie '{nom}' sauvegardée avec succès")
        self.refresh_categories()
    
    def confirm_delete_category(self, category_data):
        """Confirmer la suppression d'une catégorie"""
        nom = category_data.get('nom', 'N/A')
        
        message = f"Êtes-vous sûr de vouloir supprimer la catégorie ?\n\n" \
                 f"📁 Nom: {nom}\n" \
                 f"🆔 ID: {category_data.get('id', 'N/A')}\n\n" \
                 f"⚠️ Cette action est irréversible !\n" \
                 f"⚠️ Tous les produits liés seront également affectés !"
        
        dialog = MDDialog(
            title="🗑️ Confirmer la suppression",
            text=message,
            buttons=[
                MDFlatButton(
                    text="❌ Annuler",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDRaisedButton(
                    text="🗑️ Supprimer",
                    on_release=lambda x: self.delete_category(category_data, dialog)
                )
            ]
        )
        dialog.open()
    
    def delete_category(self, category_data, dialog):
        """Supprimer une catégorie"""
        dialog.dismiss()
        
        def delete_in_background():
            try:
                success = self.category_model.delete_category(category_data['id'])
                
                if success:
                    nom = category_data.get('nom', 'N/A')
                    Clock.schedule_once(lambda dt: self.show_success(f"Catégorie '{nom}' supprimée"))
                    Clock.schedule_once(lambda dt: self.refresh_categories())
                else:
                    Clock.schedule_once(lambda dt: self.show_error("Erreur lors de la suppression"))
                
            except ValueError as e:
                Clock.schedule_once(lambda dt: self.show_error(str(e)))
            except Exception as e:
                Clock.schedule_once(lambda dt: self.show_error(f"Erreur: {str(e)}"))
        
        threading.Thread(target=delete_in_background, daemon=True).start()
    
    def filter_categories(self, instance, text):
        """Filtrer les catégories selon le texte de recherche"""
        if not text.strip():
            self.filtered_categories = self.categories_data.copy()
        else:
            search_text = text.lower()
            self.filtered_categories = []
            
            for category in self.categories_data:
                # Recherche dans nom et description
                searchable_fields = [
                    str(category.get('nom', '')),
                    str(category.get('description', ''))
                ]
                
                if any(search_text in field.lower() for field in searchable_fields if field):
                    self.filtered_categories.append(category)
        
        self.update_categories_display()
    
    def clear_search(self, *args):
        """Effacer la recherche"""
        self.search_field.text = ""
        self.filtered_categories = self.categories_data.copy()
        self.update_categories_display()
    
    def refresh_categories(self, *args):
        """Actualiser la liste des catégories"""
        self.load_categories()
    
    def show_stats(self, *args):
        """Afficher les statistiques des catégories"""
        total_categories = len(self.categories_data)
        
        stats_message = f"📊 STATISTIQUES DES CATÉGORIES\n\n" \
                       f"📁 Total: {total_categories} catégorie(s)\n" \
                       f"✅ Toutes actives par défaut"
        
        dialog = MDDialog(
            title="📊 Statistiques",
            text=stats_message,
            buttons=[
                MDFlatButton(
                    text="✅ OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"❌ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"❌ {message}")
    
    def show_success(self, message):
        """Afficher un message de succès"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"✅ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"✅ {message}")
    
    def go_back(self, *args):
        """Retourner à l'écran précédent"""
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = 'dashboard'