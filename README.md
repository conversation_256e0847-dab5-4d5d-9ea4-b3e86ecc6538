# GesComPro_LibTam - Application de Gestion Commerciale

**Powered by: LKAIHAL LAHCEN_AIA**

## 📋 Description

GesComPro_LibTam est une application desktop moderne de gestion commerciale développée en Python avec Kivy/KivyMD. Elle offre une interface utilisateur moderne et responsive pour gérer efficacement votre activité commerciale.

## ✨ Fonctionnalités

### 🏠 Tableau de Bord
- Vue d'ensemble des statistiques clés
- Suivi des ventes du jour
- Chiffre d'affaires du mois
- Alertes de stock bas
- Actions rapides

### 👥 Gestion des Clients
- Ajout, modification, suppression de clients
- Informations complètes (nom, entreprise, contact)
- Recherche et filtrage
- Historique des achats

### 📦 Gestion des Produits
- Catalogue de produits complet
- Gestion des stocks en temps réel
- Catégories et fournisseurs
- Alertes de stock minimum
- Prix d'achat et de vente

### 💰 Gestion des Ventes
- Création de ventes rapide
- Sélection de clients et produits
- Calcul automatique des totaux
- Gestion des modes de paiement
- Historique des ventes

### 📊 Rapports et Statistiques
- Tableaux de bord interactifs
- Graphiques d'évolution des ventes
- Top des produits et clients
- Statistiques par période
- Export des données

### ⚙️ Paramètres
- Configuration de l'entreprise
- Personnalisation de l'interface
- Thème clair/sombre
- Sauvegarde et restauration
- Export des données

## 🛠️ Technologies Utilisées

- **Python 3.8+** - Langage de programmation
- **Kivy/KivyMD** - Framework d'interface utilisateur moderne
- **SQLite** - Base de données locale
- **Matplotlib** - Génération de graphiques
- **Threading** - Traitement asynchrone

## 📋 Prérequis

- Python 3.8 ou supérieur
- Windows 10/11 (pour l'exécutable)

## 🚀 Installation

### Option 1: Exécutable Windows (.exe)

1. Téléchargez `GesComPro_LibTam.exe`
2. Exécutez le fichier
3. L'application se lance directement

### Option 2: Installation depuis le code source

1. Clonez le repository:
```bash
git clone https://github.com/votre-repo/gescompro.git
cd gescompro
```

2. Installez les dépendances:
```bash
pip install -r requirements.txt
```

3. Lancez l'application:
```bash
python main.py
```

## 🔧 Création de l'exécutable

Pour créer votre propre exécutable:

```bash
python build_exe.py
```

L'exécutable sera créé dans le dossier `dist/`.

## 📁 Structure du Projet

```
gescompro/
├── main.py                 # Point d'entrée de l'application
├── config.py              # Configuration globale
├── build_exe.py           # Script de création d'exécutable
├── requirements.txt       # Dépendances Python
├── database/
│   ├── __init__.py
│   └── db_manager.py      # Gestionnaire de base de données
├── screens/
│   ├── __init__.py
│   ├── dashboard_screen.py    # Écran tableau de bord
│   ├── clients_screen.py      # Écran gestion clients
│   ├── products_screen.py     # Écran gestion produits
│   ├── sales_screen.py        # Écran gestion ventes
│   ├── reports_screen.py      # Écran rapports
│   └── settings_screen.py     # Écran paramètres
├── utils/
│   ├── __init__.py
│   └── helpers.py         # Fonctions utilitaires
├── data/                  # Dossier des données (créé automatiquement)
├── backups/              # Dossier des sauvegardes
└── exports/              # Dossier des exports
```

## 🎯 Utilisation

### Premier Lancement

1. **Démarrage**: Lancez l'application
2. **Données d'exemple**: Des données de démonstration sont automatiquement créées
3. **Configuration**: Allez dans Paramètres pour configurer votre entreprise

### Navigation

- **Menu latéral**: Cliquez sur l'icône menu (☰) pour naviguer
- **Thème**: Cliquez sur l'icône thème (🌓) pour basculer clair/sombre
- **Paramètres**: Cliquez sur l'icône paramètres (⚙️)

### Gestion des Clients

1. Allez dans "Clients"
2. Cliquez sur "Nouveau Client"
3. Remplissez les informations
4. Sauvegardez

### Création d'une Vente

1. Allez dans "Ventes"
2. Cliquez sur "Nouvelle Vente"
3. Sélectionnez un client
4. Ajoutez des produits
5. Confirmez la vente

### Consultation des Rapports

1. Allez dans "Rapports"
2. Sélectionnez la période souhaitée
3. Consultez les statistiques et graphiques

## 🔒 Sécurité et Sauvegarde

- **Base de données locale**: Vos données restent sur votre machine
- **Sauvegarde automatique**: Possibilité de créer des sauvegardes
- **Export**: Exportez vos données en CSV/Excel

## 🐛 Résolution de Problèmes

### L'application ne se lance pas
- Vérifiez que vous avez les droits d'exécution
- Sous Windows, autorisez l'application dans Windows Defender

### Erreur de base de données
- Vérifiez les permissions du dossier `data/`
- Supprimez le fichier `data/gescom.db` pour réinitialiser

### Interface qui ne s'affiche pas correctement
- Vérifiez votre résolution d'écran
- Essayez de basculer entre thème clair/sombre

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🤝 Contribution

Les contributions sont les bienvenues! N'hésitez pas à:

1. Fork le projet
2. Créer une branche pour votre fonctionnalité
3. Commiter vos changements
4. Pousser vers la branche
5. Ouvrir une Pull Request

## 📞 Support

Pour toute question ou problème:

- Ouvrez une issue sur GitHub
- Consultez la documentation
- Contactez l'équipe de développement

## 🔄 Mises à Jour

### Version 1.0.0
- Interface moderne avec Kivy/KivyMD
- Gestion complète des clients, produits et ventes
- Rapports et statistiques avancés
- Thème clair/sombre
- Export des données
- Base de données SQLite

---

**GesComPro_LibTam** - Simplifiez votre gestion commerciale! 🚀

**Développé par : LKAIHAL LAHCEN_AIA**