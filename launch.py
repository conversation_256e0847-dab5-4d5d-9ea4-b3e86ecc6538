"""
Script de lancement simple pour GesComPro_LibTam
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

def check_dependencies():
    """Vérifier les dépendances essentielles"""
    missing_deps = []
    
    try:
        import kivy
        print(f"✅ Kivy {kivy.__version__}")
    except ImportError:
        missing_deps.append("kivy")
    
    try:
        import kivymd
        print(f"✅ KivyMD")
    except ImportError:
        missing_deps.append("kivymd")
    
    try:
        import matplotlib
        print(f"✅ Matplotlib {matplotlib.__version__}")
    except ImportError:
        missing_deps.append("matplotlib")
    
    try:
        import reportlab
        print(f"✅ ReportLab")
    except ImportError:
        missing_deps.append("reportlab")
    
    if missing_deps:
        print(f"\n❌ Dépendances manquantes: {', '.join(missing_deps)}")
        print("Installez-les avec: pip install " + " ".join(missing_deps))
        return False
    
    return True

def main():
    """Fonction principale"""
    print("🚀 Lancement de GesComPro_LibTam")
    print("=" * 40)
    
    # Vérifier les dépendances
    if not check_dependencies():
        input("\nAppuyez sur Entrée pour quitter...")
        return
    
    print("\n📱 Démarrage de l'application...")
    
    try:
        # Importer et lancer l'application
        from main import GesComApp
        
        app = GesComApp()
        app.run()
        
    except Exception as e:
        print(f"\n❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()
        input("\nAppuyez sur Entrée pour quitter...")

if __name__ == "__main__":
    main()