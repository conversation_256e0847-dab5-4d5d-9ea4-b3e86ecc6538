#!/usr/bin/env python3
"""
Script de débogage pour identifier la source de l'avertissement width_mult
"""

import os
import sys
import warnings
import traceback

# Capturer tous les avertissements avec traceback
def warning_handler(message, category, filename, lineno, file=None, line=None):
    if "width_mult" in str(message):
        print(f"\n🔍 AVERTISSEMENT width_mult DÉTECTÉ:")
        print(f"📄 Fichier: {filename}")
        print(f"📍 Ligne: {lineno}")
        print(f"⚠️ Message: {message}")
        print(f"📂 Catégorie: {category}")
        print("\n📋 STACK TRACE:")
        traceback.print_stack()
        print("\n" + "="*60 + "\n")

# Installer le gestionnaire d'avertissements
warnings.showwarning = warning_handler

# Configurer le logger de Kivy
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

# Configuration pour Windows
if sys.platform == 'win32':
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'

print("🔍 DÉMARRAGE DU DÉBOGAGE width_mult")
print("Objectif: Identifier la source exacte de l'avertissement")
print("="*60)

try:
    from kivymd.app import MDApp
    from kivymd.uix.screen import MDScreen
    from kivymd.uix.boxlayout import MDBoxLayout
    from kivymd.uix.button import MDRaisedButton
    from kivymd.uix.label import MDLabel
    
    print("✅ Imports KivyMD réussis")
    
    # Import de l'application principale
    print("📦 Import de l'application principale...")
    from main import GesComApp
    
    print("✅ Import de GesComApp réussi")
    
    class DebugApp(MDApp):
        def build(self):
            print("🏗️ Construction de l'interface de débogage...")
            
            screen = MDScreen()
            layout = MDBoxLayout(
                orientation='vertical',
                spacing="20dp",
                padding="20dp"
            )
            
            title = MDLabel(
                text="🔍 Débogage width_mult\nRecherche de la source...",
                font_style="H5",
                theme_text_color="Primary",
                halign="center",
                size_hint_y=None,
                height="100dp"
            )
            
            test_btn = MDRaisedButton(
                text="🧪 Tester Application Principale",
                size_hint_y=None,
                height="60dp",
                on_release=self.test_main_app
            )
            
            self.result_label = MDLabel(
                text="En attente de test...",
                font_style="Body1",
                theme_text_color="Secondary",
                halign="center"
            )
            
            layout.add_widget(title)
            layout.add_widget(test_btn)
            layout.add_widget(self.result_label)
            
            screen.add_widget(layout)
            return screen
        
        def test_main_app(self, *args):
            print("\n🧪 TEST: Création de l'application principale")
            try:
                # Créer l'application principale
                main_app = GesComApp()
                
                # Construire l'interface
                print("🏗️ Construction de l'interface principale...")
                root_widget = main_app.build()
                
                self.result_label.text = "✅ Application principale créée\n" \
                                       "Vérifiez la console pour les avertissements width_mult"
                
                print("✅ Interface principale construite avec succès")
                
            except Exception as e:
                error_msg = f"❌ Erreur lors du test: {str(e)}"
                self.result_label.text = error_msg
                print(error_msg)
                traceback.print_exc()
    
    print("🚀 Lancement de l'application de débogage...")
    app = DebugApp()
    app.run()
    
except Exception as e:
    print(f"❌ Erreur critique: {e}")
    traceback.print_exc()