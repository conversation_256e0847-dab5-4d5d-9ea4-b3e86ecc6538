#!/usr/bin/env python3
"""
Test du bouton de suppression pour ventes "En cours" ET "Annulée"
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

# Configuration pour Windows
if sys.platform == 'win32':
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel

# Import de l'écran des ventes
from screens.sales_screen import SalesScreen


class TestDeleteBothStatusesApp(MDApp):
    """Test du bouton de suppression pour les deux statuts"""
    
    def build(self):
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        title = MDLabel(
            text="🗑️ Test Suppression Étendue\nEn Cours + Annulée",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Test écran des ventes avec suppression étendue
        sales_btn = MDRaisedButton(
            text="🛒 Écran Ventes - Suppression Étendue",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_sales_screen
        )
        
        self.result_label = MDLabel(
            text="Test de suppression pour deux statuts\n\n" \
                 "STATUTS SUPPRIMABLES :\n" \
                 "✅ En cours : Suppression + restauration stock\n" \
                 "✅ Annulée : Suppression + nettoyage données\n" \
                 "❌ Payée : Protection - pas de suppression\n\n" \
                 "FONCTIONNALITÉS :\n" \
                 "• Bouton 🗑️ visible pour En cours ET Annulée\n" \
                 "• Dialogue adapté selon le statut\n" \
                 "• Gestion différenciée des stocks",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(sales_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_sales_screen(self, *args):
        """Tester l'écran des ventes avec suppression étendue"""
        try:
            # Créer et afficher l'écran des ventes
            sales_screen = SalesScreen()
            
            # Remplacer l'écran actuel
            self.root.clear_widgets()
            self.root.add_widget(sales_screen)
            
            self.result_label.text = "🛒 Écran des ventes ouvert !\n\n" \
                                   "VÉRIFICATIONS ÉTENDUES :\n" \
                                   "✅ Bouton 🗑️ visible pour 'En cours'\n" \
                                   "✅ Bouton 🗑️ visible pour 'Annulée'\n" \
                                   "❌ Bouton 🗑️ absent pour 'Payée'\n\n" \
                                   "TESTS À EFFECTUER :\n" \
                                   "1. Supprimer vente 'En cours' → Stock restauré\n" \
                                   "2. Supprimer vente 'Annulée' → Nettoyage seul\n" \
                                   "3. Vérifier protection ventes 'Payée'\n\n" \
                                   "Dialogue adapté selon le statut !"
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur écran ventes :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()


def main():
    print("🗑️ Test Suppression Étendue - En Cours + Annulée")
    print("OBJECTIF: Vérifier la suppression pour les deux statuts autorisés")
    
    try:
        app = TestDeleteBothStatusesApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()