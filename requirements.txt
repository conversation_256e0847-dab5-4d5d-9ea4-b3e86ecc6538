# GesComPro - Requirements
# Application de gestion commerciale moderne

# Interface utilisateur
kivy>=2.3.0
kivymd>=1.2.0

# Génération de PDF
reportlab>=4.0.0

# Graphiques et visualisation
matplotlib>=3.7.0

# Dépendances Windows pour Kivy
kivy-deps.angle>=0.4.0; sys_platform == "win32"
kivy-deps.sdl2>=0.8.0; sys_platform == "win32"
kivy-deps.glew>=0.3.1; sys_platform == "win32"
pypiwin32; sys_platform == "win32"

# Pour la création d'exécutables
pyinstaller>=5.0.0

# Utilitaires pour les codes-barres (optionnel)
# python-barcode>=0.14.0
# pillow>=9.0.0

# Base de données (inclus avec Python)
# sqlite3

# Utilitaires système (inclus avec Python 3.4+)
# pathlib

# Développement et tests (optionnel)
# pytest>=7.0.0
# black>=22.0.0
# flake8>=4.0.0