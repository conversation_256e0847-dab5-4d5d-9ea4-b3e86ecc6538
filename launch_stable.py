"""
Script de lancement ultra-stable pour GesComPro_LibTam
Sans matplotlib pour éviter les erreurs de compatibilité
"""

import sys
import os
import warnings
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

# Supprimer tous les avertissements
warnings.filterwarnings("ignore")

def configure_environment():
    """Configurer l'environnement pour un lancement ultra-stable"""
    # Configuration Kivy optimale
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    os.environ['KIVY_LOG_MODE'] = 'PYTHON'
    
    # Désactiver matplotlib pour éviter les erreurs
    os.environ['GESCOM_DISABLE_MATPLOTLIB'] = '1'
    
    # Configuration pour de meilleures performances
    os.environ['KIVY_WINDOW'] = 'sdl2'
    
    print("✅ Environnement configuré en mode ultra-stable")


def check_core_dependencies():
    """Vérifier uniquement les dépendances essentielles"""
    print("🔍 Vérification des dépendances essentielles...")
    
    core_deps = {
        'kivy': 'Interface utilisateur Kivy',
        'kivymd': 'Material Design KivyMD',
        'sqlite3': 'Base de données SQLite'
    }
    
    missing = []
    for dep, desc in core_deps.items():
        try:
            __import__(dep)
            print(f"   ✅ {desc}")
        except ImportError:
            missing.append(dep)
            print(f"   ❌ {desc} manquant")
    
    # Dépendances optionnelles
    optional_deps = {
        'reportlab': 'Génération PDF (optionnel)',
        'matplotlib': 'Graphiques (désactivé pour stabilité)'
    }
    
    for dep, desc in optional_deps.items():
        try:
            __import__(dep)
            print(f"   ✅ {desc}")
        except ImportError:
            print(f"   ⚠️ {desc}")
    
    if missing:
        print(f"\n❌ Dépendances critiques manquantes: {', '.join(missing)}")
        print("Installez avec: pip install " + " ".join(missing))
        return False
    
    return True


def initialize_database():
    """Initialiser la base de données de manière sécurisée"""
    print("💾 Initialisation de la base de données...")
    
    try:
        from database.db_manager import DatabaseManager
        
        db = DatabaseManager()
        if db.connect():
            # Initialiser avec les données de base
            db.initialize_database()
            print("   ✅ Base de données initialisée et optimisée")
            return True
        else:
            print("   ❌ Impossible de se connecter à la base de données")
            return False
    except Exception as e:
        print(f"   ❌ Erreur base de données: {e}")
        return False


def main():
    """Fonction principale ultra-stable"""
    print("🚀 LANCEMENT ULTRA-STABLE DE GesComPro_LibTam")
    print("=" * 50)
    print("📱 Gestion Commerciale Professionnelle")
    print("🛡️ Version ultra-stable sans matplotlib")
    print("⚡ Base de données optimisée")
    print("=" * 50)
    
    # Configuration de l'environnement
    configure_environment()
    
    # Vérification des dépendances essentielles
    if not check_core_dependencies():
        input("\nAppuyez sur Entrée pour quitter...")
        return
    
    # Initialisation de la base de données
    if not initialize_database():
        print("⚠️ Avertissement: Problème avec la base de données")
        print("L'application peut fonctionner en mode dégradé")
    
    # Démarrage de l'application
    print("\n📱 Démarrage de l'application...")
    
    try:
        # Configuration des logs pour réduire le bruit
        import logging
        logging.getLogger('kivy').setLevel(logging.ERROR)
        logging.getLogger('kivymd').setLevel(logging.ERROR)
        
        # Importer l'application
        from main import GesComApp
        
        print("   ✅ Modules importés avec succès")
        print("   🎯 Interface utilisateur prête")
        print("   📊 Mode graphiques texte activé")
        print("   🛡️ Configuration ultra-stable")
        
        print("\n🎉 GesComPro_LibTam - Démarrage réussi!")
        print("=" * 40)
        print("📋 Fonctionnalités disponibles:")
        print("   • 👥 Gestion des clients")
        print("   • 📦 Gestion des produits")
        print("   • 📁 Gestion des catégories")
        print("   • 💰 Gestion des ventes")
        print("   • 📊 Rapports (mode texte)")
        print("   • ⚙️ Paramètres")
        print("=" * 40)
        
        # Créer et lancer l'application
        app = GesComApp()
        app.run()
        
    except ImportError as e:
        print(f"\n❌ Erreur d'importation: {e}")
        print("\n🔧 SOLUTIONS:")
        print("   1. Vérifiez l'installation: pip install kivy kivymd")
        print("   2. Réinstallez les dépendances: pip install -r requirements.txt")
        print("   3. Vérifiez la version Python (3.8+ recommandé)")
        
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        
        # Diagnostic intelligent
        error_str = str(e).lower()
        
        if 'matplotlib' in error_str or 'figurecanvas' in error_str:
            print("\n✅ ERREUR MATPLOTLIB ÉVITÉE:")
            print("   Cette version utilise le mode graphiques texte")
            print("   L'erreur matplotlib a été contournée avec succès")
        
        elif 'kivymd' in error_str:
            print("\n🔧 ERREUR KIVYMD:")
            print("   1. Version recommandée: pip install kivymd==1.1.1")
            print("   2. Ou version récente: pip install kivymd")
        
        elif 'permission' in error_str or 'access' in error_str:
            print("\n🔧 ERREUR DE PERMISSIONS:")
            print("   1. Exécutez en tant qu'administrateur")
            print("   2. Vérifiez les permissions du dossier")
        
        else:
            print("\n🔧 DIAGNOSTIC GÉNÉRAL:")
            print("   1. Cette version est optimisée pour la stabilité")
            print("   2. Toutes les fonctionnalités principales sont disponibles")
            print("   3. Les graphiques sont en mode texte (plus stable)")
        
        print(f"\n📋 INFORMATIONS SYSTÈME:")
        print(f"   • Python: {sys.version.split()[0]}")
        print(f"   • Plateforme: {sys.platform}")
        print(f"   • Architecture: {sys.maxsize > 2**32 and '64-bit' or '32-bit'}")
        
        print(f"\n🔍 AIDE SUPPLÉMENTAIRE:")
        print("   • Documentation: README.md")
        print("   • Support: Vérifiez les logs d'erreur")
        print("   • Alternative: Utilisez launch_safe.py")
        
        input("\nAppuyez sur Entrée pour quitter...")


if __name__ == "__main__":
    main()
