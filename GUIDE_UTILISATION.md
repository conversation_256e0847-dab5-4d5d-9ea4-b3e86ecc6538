# Guide d'Utilisation - GesComPro

## 🚀 Démarrage Rapide

### Installation
1. Exécutez `python install.py` pour installer automatiquement l'application
2. Ou installez manuellement avec `pip install -r requirements.txt`

### Lancement
- **Windows**: Double-cliquez sur `GesComPro.bat`
- **Ligne de commande**: `python launch.py` ou `python main.py`

## 📱 Interface Utilisateur

### Navigation
- **Menu latéral**: Cliquez sur l'icône ☰ en haut à gauche
- **Thème**: Cliquez sur l'icône 🌓 pour basculer clair/sombre
- **Paramètres**: Cliquez sur l'icône ⚙️

### Écrans Principaux

#### 🏠 Tableau de Bord
- Vue d'ensemble des statistiques
- Ventes du jour et du mois
- Alertes de stock bas
- Actions rapides

#### 👥 Gestion des Clients
- **Ajouter**: Bouton "Nouveau Client"
- **Modifier**: Cliquez sur l'icône ✏️ sur une carte client
- **Supprimer**: Cliquez sur l'icône 🗑️
- **Rechercher**: Utilisez la barre de recherche

#### 📦 Gestion des Produits
- **Ajouter**: Bouton "Nouveau Produit"
- **Code-barres**: 
  - Génération automatique (icône 📊)
  - Validation en temps réel (icône ✓)
  - Recherche par code-barres (icône 🔍)
- **Stock bas**: Bouton "Stock bas" pour voir les alertes
- **Export**: Icône 📤 pour exporter la liste

#### 💰 Gestion des Ventes
- **Nouvelle vente**: Bouton "Nouvelle Vente"
- **Sélection client**: Menu déroulant
- **Ajout produits**: Recherche et sélection
- **Modes de paiement**: Espèces, CB, Chèque, Virement
- **Impression**: Génération automatique de facture PDF

#### 📊 Rapports
- **Périodes**: Jour, semaine, mois, trimestre, année
- **Graphiques**: Évolution des ventes, top produits/clients
- **Export PDF**: Génération de rapports détaillés

#### ⚙️ Paramètres
- **Entreprise**: Informations de votre société
- **Thème**: Personnalisation de l'interface
- **Sauvegarde**: Export/import des données
- **À propos**: Informations sur l'application

## 🔧 Fonctionnalités Avancées

### Codes-Barres
- **Types supportés**: EAN-13, EAN-8, Code 128, Personnalisé
- **Génération automatique**: Codes uniques garantis
- **Validation**: Vérification de format et d'unicité
- **Recherche**: Recherche rapide par code-barres

### Gestion des Stocks
- **Alertes automatiques**: Notification quand stock < seuil
- **Suivi en temps réel**: Mise à jour automatique lors des ventes
- **Historique**: Traçabilité des mouvements de stock

### Rapports et Exports
- **Formats**: CSV, Excel, PDF
- **Données exportables**: Clients, produits, ventes, rapports
- **Personnalisation**: Filtres par période, catégorie, etc.

### Sauvegarde et Restauration
- **Sauvegarde manuelle**: Bouton dans les paramètres
- **Export complet**: Toutes les données en un fichier
- **Restauration**: Import de données depuis une sauvegarde

## 📋 Utilisation Quotidienne

### Workflow Typique

1. **Matin**: 
   - Consulter le tableau de bord
   - Vérifier les alertes de stock
   - Consulter les ventes de la veille

2. **Pendant la journée**:
   - Enregistrer les ventes au fur et à mesure
   - Ajouter nouveaux clients si nécessaire
   - Mettre à jour les stocks si besoin

3. **Soir**:
   - Consulter les rapports du jour
   - Faire une sauvegarde si nécessaire
   - Préparer les commandes pour les produits en stock bas

### Conseils d'Utilisation

#### Gestion des Clients
- Remplissez au minimum nom et téléphone
- Utilisez le champ "Entreprise" pour les clients professionnels
- L'email permet l'envoi automatique de factures (futur)

#### Gestion des Produits
- Utilisez des références courtes et mémorisables
- Générez des codes-barres pour faciliter les ventes
- Définissez des stocks minimum réalistes
- Organisez par catégories

#### Gestion des Ventes
- Vérifiez toujours le client avant validation
- Utilisez la recherche par code-barres pour aller plus vite
- Imprimez ou envoyez la facture immédiatement

## 🔍 Recherche et Filtres

### Recherche Globale
- **Clients**: Par nom, prénom, entreprise, téléphone
- **Produits**: Par nom, référence, code-barres, catégorie
- **Ventes**: Par numéro, client, date

### Filtres Avancés
- **Par période**: Sélection de dates
- **Par statut**: Actif/inactif
- **Par catégorie**: Filtrage des produits
- **Par stock**: Stock bas, stock normal

## ⚠️ Résolution de Problèmes

### Problèmes Courants

#### L'application ne se lance pas
1. Vérifiez que Python 3.8+ est installé
2. Installez les dépendances: `pip install -r requirements.txt`
3. Vérifiez les permissions du dossier

#### Erreur de base de données
1. Vérifiez que le dossier `data/` existe
2. Supprimez `data/gescom.db` pour réinitialiser
3. Relancez l'application

#### Interface qui ne s'affiche pas
1. Mettez à jour les pilotes graphiques
2. Essayez de changer le thème
3. Vérifiez la résolution d'écran

#### Codes-barres invalides
1. Vérifiez le format (13 chiffres pour EAN-13)
2. Utilisez la génération automatique
3. Vérifiez l'unicité dans la base

### Logs et Débogage
- **Fichier de log**: `data/gescom.log`
- **Mode debug**: Lancez avec `python main.py --debug`
- **Test complet**: Exécutez `python test_app.py`

## 📞 Support

### Auto-diagnostic
1. Exécutez `python test_app.py`
2. Consultez le fichier `data/gescom.log`
3. Vérifiez les permissions des dossiers

### Sauvegarde Préventive
- Sauvegardez régulièrement via les Paramètres
- Conservez plusieurs versions de sauvegarde
- Testez la restauration périodiquement

### Mise à Jour
1. Sauvegardez vos données
2. Téléchargez la nouvelle version
3. Remplacez les fichiers (gardez le dossier `data/`)
4. Relancez l'application

## 🎯 Bonnes Pratiques

### Sécurité des Données
- Sauvegardez régulièrement
- Conservez les sauvegardes en lieu sûr
- Ne partagez pas le fichier de base de données

### Performance
- Nettoyez périodiquement les données anciennes
- Limitez le nombre de produits affichés
- Fermez l'application proprement

### Organisation
- Utilisez des catégories cohérentes
- Nommez les produits de façon claire
- Maintenez les informations clients à jour

---

**GesComPro v1.0.0** - Application de gestion commerciale moderne et intuitive 🚀