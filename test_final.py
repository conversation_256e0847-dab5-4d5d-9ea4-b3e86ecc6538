"""
Test final de l'application GesComPro_LibTam
Vérifie que l'application peut se lancer et que tous les composants fonctionnent
"""

import sys
import os
import time
import threading
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

def test_app_launch():
    """Test de lancement de l'application"""
    print("🚀 Test de lancement de l'application...")
    
    try:
        # Import des modules principaux
        from main import GesComApp
        from database.db_manager import DatabaseManager
        
        print("✅ Imports réussis")
        
        # Test de la base de données
        db_manager = DatabaseManager("data/test_gescom.db")
        if db_manager.initialize_database():
            print("✅ Base de données initialisée")
        else:
            print("❌ Erreur d'initialisation de la base de données")
            return False
        
        # Test de création de l'application (sans la lancer)
        app = GesComApp()
        print("✅ Application créée")
        
        # Test de construction de l'interface (sans affichage)
        try:
            root = app.build()
            print("✅ Interface construite")
        except Exception as e:
            print(f"❌ Erreur lors de la construction de l'interface: {e}")
            return False
        
        print("✅ Tous les tests de lancement réussis")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test de lancement: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_components():
    """Test des composants individuels"""
    print("\n🧪 Test des composants...")
    
    try:
        # Test des utilitaires
        from utils.barcode_utils import BarcodeGenerator, BarcodeValidator
        from utils.helpers import format_currency, validate_email
        from utils.pdf_generator import PDFGenerator
        
        # Test codes-barres
        barcode = BarcodeGenerator.generate_ean13()
        if BarcodeValidator.validate_ean13(barcode):
            print("✅ Codes-barres fonctionnels")
        else:
            print("❌ Problème avec les codes-barres")
            return False
        
        # Test helpers
        if format_currency(123.45) and validate_email("<EMAIL>"):
            print("✅ Fonctions utilitaires fonctionnelles")
        else:
            print("❌ Problème avec les fonctions utilitaires")
            return False
        
        # Test PDF
        pdf_gen = PDFGenerator()
        if pdf_gen:
            print("✅ Générateur PDF fonctionnel")
        else:
            print("❌ Problème avec le générateur PDF")
            return False
        
        print("✅ Tous les composants fonctionnent")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test des composants: {e}")
        return False

def test_screens():
    """Test des écrans"""
    print("\n📱 Test des écrans...")
    
    try:
        from screens.dashboard_screen import DashboardScreen
        from screens.clients_screen import ClientsScreen
        from screens.products_screen import ProductsScreen
        from screens.sales_screen import SalesScreen
        from screens.reports_screen import ReportsScreen
        from screens.settings_screen import SettingsScreen
        
        screens = [
            ("Dashboard", DashboardScreen),
            ("Clients", ClientsScreen),
            ("Produits", ProductsScreen),
            ("Ventes", SalesScreen),
            ("Rapports", ReportsScreen),
            ("Paramètres", SettingsScreen)
        ]
        
        for screen_name, screen_class in screens:
            try:
                screen = screen_class(name=screen_name.lower())
                print(f"✅ {screen_name} créé")
            except Exception as e:
                print(f"❌ Erreur avec {screen_name}: {e}")
                return False
        
        print("✅ Tous les écrans fonctionnent")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test des écrans: {e}")
        return False

def cleanup_test_files():
    """Nettoyer les fichiers de test"""
    test_files = [
        "data/test_gescom.db",
        "exports/test_report.pdf"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"🧹 Fichier de test supprimé: {file_path}")
            except:
                pass

def main():
    """Fonction principale"""
    print("🧪 Tests finaux de GesComPro_LibTam")
    print("=" * 50)
    
    # Créer les répertoires nécessaires
    os.makedirs("data", exist_ok=True)
    os.makedirs("exports", exist_ok=True)
    
    tests = [
        ("Lancement de l'application", test_app_launch),
        ("Composants", test_components),
        ("Écrans", test_screens)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 RÉSULTATS FINAUX")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:<25} {status}")
    
    all_passed = all(result for _, result in results)
    
    if all_passed:
        print("\n🎉 TOUS LES TESTS SONT RÉUSSIS!")
        print("🚀 L'application GesComPro_LibTam est prête à être utilisée!")
        print("\n📋 Pour lancer l'application:")
        print("   • Exécuter: python launch.py")
        print("   • Ou double-cliquer sur: GesComPro_LibTam.bat")
    else:
        print("\n⚠️ CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez les erreurs ci-dessus avant d'utiliser l'application")
    
    # Nettoyer les fichiers de test
    cleanup_test_files()
    
    return all_passed

if __name__ == "__main__":
    success = main()
    input("\nAppuyez sur Entrée pour continuer...")
    sys.exit(0 if success else 1)