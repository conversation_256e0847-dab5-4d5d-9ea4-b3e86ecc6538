#!/usr/bin/env python3
"""
Test du problème de modification de client
"""

import os
import sys
import tempfile

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def test_client_edit_issue():
    """Test pour reproduire le problème de modification"""
    print("🧪 Test du problème de modification de client")
    print("=" * 50)
    
    # Créer une base de données temporaire
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        test_db_path = tmp_file.name
    
    try:
        # Initialiser la base de données
        db_manager = DatabaseManager(test_db_path)
        if not db_manager.connect():
            print("❌ Erreur de connexion à la base de données")
            return False
        
        db_manager.initialize_database()
        print("✅ Base de données initialisée")
        
        # Ajouter un client de test
        client_id = db_manager.execute_insert("""
            INSERT INTO clients (nom, prenom, entreprise, email, telephone, adresse, ville, code_postal, pays)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, ('Dupont', 'Jean', 'SARL Dupont', '<EMAIL>', '0123456789', '123 Rue Test', 'Paris', '75001', 'France'))
        
        if not client_id:
            print("❌ Erreur lors de l'ajout du client test")
            return False
        
        print(f"✅ Client test ajouté (ID: {client_id})")
        
        # Récupérer le client (comme le fait l'application)
        clients = db_manager.execute_query("""
            SELECT * FROM clients WHERE id = ?
        """, (client_id,))
        
        if not clients:
            print("❌ Client non trouvé")
            return False
        
        client_data = clients[0]
        print(f"✅ Client récupéré: {type(client_data)}")
        
        # Tester l'accès aux données
        print("\n🔸 Test d'accès aux données:")
        try:
            print(f"  - ID: {client_data.get('id')} (type: {type(client_data.get('id'))})")
            print(f"  - Nom: {client_data.get('nom')} (type: {type(client_data.get('nom'))})")
            print(f"  - Prénom: {client_data.get('prenom')} (type: {type(client_data.get('prenom'))})")
            print(f"  - Email: {client_data.get('email')} (type: {type(client_data.get('email'))})")
            print("✅ Accès aux données réussi")
        except Exception as e:
            print(f"❌ Erreur d'accès aux données: {e}")
            return False
        
        # Tester la conversion en dictionnaire
        print("\n🔸 Test de conversion en dictionnaire:")
        try:
            client_dict = dict(client_data)
            print(f"✅ Conversion réussie: {client_dict}")
        except Exception as e:
            print(f"❌ Erreur de conversion: {e}")
            return False
        
        # Simuler le problème du dialog
        print("\n🔸 Test de création du dialog:")
        try:
            # Simuler ce que fait ClientFormDialog.__init__
            client_data_for_dialog = client_data or {}
            
            # Tester l'accès aux champs comme dans le dialog
            nom = client_data_for_dialog.get('nom', '')
            prenom = client_data_for_dialog.get('prenom', '')
            entreprise = client_data_for_dialog.get('entreprise', '')
            email = client_data_for_dialog.get('email', '')
            
            print(f"  - Nom pour dialog: '{nom}'")
            print(f"  - Prénom pour dialog: '{prenom}'")
            print(f"  - Entreprise pour dialog: '{entreprise}'")
            print(f"  - Email pour dialog: '{email}'")
            print("✅ Préparation des données pour dialog réussie")
            
        except Exception as e:
            print(f"❌ Erreur lors de la préparation du dialog: {e}")
            print(f"Type de client_data: {type(client_data)}")
            print(f"Contenu: {client_data}")
            return False
        
        print("\n🎉 TOUS LES TESTS RÉUSSIS!")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Nettoyer
        if db_manager:
            db_manager.disconnect()
        try:
            os.unlink(test_db_path)
            print("🧹 Fichier de test supprimé")
        except:
            pass

if __name__ == "__main__":
    print("🚀 Test du problème de modification de client")
    print("=" * 60)
    
    success = test_client_edit_issue()
    
    if success:
        print("\n✅ Le problème n'est pas dans l'accès aux données")
        print("🔍 Le problème doit être dans l'interface utilisateur")
    else:
        print("\n❌ Problème identifié dans l'accès aux données")
    
    print("\nAppuyez sur Entrée pour continuer...")
    input()