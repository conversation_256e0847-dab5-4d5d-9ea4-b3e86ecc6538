#!/usr/bin/env python3
"""
Debug spécifique du problème d'affichage du formulaire de vente
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

# Configuration pour Windows
if sys.platform == 'win32':
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel

# Import du formulaire
from forms.sales_form import SalesFormDialog


class DebugSalesDialogApp(MDApp):
    """Debug du formulaire de vente"""
    
    def build(self):
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="20dp"
        )
        
        title = MDLabel(
            text="🔍 DEBUG - Formulaire de Vente\nProblème: N'affiche que les boutons",
            font_style="H5",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Test avec debug détaillé
        debug_btn = MDRaisedButton(
            text="🔍 Test avec Debug Détaillé",
            size_hint_y=None,
            height="60dp",
            on_release=self.debug_dialog
        )
        
        self.result_label = MDLabel(
            text="Prêt pour le debug du formulaire",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(debug_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def debug_dialog(self, *args):
        """Debug détaillé du formulaire"""
        try:
            print("\n" + "="*60)
            print("🔍 DEBUG DÉTAILLÉ - Formulaire de Vente")
            print("="*60)
            
            # Étape 1: Création de l'instance
            print("1. 🏗️ Création de l'instance SalesFormDialog...")
            dialog = SalesFormDialog(on_save_callback=self.on_save_debug)
            print(f"   ✅ Instance créée: {dialog}")
            
            # Étape 2: Vérification des propriétés
            print("2. 🔍 Vérification des propriétés...")
            print(f"   📏 Taille: {dialog.size_hint}")
            print(f"   📐 Hauteur: {dialog.height}")
            print(f"   🏷️ Titre: {dialog.title}")
            print(f"   🎨 Type: {dialog.type}")
            
            # Étape 3: Vérification du contenu
            print("3. 📄 Vérification du contenu...")
            if hasattr(dialog, 'content_cls') and dialog.content_cls:
                print(f"   ✅ content_cls défini: {dialog.content_cls}")
                print(f"   📦 Type du contenu: {type(dialog.content_cls)}")
                
                # Vérifier les enfants du contenu
                if hasattr(dialog.content_cls, 'children'):
                    print(f"   👶 Nombre d'enfants: {len(dialog.content_cls.children)}")
                    for i, child in enumerate(dialog.content_cls.children):
                        print(f"      {i+1}. {type(child).__name__}")
                        
                        # Si c'est un MDBoxLayout, vérifier ses enfants
                        if hasattr(child, 'children') and len(child.children) > 0:
                            print(f"         📦 Sous-enfants: {len(child.children)}")
                            for j, subchild in enumerate(child.children):
                                print(f"            {j+1}. {type(subchild).__name__}")
                        else:
                            print(f"         ⚠️ Pas de sous-enfants ou vide")
                else:
                    print("   ⚠️ Pas d'attribut children")
            else:
                print("   ❌ content_cls NON DÉFINI ou VIDE")
            
            # Étape 4: Vérification des boutons
            print("4. 🔘 Vérification des boutons...")
            if hasattr(dialog, 'buttons') and dialog.buttons:
                print(f"   ✅ Boutons définis: {len(dialog.buttons)} boutons")
                for i, btn in enumerate(dialog.buttons):
                    print(f"      {i+1}. {btn.text}")
            else:
                print("   ❌ Boutons NON DÉFINIS")
            
            # Étape 5: Ouverture du dialog
            print("5. 🚀 Ouverture du dialog...")
            dialog.open()
            print("   ✅ Dialog ouvert")
            
            self.result_label.text = "🔍 Debug terminé !\n\n" \
                                   "Vérifiez la console pour les détails.\n" \
                                   "Le formulaire devrait maintenant s'afficher."
            
            print("="*60)
            print("🎯 RÉSULTAT: Vérifiez si le contenu s'affiche maintenant")
            print("="*60)
            
        except Exception as e:
            error_msg = f"❌ Erreur pendant le debug: {str(e)}"
            print(error_msg)
            self.result_label.text = error_msg
            import traceback
            traceback.print_exc()
    
    def on_save_debug(self, data):
        """Callback de debug"""
        print(f"💾 Callback appelé avec: {data}")
        self.result_label.text = "🎉 Callback de sauvegarde appelé !\nLe formulaire fonctionne."


def main():
    print("🔍 DEBUG - Problème d'affichage du formulaire de vente")
    print("OBJECTIF: Identifier pourquoi seuls les boutons s'affichent")
    
    try:
        app = DebugSalesDialogApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()