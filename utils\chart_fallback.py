
"""
Module de fallback pour les graphiques
"""

def create_simple_chart_widget(data, title="Graphique"):
    """Créer un widget graphique simple sans matplotlib"""
    from kivymd.uix.card import MDCard
    from kivymd.uix.boxlayout import MDBox<PERSON>ayout
    from kivymd.uix.label import MD<PERSON>abel
    from kivymd.uix.scrollview import MDScrollView
    
    card = MDCard(elevation=2, padding="16dp", size_hint_y=None, height="300dp")
    layout = MDBoxLayout(orientation='vertical', spacing="8dp")
    
    # Titre
    title_label = MDLabel(
        text=title,
        font_style="H6",
        theme_text_color="Primary",
        size_hint_y=None,
        height="32dp"
    )
    layout.add_widget(title_label)
    
    # Données en mode texte
    scroll = MDScrollView()
    data_layout = MDBoxLayout(orientation='vertical', spacing="4dp", adaptive_height=True)
    
    for item in data:
        item_label = MDLabel(
            text=str(item),
            size_hint_y=None,
            height="24dp"
        )
        data_layout.add_widget(item_label)
    
    scroll.add_widget(data_layout)
    layout.add_widget(scroll)
    
    card.add_widget(layout)
    return card
