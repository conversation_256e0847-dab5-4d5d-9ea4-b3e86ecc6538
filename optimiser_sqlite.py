#!/usr/bin/env python3
"""
Script d'optimisation SQLite pour GesComPro_LibTam
"""

import sqlite3
import os
from database.db_manager import DatabaseManager

def optimiser_sqlite():
    """Appliquer les optimisations SQLite recommandées"""
    print("⚡ OPTIMISATION DE LA BASE DE DONNÉES SQLITE")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    
    if not db_manager.connect():
        print("❌ Impossible de se connecter à la base de données")
        return False
    
    try:
        cursor = db_manager.connection.cursor()
        
        # 1. Configuration optimale
        print("🔧 Application de la configuration optimale...")
        
        optimizations = [
            ("PRAGMA journal_mode = WAL", "Mode journal WAL activé"),
            ("PRAGMA synchronous = NORMAL", "Synchronisation normale"),
            ("PRAGMA cache_size = 10000", "Cache mémoire augmenté"),
            ("PRAGMA temp_store = MEMORY", "Stockage temporaire en mémoire"),
            ("PRAGMA foreign_keys = ON", "Clés étrangères activées")
        ]
        
        for pragma, description in optimizations:
            cursor.execute(pragma)
            print(f"   ✅ {description}")
        
        # 2. Création des index
        print("\n📊 Création des index de performance...")
        
        indexes = [
            ("CREATE INDEX IF NOT EXISTS idx_produits_nom ON produits(nom)", "Index sur nom produit"),
            ("CREATE INDEX IF NOT EXISTS idx_produits_reference ON produits(reference)", "Index sur référence"),
            ("CREATE INDEX IF NOT EXISTS idx_produits_categorie ON produits(categorie_id)", "Index sur catégorie"),
            ("CREATE INDEX IF NOT EXISTS idx_ventes_date ON ventes(date_vente)", "Index sur date vente"),
            ("CREATE INDEX IF NOT EXISTS idx_ventes_client ON ventes(client_id)", "Index sur client"),
            ("CREATE INDEX IF NOT EXISTS idx_clients_nom ON clients(nom)", "Index sur nom client"),
            ("CREATE INDEX IF NOT EXISTS idx_categories_nom ON categories(nom)", "Index sur nom catégorie")
        ]
        
        for sql, description in indexes:
            try:
                cursor.execute(sql)
                print(f"   ✅ {description}")
            except sqlite3.Error as e:
                print(f"   ⚠️ {description}: {e}")
        
        # 3. Maintenance
        print("\n💾 Maintenance de la base de données...")
        
        cursor.execute("VACUUM")
        print("   ✅ Défragmentation effectuée")
        
        cursor.execute("ANALYZE")
        print("   ✅ Statistiques mises à jour")
        
        db_manager.connection.commit()
        print("\n🎉 Optimisation terminée avec succès!")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'optimisation: {e}")
        return False
    
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    optimiser_sqlite()
