"""
Formulaire CRUD pour les catégories - Version corrigée
"""

from kivymd.uix.dialog import MDDialog
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import <PERSON><PERSON>abel
from kivymd.uix.textfield import <PERSON><PERSON><PERSON>t<PERSON>ield
from kivymd.uix.button import MDRaisedButton, MDFlatButton
from kivymd.uix.snackbar import MDSnackbar
from models.category_model import CategoryModel
from datetime import datetime


class CategoryFormDialog(MDDialog):
    """Formulaire pour créer/modifier une catégorie - Version simplifiée"""
    
    def __init__(self, category_data=None, on_save_callback=None, **kwargs):
        self.category_data = category_data or {}
        self.on_save_callback = on_save_callback
        self.category_model = CategoryModel()
        
        # Déterminer le mode (création ou modification)
        self.is_edit_mode = bool(self.category_data and self.category_data.get('id'))
        
        # C<PERSON>er le contenu du formulaire
        content = self.create_content()
        
        # Créer les boutons
        cancel_btn = MDFlatButton(
            text="❌ Annuler",
            on_release=self.dismiss_dialog
        )
        
        save_btn = MDRaisedButton(
            text="💾 Enregistrer",
            on_release=self.save_category
        )
        
        super().__init__(
            title="✏️ Modifier la catégorie" if self.is_edit_mode else "🆕 Nouvelle catégorie",
            type="custom",
            content_cls=content,
            size_hint=(0.9, None),
            height="400dp",
            buttons=[cancel_btn, save_btn],
            **kwargs
        )
    
    def create_content(self):
        """Créer le contenu du formulaire"""
        # Layout principal
        main_layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="300dp"
        )
        
        # En-tête
        if self.is_edit_mode:
            header_text = f"📁 Modification de la catégorie\n🆔 ID: {self.category_data.get('id', 'N/A')}"
        else:
            header_text = "📁 Création d'une nouvelle catégorie"
        
        header = MDLabel(
            text=header_text,
            font_style="Subtitle1",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        # Champ nom
        nom_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="80dp"
        )
        
        nom_label = MDLabel(
            text="📝 Nom de la catégorie (OBLIGATOIRE)",
            font_style="Body2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        self.nom_field = MDTextField(
            text=self.category_data.get('nom', ''),
            hint_text="Saisissez le nom de la catégorie",
            mode="rectangle",
            size_hint_y=None,
            height="56dp",
            required=True
        )
        
        nom_layout.add_widget(nom_label)
        nom_layout.add_widget(self.nom_field)
        
        # Champ description
        desc_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="100dp"
        )
        
        desc_label = MDLabel(
            text="📄 Description (OPTIONNELLE)",
            font_style="Body2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        self.description_field = MDTextField(
            text=self.category_data.get('description', ''),
            hint_text="Description de la catégorie (optionnel)",
            mode="rectangle",
            multiline=True,
            size_hint_y=None,
            height="76dp"
        )
        
        desc_layout.add_widget(desc_label)
        desc_layout.add_widget(self.description_field)
        
        # Informations supplémentaires (si modification)
        info_layout = MDBoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height="60dp" if self.is_edit_mode else "0dp"
        )
        
        if self.is_edit_mode:
            date_creation = self.category_data.get('date_creation', '')
            if date_creation:
                try:
                    if isinstance(date_creation, str):
                        date_obj = datetime.fromisoformat(date_creation.replace('Z', '+00:00'))
                        date_formatted = date_obj.strftime('%d/%m/%Y %H:%M')
                    else:
                        date_formatted = str(date_creation)[:16]
                except:
                    date_formatted = str(date_creation)[:16]
            else:
                date_formatted = "Non définie"
            
            info_label = MDLabel(
                text=f"ℹ️ Créée le: {date_formatted}",
                font_style="Caption",
                theme_text_color="Secondary",
                halign="center",
                size_hint_y=None,
                height="60dp"
            )
            info_layout.add_widget(info_label)
        
        # Assembler le layout
        main_layout.add_widget(header)
        main_layout.add_widget(nom_layout)
        main_layout.add_widget(desc_layout)
        main_layout.add_widget(info_layout)
        
        return main_layout
    
    def validate_form(self):
        """Valider le formulaire"""
        errors = []
        
        # Vérifier le nom (OBLIGATOIRE)
        if not self.nom_field.text.strip():
            errors.append("Le nom de la catégorie est obligatoire")
        elif len(self.nom_field.text.strip()) < 2:
            errors.append("Le nom doit contenir au moins 2 caractères")
        
        return errors
    
    def save_category(self, *args):
        """Sauvegarder la catégorie"""
        # Validation
        errors = self.validate_form()
        if errors:
            self.show_error("Erreurs de validation:\n" + "\n".join(f"• {error}" for error in errors))
            return
        
        try:
            # Préparer les données
            category_data = {
                'nom': self.nom_field.text.strip(),
                'description': self.description_field.text.strip()
            }
            
            if self.is_edit_mode:
                # Modification
                success = self.category_model.update_category(
                    self.category_data['id'], 
                    category_data
                )
                
                if success:
                    self.show_success("Catégorie modifiée avec succès")
                    result_data = {
                        'id': self.category_data['id'],
                        **category_data
                    }
                else:
                    self.show_error("Erreur lors de la modification")
                    return
            else:
                # Création
                category_id = self.category_model.create_category(category_data)
                
                if category_id:
                    self.show_success("Catégorie créée avec succès")
                    result_data = {
                        'id': category_id,
                        **category_data
                    }
                else:
                    self.show_error("Erreur lors de la création")
                    return
            
            # Callback avec les données
            if self.on_save_callback:
                self.on_save_callback(result_data)
            
            self.dismiss()
            
        except ValueError as e:
            self.show_error(str(e))
        except Exception as e:
            self.show_error(f"Erreur inattendue: {str(e)}")
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"❌ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"❌ {message}")
    
    def show_success(self, message):
        """Afficher un message de succès"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"✅ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"✅ {message}")
    
    def dismiss_dialog(self, *args):
        """Fermer le dialog"""
        self.dismiss()