#!/usr/bin/env python3
"""
Lanceur simplifié pour GesComPro_LibTam
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from database.db_manager import DatabaseManager

class GesComSimpleApp(MDApp):
    def build(self):
        self.title = "GesComPro_LibTam - Gestion Commerciale"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Initialiser la base de données
        self.db_manager = DatabaseManager()
        
        screen = MDScreen()
        layout = MDBoxLayout(
            orientation='vertical', 
            padding="20dp", 
            spacing="20dp"
        )
        
        # Titre principal
        title = MDLabel(
            text="🏪 GesComPro_LibTam",
            font_style="H3",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        subtitle = MDLabel(
            text="Système de Gestion Commerciale Professionnel",
            font_style="H6",
            halign="center",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="40dp"
        )
        
        # Grille de boutons pour les modules
        buttons_grid = MDGridLayout(
            cols=2,
            spacing="20dp",
            size_hint_y=None,
            height="400dp",
            adaptive_height=True
        )
        
        # Boutons des modules
        modules = [
            ("📊 Tableau de Bord", "dashboard", self.open_dashboard),
            ("👥 Clients", "clients", self.open_clients),
            ("📦 Produits", "products", self.open_products),
            ("🛒 Ventes", "sales", self.open_sales),
            ("📈 Rapports", "reports", self.open_reports),
            ("⚙️ Paramètres", "settings", self.open_settings)
        ]
        
        for text, icon, callback in modules:
            card = MDCard(
                elevation=3,
                padding="16dp",
                size_hint=(None, None),
                size=("200dp", "120dp")
            )
            
            card_layout = MDBoxLayout(
                orientation='vertical',
                spacing="10dp"
            )
            
            btn = MDRaisedButton(
                text=text,
                size_hint=(1, None),
                height="60dp",
                on_release=callback
            )
            
            card_layout.add_widget(btn)
            card.add_widget(card_layout)
            buttons_grid.add_widget(card)
        
        # Informations sur l'application
        info_label = MDLabel(
            text="✅ Système de ventes activé | Devise: Dirham (DH) | Version: 1.0",
            halign="center",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="40dp"
        )
        
        layout.add_widget(title)
        layout.add_widget(subtitle)
        layout.add_widget(buttons_grid)
        layout.add_widget(info_label)
        
        screen.add_widget(layout)
        return screen
    
    def open_dashboard(self, *args):
        """Ouvrir le tableau de bord"""
        try:
            from screens.dashboard_screen import DashboardScreen
            dashboard = DashboardScreen()
            self.root.clear_widgets()
            self.root.add_widget(dashboard)
        except Exception as e:
            print(f"❌ Erreur dashboard: {e}")
    
    def open_clients(self, *args):
        """Ouvrir l'écran des clients"""
        try:
            from screens.clients_screen import ClientsScreen
            clients = ClientsScreen()
            self.root.clear_widgets()
            self.root.add_widget(clients)
        except Exception as e:
            print(f"❌ Erreur clients: {e}")
    
    def open_products(self, *args):
        """Ouvrir l'écran des produits"""
        try:
            from screens.products_screen import ProductsScreen
            products = ProductsScreen()
            self.root.clear_widgets()
            self.root.add_widget(products)
        except Exception as e:
            print(f"❌ Erreur produits: {e}")
    
    def open_sales(self, *args):
        """Ouvrir l'écran des ventes"""
        try:
            print("🛒 Ouverture de l'écran des ventes...")
            from screens.sales_screen import SalesScreen
            sales = SalesScreen()
            self.root.clear_widgets()
            self.root.add_widget(sales)
            print("✅ Écran des ventes ouvert")
        except Exception as e:
            print(f"❌ Erreur ventes: {e}")
            import traceback
            traceback.print_exc()
    
    def open_reports(self, *args):
        """Ouvrir l'écran des rapports"""
        try:
            from screens.reports_screen import ReportsScreen
            reports = ReportsScreen()
            self.root.clear_widgets()
            self.root.add_widget(reports)
        except Exception as e:
            print(f"❌ Erreur rapports: {e}")
    
    def open_settings(self, *args):
        """Ouvrir l'écran des paramètres"""
        try:
            from screens.settings_screen import SettingsScreen
            settings = SettingsScreen()
            self.root.clear_widgets()
            self.root.add_widget(settings)
        except Exception as e:
            print(f"❌ Erreur paramètres: {e}")

if __name__ == "__main__":
    print("🚀 LANCEMENT DE GESCOMPRO_LIBTAM")
    print("=" * 50)
    print("🏪 Système de Gestion Commerciale")
    print("✅ Toutes les fonctionnalités activées")
    print("🛒 Système de ventes opérationnel")
    print("🪙 Devise: Dirham Marocain (DH)")
    print("=" * 50)
    
    try:
        app = GesComSimpleApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()
        input("\nAppuyez sur Entrée pour quitter...")