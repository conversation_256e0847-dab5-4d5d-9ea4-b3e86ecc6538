#!/usr/bin/env python3
"""
Vérifier la structure de la table clients
"""

from database.db_manager import DatabaseManager

def check_clients_table():
    """Vérifier la structure de la table clients"""
    db_manager = DatabaseManager()
    
    try:
        if not db_manager.connect():
            print("❌ Impossible de se connecter à la base de données")
            return
        
        # Vérifier la structure de la table clients
        print("🔍 Structure de la table clients :")
        structure = db_manager.execute_query("PRAGMA table_info(clients)")
        
        if structure:
            print("Colonnes trouvées :")
            for col in structure:
                print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        else:
            print("❌ Table clients non trouvée")
            return
        
        # Vérifier quelques clients
        print("\n📋 Quelques clients dans la table :")
        clients = db_manager.execute_query("SELECT * FROM clients LIMIT 5")
        
        if clients:
            for client in clients:
                print(f"  - ID: {client.get('id')}, Nom: {client.get('nom', 'N/A')}, Prénom: {client.get('prenom', 'N/A')}")
        else:
            print("❌ Aucun client trouvé")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        db_manager.close()

if __name__ == '__main__':
    check_clients_table()