"""
Comparaison des performances avant/après optimisation
"""

import os
import sys
import time
import sqlite3
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))


def test_connexion_basique():
    """Tester la connexion basique SQLite (avant optimisation)"""
    print("🐌 TEST CONNEXION BASIQUE (AVANT)")
    print("-" * 35)
    
    db_path = "data/gescom.db"
    
    start_time = time.time()
    connections = []
    
    for i in range(10):
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        connections.append(conn)
    
    connection_time = time.time() - start_time
    print(f"   📊 10 connexions basiques: {connection_time:.3f}s")
    
    # Test requêtes basiques
    start_time = time.time()
    for i in range(100):
        cursor = connections[0].cursor()
        cursor.execute("SELECT COUNT(*) FROM categories")
        cursor.fetchone()
    
    query_time = time.time() - start_time
    print(f"   📊 100 requêtes basiques: {query_time:.3f}s")
    
    # Fermer les connexions
    for conn in connections:
        conn.close()
    
    return connection_time, query_time


def test_connexion_optimisee():
    """Tester la connexion optimisée (après optimisation)"""
    print("\n🚀 TEST CONNEXION OPTIMISÉE (APRÈS)")
    print("-" * 35)
    
    from database.db_manager import DatabaseManager
    
    start_time = time.time()
    managers = []
    
    for i in range(10):
        db = DatabaseManager()
        if db.connect():
            managers.append(db)
    
    connection_time = time.time() - start_time
    print(f"   📊 10 connexions optimisées: {connection_time:.3f}s")
    
    # Test requêtes optimisées
    if managers:
        start_time = time.time()
        for i in range(100):
            result = managers[0].execute_query("SELECT COUNT(*) FROM categories")
        
        query_time = time.time() - start_time
        print(f"   📊 100 requêtes optimisées: {query_time:.3f}s")
    else:
        query_time = float('inf')
    
    # Fermer les connexions
    for db in managers:
        db.disconnect()
    
    return connection_time, query_time


def test_insertions_comparaison():
    """Comparer les performances d'insertion"""
    print("\n💾 COMPARAISON DES INSERTIONS")
    print("-" * 35)
    
    # Test insertion basique
    db_path = "data/gescom.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    start_time = time.time()
    for i in range(20):
        cursor.execute(
            "INSERT INTO categories (nom, description) VALUES (?, ?)",
            (f'Test_Basic_{i}', f'Description basique {i}')
        )
        conn.commit()  # Commit individuel
    
    basic_time = time.time() - start_time
    print(f"   🐌 20 insertions basiques: {basic_time:.3f}s")
    
    # Nettoyer
    cursor.execute("DELETE FROM categories WHERE nom LIKE 'Test_Basic_%'")
    conn.commit()
    conn.close()
    
    # Test insertion optimisée
    from database.db_manager import DatabaseManager
    db = DatabaseManager()
    db.connect()
    
    start_time = time.time()
    batch_data = []
    for i in range(20):
        batch_data.append((f'Test_Optimized_{i}', f'Description optimisée {i}'))
    
    db.execute_batch(
        "INSERT INTO categories (nom, description) VALUES (?, ?)",
        batch_data
    )
    
    optimized_time = time.time() - start_time
    print(f"   🚀 20 insertions optimisées: {optimized_time:.3f}s")
    
    # Nettoyer
    db.execute_update("DELETE FROM categories WHERE nom LIKE 'Test_Optimized_%'")
    db.disconnect()
    
    return basic_time, optimized_time


def calculer_ameliorations(avant, apres):
    """Calculer le pourcentage d'amélioration"""
    if avant > 0 and apres > 0:
        improvement = ((avant - apres) / avant) * 100
        speedup = avant / apres
        return improvement, speedup
    return 0, 1


def main():
    """Fonction principale de comparaison"""
    print("⚡ COMPARAISON DES PERFORMANCES DE LA BASE DE DONNÉES")
    print("=" * 60)
    print("🎯 Mesure de l'impact des optimisations appliquées")
    print("=" * 60)
    
    try:
        # Tests de connexion
        basic_conn_time, basic_query_time = test_connexion_basique()
        opt_conn_time, opt_query_time = test_connexion_optimisee()
        
        # Tests d'insertion
        basic_insert_time, opt_insert_time = test_insertions_comparaison()
        
        # Calculs des améliorations
        print("\n📊 RÉSULTATS DE LA COMPARAISON")
        print("=" * 40)
        
        # Connexions
        conn_improvement, conn_speedup = calculer_ameliorations(basic_conn_time, opt_conn_time)
        print(f"🔗 CONNEXIONS:")
        print(f"   Avant: {basic_conn_time:.3f}s")
        print(f"   Après: {opt_conn_time:.3f}s")
        print(f"   Amélioration: {conn_improvement:.1f}%")
        print(f"   Accélération: {conn_speedup:.1f}x plus rapide")
        
        # Requêtes
        query_improvement, query_speedup = calculer_ameliorations(basic_query_time, opt_query_time)
        print(f"\n📊 REQUÊTES:")
        print(f"   Avant: {basic_query_time:.3f}s")
        print(f"   Après: {opt_query_time:.3f}s")
        print(f"   Amélioration: {query_improvement:.1f}%")
        print(f"   Accélération: {query_speedup:.1f}x plus rapide")
        
        # Insertions
        insert_improvement, insert_speedup = calculer_ameliorations(basic_insert_time, opt_insert_time)
        print(f"\n💾 INSERTIONS:")
        print(f"   Avant: {basic_insert_time:.3f}s")
        print(f"   Après: {opt_insert_time:.3f}s")
        print(f"   Amélioration: {insert_improvement:.1f}%")
        print(f"   Accélération: {insert_speedup:.1f}x plus rapide")
        
        # Résumé global
        print(f"\n🎉 RÉSUMÉ GLOBAL")
        print("=" * 25)
        
        avg_improvement = (conn_improvement + query_improvement + insert_improvement) / 3
        avg_speedup = (conn_speedup + query_speedup + insert_speedup) / 3
        
        print(f"📈 Amélioration moyenne: {avg_improvement:.1f}%")
        print(f"🚀 Accélération moyenne: {avg_speedup:.1f}x")
        
        # Recommandations
        print(f"\n💡 RECOMMANDATIONS")
        print("-" * 20)
        
        if avg_improvement > 50:
            print("✅ Optimisations très efficaces!")
            print("🎯 Utilisez 'launch_optimized_db.py' pour de meilleures performances")
        elif avg_improvement > 20:
            print("✅ Optimisations efficaces")
            print("🔧 Continuez à utiliser les optimisations")
        else:
            print("⚠️ Optimisations modérées")
            print("🔍 Analysez d'autres goulots d'étranglement")
        
        print(f"\n🛠️ OUTILS DISPONIBLES:")
        print("   • optimiser_db_automatique.py - Optimisation complète")
        print("   • test_performance_db.py - Tests de performance")
        print("   • launch_optimized_db.py - Lancement optimisé")
        
    except Exception as e:
        print(f"❌ Erreur lors de la comparaison: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
