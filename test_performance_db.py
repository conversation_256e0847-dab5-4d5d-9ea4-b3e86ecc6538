"""
Test des performances de la base de données optimisée
"""

import os
import sys
import time
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

from database.db_manager import DatabaseManager


def test_connection_performance():
    """Tester les performances de connexion"""
    print("🔗 TEST DES PERFORMANCES DE CONNEXION")
    print("-" * 40)
    
    # Test connexions multiples (avant optimisation)
    start_time = time.time()
    managers = []
    for i in range(10):
        db = DatabaseManager()
        if db.connect():
            managers.append(db)
    
    connection_time = time.time() - start_time
    print(f"✅ 10 connexions en {connection_time:.3f}s")
    
    # Fermer les connexions
    for db in managers:
        db.disconnect()
    
    return connection_time


def test_query_performance():
    """Tester les performances des requêtes"""
    print("\n📊 TEST DES PERFORMANCES DE REQUÊTES")
    print("-" * 40)
    
    db = DatabaseManager()
    if not db.connect():
        print("❌ Impossible de se connecter")
        return
    
    # Initialiser la base
    db.initialize_database()
    
    # Test requêtes simples
    start_time = time.time()
    for i in range(100):
        categories = db.execute_query("SELECT COUNT(*) as count FROM categories")
    query_time = time.time() - start_time
    print(f"✅ 100 requêtes COUNT en {query_time:.3f}s")
    
    # Test requêtes complexes
    start_time = time.time()
    for i in range(10):
        products = db.execute_query("""
            SELECT p.*, c.nom as categorie_nom 
            FROM produits p 
            LEFT JOIN categories c ON p.categorie_id = c.id 
            WHERE p.actif = 1
        """)
    complex_query_time = time.time() - start_time
    print(f"✅ 10 requêtes JOIN en {complex_query_time:.3f}s")
    
    db.disconnect()
    return query_time, complex_query_time


def test_insert_performance():
    """Tester les performances d'insertion"""
    print("\n💾 TEST DES PERFORMANCES D'INSERTION")
    print("-" * 40)
    
    db = DatabaseManager()
    if not db.connect():
        print("❌ Impossible de se connecter")
        return
    
    # Test insertions individuelles
    start_time = time.time()
    for i in range(50):
        category_data = {
            'nom': f'Test_Category_{i}',
            'description': f'Description de test {i}'
        }
        db.execute_insert(
            "INSERT INTO categories (nom, description) VALUES (?, ?)",
            (category_data['nom'], category_data['description'])
        )
    
    individual_insert_time = time.time() - start_time
    print(f"✅ 50 insertions individuelles en {individual_insert_time:.3f}s")
    
    # Test insertion en lot
    start_time = time.time()
    batch_data = []
    for i in range(50, 100):
        batch_data.append((f'Batch_Category_{i}', f'Description batch {i}'))
    
    db.execute_batch(
        "INSERT INTO categories (nom, description) VALUES (?, ?)",
        batch_data
    )
    
    batch_insert_time = time.time() - start_time
    print(f"✅ 50 insertions en lot en {batch_insert_time:.3f}s")
    
    # Nettoyer les données de test
    db.execute_update("DELETE FROM categories WHERE nom LIKE 'Test_Category_%' OR nom LIKE 'Batch_Category_%'")
    
    db.disconnect()
    return individual_insert_time, batch_insert_time


def test_transaction_performance():
    """Tester les performances des transactions"""
    print("\n🔄 TEST DES PERFORMANCES DE TRANSACTIONS")
    print("-" * 40)
    
    db = DatabaseManager()
    if not db.connect():
        print("❌ Impossible de se connecter")
        return
    
    # Test sans transaction explicite
    start_time = time.time()
    for i in range(20):
        db.execute_insert(
            "INSERT INTO categories (nom, description) VALUES (?, ?)",
            (f'NoTrans_{i}', f'Sans transaction {i}')
        )
    no_trans_time = time.time() - start_time
    print(f"✅ 20 insertions sans transaction en {no_trans_time:.3f}s")
    
    # Test avec transaction explicite
    start_time = time.time()
    db.begin_transaction()
    for i in range(20):
        db.execute_insert(
            "INSERT INTO categories (nom, description) VALUES (?, ?)",
            (f'WithTrans_{i}', f'Avec transaction {i}')
        )
    db.commit_transaction()
    trans_time = time.time() - start_time
    print(f"✅ 20 insertions avec transaction en {trans_time:.3f}s")
    
    # Nettoyer
    db.execute_update("DELETE FROM categories WHERE nom LIKE 'NoTrans_%' OR nom LIKE 'WithTrans_%'")
    
    db.disconnect()
    return no_trans_time, trans_time


def main():
    """Fonction principale de test"""
    print("⚡ TEST DES PERFORMANCES DE LA BASE DE DONNÉES OPTIMISÉE")
    print("=" * 60)
    
    try:
        # Tests de performance
        conn_time = test_connection_performance()
        query_times = test_query_performance()
        insert_times = test_insert_performance()
        trans_times = test_transaction_performance()
        
        # Résumé
        print("\n📋 RÉSUMÉ DES PERFORMANCES")
        print("=" * 30)
        print(f"🔗 Connexions: {conn_time:.3f}s pour 10 connexions")
        if query_times:
            print(f"📊 Requêtes simples: {query_times[0]:.3f}s pour 100 requêtes")
            print(f"📊 Requêtes complexes: {query_times[1]:.3f}s pour 10 requêtes")
        if insert_times:
            print(f"💾 Insertions individuelles: {insert_times[0]:.3f}s pour 50 insertions")
            print(f"💾 Insertions en lot: {insert_times[1]:.3f}s pour 50 insertions")
            improvement = ((insert_times[0] - insert_times[1]) / insert_times[0]) * 100
            print(f"🚀 Amélioration lot vs individuel: {improvement:.1f}%")
        if trans_times:
            print(f"🔄 Sans transaction: {trans_times[0]:.3f}s pour 20 insertions")
            print(f"🔄 Avec transaction: {trans_times[1]:.3f}s pour 20 insertions")
        
        print("\n✅ Tests de performance terminés!")
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
