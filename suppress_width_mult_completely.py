#!/usr/bin/env python3
"""
Script pour supprimer complètement l'avertissement width_mult
"""

import os
import sys
import warnings

# Supprimer TOUS les avertissements liés à width_mult
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)
warnings.filterwarnings("ignore", message=".*width_mult.*", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*width_mult.*", category=FutureWarning)

# Supprimer aussi les avertissements KivyMD généraux
warnings.filterwarnings("ignore", message=".*deprecated.*", category=UserWarning)
warnings.filterwarnings("ignore", message=".*Deprecated property.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer TOUS les avertissements
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)
logging.getLogger('kivymd').setLevel(logging.ERROR)

# Configuration pour Windows
if sys.platform == 'win32':
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'

print("🔇 SUPPRESSION COMPLÈTE DES AVERTISSEMENTS width_mult")
print("Lancement de l'application principale...")
print("="*60)

try:
    # Import et lancement de l'application principale
    from main import GesComApp
    
    print("✅ Import réussi")
    print("🚀 Lancement de l'application...")
    
    app = GesComApp()
    app.run()
    
except Exception as e:
    print(f"❌ Erreur: {e}")
    import traceback
    traceback.print_exc()