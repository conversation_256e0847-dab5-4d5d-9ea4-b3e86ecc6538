#!/usr/bin/env python3
"""
Test de la liste déroulante client dans le formulaire de vente
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

# Configuration pour Windows
if sys.platform == 'win32':
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel

# Import du formulaire avec liste déroulante client
from forms.sales_form import SalesFormDialog


class TestClientDropdownApp(MDApp):
    """Test de la liste déroulante client"""
    
    def build(self):
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        title = MDLabel(
            text="🔽 Test Liste Déroulante\nClient dans Formulaire de Vente",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Test formulaire avec liste déroulante
        dropdown_btn = MDRaisedButton(
            text="🔽 Formulaire avec Liste Déroulante Client",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_dropdown_form
        )
        
        self.result_label = MDLabel(
            text="Test de la liste déroulante client\n\n" \
                 "Vérifiez que :\n" \
                 "• Le champ client est une carte cliquable\n" \
                 "• Cliquer ouvre une liste déroulante\n" \
                 "• Sélectionner un client met à jour l'affichage",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(dropdown_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_dropdown_form(self, *args):
        """Tester le formulaire avec liste déroulante client"""
        try:
            dialog = SalesFormDialog(
                on_save_callback=self.on_save
            )
            dialog.open()
            
            self.result_label.text = "🔽 Formulaire avec liste déroulante ouvert !\n\n" \
                                   "VÉRIFICATIONS :\n" \
                                   "✅ Section Client : Carte cliquable\n" \
                                   "✅ Icône flèche : Visible à droite\n" \
                                   "✅ Clic sur carte : Ouvre la liste\n" \
                                   "✅ Sélection : Met à jour l'affichage\n" \
                                   "✅ Clients disponibles : Base ou test\n\n" \
                                   "Testez la sélection d'un client !"
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur formulaire liste déroulante :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def on_save(self, data):
        """Callback de test"""
        client_info = "Non sélectionné"
        if 'client_id' in data:
            client_info = f"ID {data['client_id']}"
        
        self.result_label.text = f"🎉 Test de sauvegarde réussi !\n\n" \
                               f"Client sélectionné : {client_info}\n" \
                               f"La liste déroulante fonctionne parfaitement !"


def main():
    print("🔽 Test Liste Déroulante Client - Formulaire de Vente")
    print("OBJECTIF: Vérifier que le champ client est une vraie liste déroulante")
    
    try:
        app = TestClientDropdownApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()