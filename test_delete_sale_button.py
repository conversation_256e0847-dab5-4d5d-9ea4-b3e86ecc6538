#!/usr/bin/env python3
"""
Test du bouton de suppression des ventes en cours
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

# Configuration pour Windows
if sys.platform == 'win32':
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel

# Import de l'écran des ventes
from screens.sales_screen import SalesScreen


class TestDeleteSaleButtonApp(MDApp):
    """Test du bouton de suppression des ventes"""
    
    def build(self):
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        title = MDLabel(
            text="🗑️ Test Bouton Suppression\nVentes En Cours",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Test écran des ventes avec suppression
        sales_btn = MDRaisedButton(
            text="🛒 Écran Ventes avec Suppression",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_sales_screen
        )
        
        self.result_label = MDLabel(
            text="Test du bouton de suppression des ventes\n\n" \
                 "Fonctionnalités à tester :\n" \
                 "• Bouton 🗑️ visible uniquement pour ventes 'En cours'\n" \
                 "• Dialogue de confirmation avant suppression\n" \
                 "• Suppression en base avec restauration stock\n" \
                 "• Mise à jour automatique de la liste\n" \
                 "• Messages de succès/erreur",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(sales_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_sales_screen(self, *args):
        """Tester l'écran des ventes avec suppression"""
        try:
            # Créer et afficher l'écran des ventes
            sales_screen = SalesScreen()
            
            # Remplacer l'écran actuel
            self.root.clear_widgets()
            self.root.add_widget(sales_screen)
            
            self.result_label.text = "🛒 Écran des ventes ouvert !\n\n" \
                                   "VÉRIFICATIONS :\n" \
                                   "✅ Cartes de ventes affichées\n" \
                                   "✅ Bouton 🗑️ visible pour ventes 'En cours'\n" \
                                   "✅ Bouton 🗑️ absent pour ventes 'Payée/Annulée'\n" \
                                   "✅ Clic sur 🗑️ → Dialogue de confirmation\n" \
                                   "✅ Confirmation → Suppression + restauration stock\n" \
                                   "✅ Annulation → Aucune action\n\n" \
                                   "Testez la suppression d'une vente en cours !"
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur écran ventes :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()


def main():
    print("🗑️ Test Bouton Suppression Ventes En Cours")
    print("OBJECTIF: Vérifier la suppression sécurisée des ventes en cours")
    
    try:
        app = TestDeleteSaleButtonApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()