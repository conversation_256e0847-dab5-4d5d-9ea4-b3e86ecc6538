# GesComPro_LibTam - Architecture Overview

## Application Architecture

### High-Level Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    GesComApp (MDApp)                        │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              Screen Manager                             ││
│  │  ┌─────────┬─────────┬─────────┬─────────┬─────────────┐││
│  │  │Dashboard│ Clients │Products │Categories│   Sales     │││
│  │  │ Screen  │ Screen  │ Screen  │ Screen   │   Screen    │││
│  │  └─────────┴─────────┴─────────┴─────────┴─────────────┘││
│  │  ┌─────────┬─────────────────────────────────────────────┐││
│  │  │Reports  │           Settings Screen                  │││
│  │  │ Screen  │                                             │││
│  │  └─────────┴─────────────────────────────────────────────┘││
│  └─────────────────────────────────────────────────────────┘│
│  ┌─────────────────────────────────────────────────────────┐│
│  │              Navigation Drawer                          ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                DatabaseManager (Singleton)                 │
│  ┌─────────────────────────────────────────────────────────┐│
│  │                SQLite Database                          ││
│  │  ┌─────────┬─────────┬─────────┬─────────┬─────────────┐││
│  │  │ clients │produits │categories│ ventes  │lignes_vente │││
│  │  └─────────┴─────────┴─────────┴─────────┴─────────────┘││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Main Application (main.py)
**Class**: `GesComApp(MDApp)`
- **Responsibilities**:
  - Application lifecycle management
  - Screen manager initialization
  - Navigation drawer setup
  - Theme management (Light/Dark)
  - Database initialization
- **Key Features**:
  - Material Design 3 (MD3) theming
  - Custom warning handler for Kivy deprecation warnings
  - Singleton database connection
  - Navigation between screens

### 2. Database Layer (database/db_manager.py)
**Class**: `DatabaseManager` (Singleton)
- **Responsibilities**:
  - SQLite database connection management
  - CRUD operations for all entities
  - Database schema initialization
  - Sample data creation
  - Performance optimization
- **Key Features**:
  - Connection pooling
  - Prepared statements
  - Transaction management
  - Data validation
  - Backup/restore functionality

### 3. Screen Architecture

#### Dashboard Screen (screens/dashboard_screen.py)
**Class**: `DashboardScreen(MDScreen)`
- **Purpose**: Main overview and statistics
- **Features**:
  - Sales summary cards
  - Quick action buttons
  - Stock alerts
  - Recent activity

#### Clients Screen (screens/clients_screen.py)
**Class**: `ClientsScreen(MDScreen)`
- **Purpose**: Customer relationship management
- **Features**:
  - Client list with search/filter
  - `ClientFormDialog` for add/edit operations
  - Client details view
  - Purchase history

#### Products Screen (screens/products_screen.py)
**Class**: `ProductsScreen(MDScreen)`
- **Purpose**: Product catalog management
- **Features**:
  - Product grid/list view
  - `ProductFormDialog` for add/edit operations
  - Stock management
  - Category integration
  - Barcode support

#### Categories Screen (screens/categories_screen.py)
**Class**: `CategoriesScreen(MDScreen)`
- **Purpose**: Product categorization
- **Features**:
  - Hierarchical category tree
  - CRUD operations
  - Product-category relationships
  - Category statistics

#### Sales Screen (screens/sales_screen.py)
**Class**: `SalesScreen(MDScreen)`
- **Purpose**: Sales transaction management
- **Features**:
  - Sales list with filtering
  - `SaleFormDialog` for creating sales
  - `ProductSelectionDialog` for product picker
  - `SaleViewDialog` for viewing sales
  - Invoice generation and printing
  - Custom dropdown components

#### Reports Screen (screens/reports_screen.py)
**Class**: `ReportsScreen(MDScreen)`
- **Purpose**: Analytics and reporting
- **Features**:
  - Sales charts and graphs
  - Period-based analysis
  - Top products/clients reports
  - Data export functionality

#### Settings Screen (screens/settings_screen.py)
**Class**: `SettingsScreen(MDScreen)`
- **Purpose**: Application configuration
- **Features**:
  - `CompanyInfoDialog` for company settings
  - `BackupDialog` for data management
  - Theme customization
  - System preferences

## Dialog and Form Architecture

### Sales Forms
```
SalesFormDialog (forms/sales_form.py)
├── Client Selection (CustomDropdown)
├── Product Selection (ProductSelectionDialog)
├── Quantity Management (ProductLineCard)
├── Payment Method Selection
├── Total Calculations (HT/TTC)
└── Save/Cancel Actions
```

### Form Components
- **`CustomDropdown`**: Reusable dropdown component
- **`SaleCard`**: Sales display card with actions
- **`ProductLineCard`**: Product line item with quantity controls

## Data Flow Architecture

### 1. User Interaction Flow
```
User Action → Screen Event → Dialog/Form → Validation → Database → UI Update
```

### 2. Database Operations Flow
```
Screen/Form → DatabaseManager → SQLite → Result → Callback → UI Refresh
```

### 3. Navigation Flow
```
Navigation Drawer → Screen Manager → Target Screen → Screen Initialization
```

## Design Patterns

### 1. Singleton Pattern
- **DatabaseManager**: Ensures single database connection
- **Benefits**: Resource management, consistency, performance

### 2. MVC Pattern
- **Models**: Data structures and business logic
- **Views**: Screen classes and UI components
- **Controllers**: Form dialogs and event handlers

### 3. Observer Pattern
- **Callbacks**: UI update notifications
- **Events**: User interaction handling
- **Data binding**: Automatic UI synchronization

### 4. Factory Pattern
- **Screen creation**: Dynamic screen instantiation
- **Dialog creation**: Reusable dialog components

## Component Relationships

### Screen Dependencies
```
DashboardScreen
├── DatabaseManager (statistics)
└── Navigation to other screens

ClientsScreen
├── DatabaseManager (client CRUD)
├── ClientFormDialog
└── Search/Filter utilities

ProductsScreen
├── DatabaseManager (product CRUD)
├── ProductFormDialog
├── CategoryModel (category integration)
└── Barcode utilities

SalesScreen
├── DatabaseManager (sales CRUD)
├── SalesFormDialog
├── ProductSelectionDialog
├── PDF generation
└── Printing utilities

ReportsScreen
├── DatabaseManager (analytics queries)
├── Chart generation (matplotlib)
└── Export utilities

SettingsScreen
├── DatabaseManager (configuration)
├── CompanyInfoDialog
├── BackupDialog
└── Theme management
```

### Form Dependencies
```
SalesFormDialog
├── DatabaseManager (clients, products, sales)
├── CustomDropdown (client selection)
├── ProductSelectionDialog (product picker)
├── ProductLineCard (line items)
└── Validation utilities

ClientFormDialog
├── DatabaseManager (client CRUD)
└── Validation utilities

ProductFormDialog
├── DatabaseManager (product CRUD)
├── CategoryModel (category selection)
└── Barcode utilities
```

## Data Model Relationships

### Entity Relationships
```
Client (1) ←→ (N) Sale
Product (1) ←→ (N) SaleLine
Category (1) ←→ (N) Product
Sale (1) ←→ (N) SaleLine
```

### Database Schema
```sql
clients
├── id (PRIMARY KEY)
├── nom, prenom
├── entreprise
├── email, telephone
└── adresse

produits
├── id (PRIMARY KEY)
├── nom, description
├── prix_achat, prix_vente
├── stock_actuel, stock_minimum
├── code_barre
└── categorie_id (FOREIGN KEY)

categories
├── id (PRIMARY KEY)
├── nom, description
└── parent_id (FOREIGN KEY, self-reference)

ventes
├── id (PRIMARY KEY)
├── numero_facture
├── client_id (FOREIGN KEY)
├── date_vente
├── montant_ht, montant_ttc
├── mode_paiement
└── statut

lignes_vente
├── id (PRIMARY KEY)
├── vente_id (FOREIGN KEY)
├── produit_id (FOREIGN KEY)
├── quantite
├── prix_unitaire
└── total_ligne
```

## Performance Considerations

### Database Optimization
- **Connection pooling**: Single connection reuse
- **Prepared statements**: Query optimization
- **Indexing**: Fast lookups on foreign keys
- **Batch operations**: Bulk inserts/updates

### UI Performance
- **Lazy loading**: Load data on demand
- **Virtual scrolling**: Handle large datasets
- **Caching**: Store frequently accessed data
- **Async operations**: Non-blocking UI updates

### Memory Management
- **Widget recycling**: Reuse UI components
- **Image optimization**: Efficient image handling
- **Garbage collection**: Proper object cleanup

## Security Architecture

### Data Protection
- **Local storage**: SQLite database on local filesystem
- **Input validation**: Sanitize user inputs
- **SQL injection prevention**: Parameterized queries
- **File permissions**: Restrict database access

### Error Handling
- **Exception handling**: Graceful error recovery
- **User feedback**: Informative error messages
- **Logging**: Debug and audit trails
- **Fallback mechanisms**: Alternative execution paths

## Extension Points

### Plugin Architecture
- **Screen plugins**: Add new screens
- **Report plugins**: Custom report types
- **Export plugins**: Additional export formats
- **Theme plugins**: Custom themes

### API Integration
- **REST API**: External system integration
- **Webhook support**: Real-time notifications
- **Cloud sync**: Data synchronization
- **Third-party services**: Payment, shipping, etc.

This architecture provides a solid foundation for a scalable, maintainable commercial management application with clear separation of concerns and extensibility for future enhancements.