"""
Formulaire de vente amélioré avec listes déroulantes pour client et paiement
Basé sur la structure de la table ventes
"""

from kivymd.uix.dialog import MDDialog
from kivymd.uix.boxlayout import MDB<PERSON>Layout
from kivymd.uix.scrollview import <PERSON><PERSON><PERSON>roll<PERSON>iew
from kivymd.uix.label import <PERSON>Label
from kivymd.uix.textfield import MD<PERSON>ext<PERSON>ield
from kivymd.uix.button import MDRaisedButton, MDFlatButton, MDIconButton
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.snackbar import MDSnackbar
from kivymd.uix.card import MDCard
from database.db_manager import DatabaseManager
from datetime import datetime


class ImprovedSaleFormDialog(MDDialog):
    """
    Formulaire de vente amélioré basé sur la structure de la table ventes
    
    Structure de la table ventes:
    - id INTEGER PRIMARY KEY AUTOINCREMENT
    - numero_facture TEXT UNIQUE NOT NULL
    - client_id INTEGER REFERENCES clients(id)
    - date_vente TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    - montant_ht DECIMAL(10,2) NOT NULL
    - montant_ttc DECIMAL(10,2) NOT NULL
    - mode_paiement TEXT NOT NULL
    - statut TEXT DEFAULT 'En cours'
    - notes TEXT
    """
    
    def __init__(self, sale_data=None, on_save_callback=None, **kwargs):
        self.sale_data = sale_data or {}
        self.on_save_callback = on_save_callback
        self.db_manager = DatabaseManager()
        
        # Données pour les listes déroulantes
        self.clients_list = []
        self.selected_client = None
        self.sale_items = []  # Produits dans la vente
        
        # Modes de paiement disponibles
        self.payment_modes = [
            "💰 Espèces",
            "💳 Carte bancaire", 
            "📄 Chèque",
            "🏦 Virement bancaire",
            "📱 Paiement électronique",
            "💸 Crédit",
            "🔄 Paiement échelonné",
            "🎁 Bon cadeau",
            "🤝 Compensation",
            "❓ Autre"
        ]
        
        # Déterminer le mode (création ou modification)
        self.is_edit_mode = bool(self.sale_data and self.sale_data.get('id'))
        
        # Créer les boutons
        self.cancel_btn = MDFlatButton(
            text="❌ Annuler",
            on_release=self.dismiss_dialog
        )
        
        self.save_btn = MDRaisedButton(
            text="💾 Enregistrer la vente",
            on_release=self.save_sale
        )
        
        # Charger les données et construire le formulaire d'abord
        try:
            self.load_clients()
            content = self.build_form()
        except Exception as e:
            print(f"❌ Erreur lors de la construction du formulaire: {e}")
            import traceback
            traceback.print_exc()
            # Créer un contenu d'erreur simple
            content = self.create_error_content(str(e))
        
        super().__init__(
            title="✏️ Modifier la vente" if self.is_edit_mode else "🛒 Nouvelle vente",
            type="custom",
            content_cls=content,
            size_hint=(0.95, None),
            height="700dp",
            buttons=[self.cancel_btn, self.save_btn],
            **kwargs
        )
    
    def build_form(self):
        """Construire le formulaire basé sur la structure de la table ventes"""
        # Container principal avec scroll
        scroll_view = MDScrollView()
        
        main_layout = MDBoxLayout(
            orientation='vertical',
            spacing="16dp",
            padding="20dp",
            size_hint_y=None,
            adaptive_height=True
        )
        
        # En-tête du formulaire
        header = self.create_header()
        main_layout.add_widget(header)
        
        # Section client (OBLIGATOIRE - FOREIGN KEY)
        client_section = self.create_client_section()
        main_layout.add_widget(client_section)
        
        # Section mode de paiement (OBLIGATOIRE)
        payment_section = self.create_payment_section()
        main_layout.add_widget(payment_section)
        
        # Section produits
        products_section = self.create_products_section()
        main_layout.add_widget(products_section)
        
        # Section notes (OPTIONNEL)
        notes_section = self.create_notes_section()
        main_layout.add_widget(notes_section)
        
        # Section totaux
        totals_section = self.create_totals_section()
        main_layout.add_widget(totals_section)
        
        # Section informations (si modification)
        if self.is_edit_mode:
            info_section = self.create_info_section()
            main_layout.add_widget(info_section)
        
        # Ajouter au scroll
        scroll_view.add_widget(main_layout)
        
        # Retourner le contenu
        return scroll_view
    
    def create_header(self):
        """Créer l'en-tête du formulaire"""
        header_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="80dp"
        )
        
        # Titre principal
        title = MDLabel(
            text="🛒 Formulaire de Vente",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp",
            halign="center"
        )
        
        # Sous-titre avec mode
        mode_text = "Modification d'une vente existante" if self.is_edit_mode else "Création d'une nouvelle vente"
        subtitle = MDLabel(
            text=mode_text,
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="20dp",
            halign="center"
        )
        
        # Numéro de facture (si modification)
        if self.is_edit_mode:
            numero_facture = self.sale_data.get('numero_facture', 'N/A')
            facture_label = MDLabel(
                text=f"📄 Facture N°: {numero_facture}",
                font_style="Subtitle2",
                theme_text_color="Primary",
                size_hint_y=None,
                height="20dp",
                halign="center"
            )
            header_layout.add_widget(facture_label)
        
        header_layout.add_widget(title)
        header_layout.add_widget(subtitle)
        
        return header_layout
    
    def create_client_section(self):
        """Créer la section client (FOREIGN KEY vers table clients)"""
        client_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="100dp"
        )
        
        # Label avec contrainte de la table
        client_label = MDLabel(
            text="👤 Client (OBLIGATOIRE - SÉLECTION DANS LA LISTE)",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        # Champ client avec liste déroulante
        self.client_field = MDTextField(
            hint_text="Cliquez pour sélectionner un client",
            text=self.get_client_display_name(),
            size_hint_y=None,
            height="56dp",
            mode="rectangle",
            readonly=True,
            # Couleurs pour indiquer que c'est une liste
            line_color_normal=[0.2, 0.2, 0.8, 1],
            line_color_focus=[0.1, 0.1, 0.9, 1],
            text_color_normal=[0, 0, 0.8, 1],
            hint_text_color_normal=[0.4, 0.4, 0.8, 1],
            fill_color_normal=[0.95, 0.95, 1, 1],
            icon_right="chevron-down",
            on_release=self.open_client_menu
        )
        
        # Créer le menu déroulant des clients
        self.create_client_menu()
        
        client_layout.add_widget(client_label)
        client_layout.add_widget(self.client_field)
        
        return client_layout
    
    def create_payment_section(self):
        """Créer la section mode de paiement (OBLIGATOIRE)"""
        payment_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="100dp"
        )
        
        # Label avec contrainte de la table
        payment_label = MDLabel(
            text="💳 Mode de paiement (OBLIGATOIRE - SÉLECTION DANS LA LISTE)",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        # Champ mode de paiement avec liste déroulante
        current_payment = self.sale_data.get('mode_paiement', '💰 Espèces')
        if not any(current_payment in mode for mode in self.payment_modes):
            current_payment = '💰 Espèces'
        
        self.payment_field = MDTextField(
            hint_text="Cliquez pour sélectionner le mode de paiement",
            text=current_payment,
            size_hint_y=None,
            height="56dp",
            mode="rectangle",
            readonly=True,
            # Couleurs pour indiquer que c'est une liste
            line_color_normal=[0.2, 0.8, 0.2, 1],
            line_color_focus=[0.1, 0.9, 0.1, 1],
            text_color_normal=[0, 0.6, 0, 1],
            hint_text_color_normal=[0.4, 0.8, 0.4, 1],
            fill_color_normal=[0.95, 1, 0.95, 1],
            icon_right="chevron-down",
            on_release=self.open_payment_menu
        )
        
        # Créer le menu déroulant des modes de paiement
        self.create_payment_menu()
        
        payment_layout.add_widget(payment_label)
        payment_layout.add_widget(self.payment_field)
        
        return payment_layout
    
    def create_products_section(self):
        """Créer la section des produits"""
        products_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="250dp"
        )
        
        # En-tête de la section
        products_header = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="40dp"
        )
        
        products_label = MDLabel(
            text="📦 Produits de la vente",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        
        add_product_btn = MDIconButton(
            icon="plus-circle",
            theme_icon_color="Custom",
            icon_color=[0.2, 0.8, 0.2, 1],
            on_release=self.add_product_dialog,
            size_hint_x=0.3
        )
        
        products_header.add_widget(products_label)
        products_header.add_widget(add_product_btn)
        
        # Liste des produits
        self.products_scroll = MDScrollView(
            size_hint_y=None,
            height="200dp"
        )
        
        self.products_container = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            adaptive_height=True
        )
        
        self.products_scroll.add_widget(self.products_container)
        
        products_layout.add_widget(products_header)
        products_layout.add_widget(self.products_scroll)
        
        return products_layout
    
    def create_notes_section(self):
        """Créer la section notes (OPTIONNEL)"""
        notes_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="120dp"
        )
        
        # Label
        notes_label = MDLabel(
            text="📝 Notes (OPTIONNELLES)",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        # Champ notes
        self.notes_field = MDTextField(
            text=self.sale_data.get('notes', ''),
            hint_text="Notes ou commentaires sur la vente (optionnel)",
            multiline=True,
            size_hint_y=None,
            height="80dp",
            mode="rectangle",
            max_text_length=500
        )
        
        notes_layout.add_widget(notes_label)
        notes_layout.add_widget(self.notes_field)
        
        return notes_layout
    
    def create_totals_section(self):
        """Créer la section des totaux"""
        totals_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="80dp"
        )
        
        # Label de section
        totals_label = MDLabel(
            text="💰 Totaux de la vente",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        # Affichage des totaux
        self.totals_display = MDLabel(
            text="Total HT: 0.00 DH | Total TTC: 0.00 DH",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp",
            halign="center"
        )
        
        totals_layout.add_widget(totals_label)
        totals_layout.add_widget(self.totals_display)
        
        return totals_layout
    
    def create_info_section(self):
        """Créer la section d'informations (pour modification uniquement)"""
        info_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="100dp"
        )
        
        # Titre de la section
        info_title = MDLabel(
            text="ℹ️ Informations de la base de données",
            font_style="Subtitle2",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="32dp"
        )
        
        # Informations en ligne
        info_row = MDBoxLayout(
            orientation='horizontal',
            spacing="16dp",
            size_hint_y=None,
            height="60dp"
        )
        
        # ID (PRIMARY KEY AUTOINCREMENT)
        id_info = MDLabel(
            text=f"🆔 ID: {self.sale_data.get('id', 'N/A')}",
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_x=0.25
        )
        
        # Date de vente (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
        date_vente = self.sale_data.get('date_vente', '')
        if date_vente:
            try:
                if isinstance(date_vente, str):
                    date_obj = datetime.fromisoformat(date_vente.replace('Z', '+00:00'))
                    date_formatted = date_obj.strftime('%d/%m/%Y %H:%M')
                else:
                    date_formatted = str(date_vente)[:16]
            except:
                date_formatted = str(date_vente)[:16]
        else:
            date_formatted = "Non définie"
        
        date_info = MDLabel(
            text=f"📅 Date: {date_formatted}",
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_x=0.35
        )
        
        # Statut
        statut = self.sale_data.get('statut', 'En cours')
        statut_info = MDLabel(
            text=f"📊 Statut: {statut}",
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_x=0.25
        )
        
        # Numéro de facture (UNIQUE NOT NULL)
        numero_facture = self.sale_data.get('numero_facture', 'N/A')
        facture_info = MDLabel(
            text=f"📄 N°: {numero_facture}",
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_x=0.15
        )
        
        info_row.add_widget(id_info)
        info_row.add_widget(date_info)
        info_row.add_widget(statut_info)
        info_row.add_widget(facture_info)
        
        info_layout.add_widget(info_title)
        info_layout.add_widget(info_row)
        
        return info_layout
    
    def create_error_content(self, error_message):
        """Créer un contenu d'erreur simple"""
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.label import MDLabel
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="200dp"
        )
        
        error_label = MDLabel(
            text=f"❌ Erreur lors de la construction du formulaire:\n\n{error_message}",
            font_style="Body1",
            theme_text_color="Error",
            halign="center"
        )
        
        layout.add_widget(error_label)
        return layout
    
    def load_clients(self):
        """Charger la liste des clients depuis la base de données"""
        try:
            if not self.db_manager.connect():
                return
            
            # Requête pour récupérer tous les clients
            clients = self.db_manager.execute_query("""
                SELECT id, nom, prenom, entreprise, email, telephone
                FROM clients 
                ORDER BY nom, prenom
            """)
            
            self.clients_list = clients or []
            
            # Si on est en mode modification, trouver le client sélectionné
            if self.is_edit_mode and self.sale_data.get('client_id'):
                for client in self.clients_list:
                    if client['id'] == self.sale_data['client_id']:
                        self.selected_client = client
                        break
            
        except Exception as e:
            print(f"Erreur lors du chargement des clients: {e}")
        finally:
            self.db_manager.close()
    
    def create_client_menu(self):
        """Créer le menu déroulant des clients"""
        client_menu_items = []
        
        for client in self.clients_list:
            # Construire le nom d'affichage
            nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
            if not nom_complet:
                nom_complet = client.get('entreprise', f"Client {client.get('id', '')}")
            
            # Ajouter des informations supplémentaires
            details = []
            if client.get('entreprise') and nom_complet != client.get('entreprise'):
                details.append(client.get('entreprise'))
            if client.get('telephone'):
                details.append(client.get('telephone'))
            
            display_text = nom_complet
            if details:
                display_text += f" ({', '.join(details)})"
            
            client_menu_items.append({
                "text": display_text,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=client: self.select_client(x)
            })
        
        # Ajouter une option pour créer un nouveau client
        client_menu_items.append({
            "text": "➕ Créer un nouveau client",
            "viewclass": "OneLineListItem",
            "on_release": self.create_new_client
        })
        
        self.client_menu = MDDropdownMenu(
            caller=self.client_field,
            items=client_menu_items,
            max_height="300dp"
        )
    
    def create_payment_menu(self):
        """Créer le menu déroulant des modes de paiement"""
        payment_menu_items = []
        
        for mode in self.payment_modes:
            payment_menu_items.append({
                "text": mode,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=mode: self.select_payment_mode(x)
            })
        
        self.payment_menu = MDDropdownMenu(
            caller=self.payment_field,
            items=payment_menu_items,
            max_height="300dp"
        )
    
    def get_client_display_name(self):
        """Obtenir le nom d'affichage du client sélectionné"""
        if not self.selected_client:
            return ""
        
        nom_complet = f"{self.selected_client.get('prenom', '')} {self.selected_client.get('nom', '')}".strip()
        if not nom_complet:
            nom_complet = self.selected_client.get('entreprise', f"Client {self.selected_client.get('id', '')}")
        
        return nom_complet
    
    def open_client_menu(self, *args):
        """Ouvrir le menu de sélection des clients"""
        self.client_menu.open()
    
    def open_payment_menu(self, *args):
        """Ouvrir le menu de sélection des modes de paiement"""
        self.payment_menu.open()
    
    def select_client(self, client):
        """Sélectionner un client"""
        self.selected_client = client
        self.client_field.text = self.get_client_display_name()
        self.client_menu.dismiss()
    
    def select_payment_mode(self, mode):
        """Sélectionner un mode de paiement"""
        self.payment_field.text = mode
        self.payment_menu.dismiss()
    
    def create_new_client(self, *args):
        """Créer un nouveau client (placeholder)"""
        self.client_menu.dismiss()
        self.show_info("Fonctionnalité de création de client à implémenter")
    
    def add_product_dialog(self, *args):
        """Ajouter un produit à la vente (placeholder)"""
        self.show_info("Fonctionnalité d'ajout de produit à implémenter")
    
    def calculate_totals(self):
        """Calculer les totaux de la vente"""
        total_ht = 0.0
        total_ttc = 0.0
        
        for item in self.sale_items:
            prix_unitaire = item.get('prix_unitaire', 0)
            quantite = item.get('quantite', 0)
            tva_rate = item.get('tva_rate', 0.2)  # 20% par défaut
            
            montant_ht = prix_unitaire * quantite
            montant_ttc = montant_ht * (1 + tva_rate)
            
            total_ht += montant_ht
            total_ttc += montant_ttc
        
        self.totals_display.text = f"Total HT: {total_ht:.2f} DH | Total TTC: {total_ttc:.2f} DH"
    
    def validate_form(self):
        """Valider le formulaire"""
        errors = []
        
        # Vérifier le client (OBLIGATOIRE - FOREIGN KEY)
        if not self.selected_client:
            errors.append("Veuillez sélectionner un client")
        
        # Vérifier le mode de paiement (OBLIGATOIRE)
        if not self.payment_field.text.strip():
            errors.append("Veuillez sélectionner un mode de paiement")
        
        # Vérifier qu'il y a au moins un produit
        if not self.sale_items:
            errors.append("Veuillez ajouter au moins un produit à la vente")
        
        return errors
    
    def save_sale(self, *args):
        """Sauvegarder la vente dans la table ventes"""
        # Validation
        errors = self.validate_form()
        if errors:
            self.show_error("Erreurs de validation:\n" + "\n".join(f"• {error}" for error in errors))
            return
        
        try:
            if not self.db_manager.connect():
                self.show_error("Impossible de se connecter à la base de données")
                return
            
            # Calculer les totaux
            self.calculate_totals()
            total_ht = sum(item.get('prix_unitaire', 0) * item.get('quantite', 0) for item in self.sale_items)
            total_ttc = sum(item.get('prix_unitaire', 0) * item.get('quantite', 0) * (1 + item.get('tva_rate', 0.2)) for item in self.sale_items)
            
            # Données à sauvegarder
            client_id = self.selected_client['id']
            mode_paiement = self.payment_field.text.strip()
            notes = self.notes_field.text.strip() or None
            
            if self.is_edit_mode:
                # UPDATE sur la table ventes
                success = self.db_manager.execute_update("""
                    UPDATE ventes 
                    SET client_id = ?, montant_ht = ?, montant_ttc = ?, 
                        mode_paiement = ?, notes = ?
                    WHERE id = ?
                """, (client_id, total_ht, total_ttc, mode_paiement, notes, self.sale_data['id']))
                
                if success:
                    self.show_success("Vente modifiée avec succès")
                    result_data = {
                        'id': self.sale_data['id'],
                        'client_id': client_id,
                        'montant_ht': total_ht,
                        'montant_ttc': total_ttc,
                        'mode_paiement': mode_paiement,
                        'notes': notes
                    }
                else:
                    self.show_error("Erreur lors de la modification")
                    return
            else:
                # Générer un numéro de facture unique
                numero_facture = f"FAC-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
                
                # INSERT dans la table ventes
                sale_id = self.db_manager.execute_insert("""
                    INSERT INTO ventes (numero_facture, client_id, montant_ht, montant_ttc, 
                                      mode_paiement, notes, statut)
                    VALUES (?, ?, ?, ?, ?, ?, 'En cours')
                """, (numero_facture, client_id, total_ht, total_ttc, mode_paiement, notes))
                
                if sale_id:
                    self.show_success("Vente créée avec succès")
                    result_data = {
                        'id': sale_id,
                        'numero_facture': numero_facture,
                        'client_id': client_id,
                        'montant_ht': total_ht,
                        'montant_ttc': total_ttc,
                        'mode_paiement': mode_paiement,
                        'notes': notes,
                        'statut': 'En cours'
                    }
                else:
                    self.show_error("Erreur lors de la création")
                    return
            
            # Callback avec les données
            if self.on_save_callback:
                self.on_save_callback(result_data)
            
            self.dismiss()
            
        except Exception as e:
            self.show_error(f"Erreur base de données: {str(e)}")
        finally:
            self.db_manager.close()
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"❌ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"❌ {message}")
    
    def show_success(self, message):
        """Afficher un message de succès"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"✅ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"✅ {message}")
    
    def show_info(self, message):
        """Afficher un message d'information"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"ℹ️ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"ℹ️ {message}")
    
    def dismiss_dialog(self, *args):
        """Fermer le dialog"""
        self.dismiss()