#!/usr/bin/env python3
"""
Test de la taille du formulaire de vente (50% de la fenêtre)
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

# Configuration pour Windows
if sys.platform == 'win32':
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from kivy.core.window import Window

# Import du formulaire
from forms.sales_form import SalesFormDialog


class TestFormSizeApp(MDApp):
    """Test de la taille du formulaire"""
    
    def build(self):
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        title = MDLabel(
            text="📏 Test Taille Formulaire\n50% de la Fenêtre Principale",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Informations sur la fenêtre
        window_info = MDLabel(
            text=f"Taille fenêtre actuelle :\n{Window.width} × {Window.height} pixels",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        # Test formulaire avec nouvelle taille
        size_btn = MDRaisedButton(
            text="📏 Formulaire 50% Hauteur",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_form_size
        )
        
        self.result_label = MDLabel(
            text="Test de la taille du formulaire\n\n" \
                 "Le formulaire doit maintenant occuper :\n" \
                 "• 90% de la largeur de la fenêtre\n" \
                 "• 50% de la hauteur de la fenêtre\n" \
                 "• Scroll automatique si contenu trop grand",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(window_info)
        layout.add_widget(size_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_form_size(self, *args):
        """Tester la taille du formulaire"""
        try:
            dialog = SalesFormDialog(
                on_save_callback=self.on_save
            )
            dialog.open()
            
            # Calculer les tailles attendues
            expected_width = Window.width * 0.9
            expected_height = Window.height * 0.5
            
            self.result_label.text = f"📏 Formulaire ouvert avec nouvelle taille !\n\n" \
                                   f"TAILLE ATTENDUE :\n" \
                                   f"• Largeur : {expected_width:.0f}px (90% de {Window.width}px)\n" \
                                   f"• Hauteur : {expected_height:.0f}px (50% de {Window.height}px)\n\n" \
                                   f"VÉRIFICATIONS :\n" \
                                   f"✅ Formulaire prend 50% de la hauteur\n" \
                                   f"✅ Scroll disponible pour le contenu\n" \
                                   f"✅ Toutes les sections visibles\n" \
                                   f"✅ Taille responsive à la fenêtre\n\n" \
                                   f"Le formulaire s'adapte maintenant à la taille de la fenêtre !"
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur test taille formulaire :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def on_save(self, data):
        """Callback de test"""
        self.result_label.text = f"🎉 Test de taille réussi !\n\n" \
                               f"Le formulaire occupe maintenant :\n" \
                               f"• 90% de la largeur de la fenêtre\n" \
                               f"• 50% de la hauteur de la fenêtre\n\n" \
                               f"Avantages :\n" \
                               f"✅ S'adapte à toutes les tailles d'écran\n" \
                               f"✅ Laisse de la place pour voir l'arrière-plan\n" \
                               f"✅ Scroll automatique si nécessaire\n" \
                               f"✅ Interface plus équilibrée"


def main():
    print("📏 Test Taille Formulaire - 50% de la Fenêtre")
    print("OBJECTIF: Vérifier que le formulaire occupe 50% de la hauteur")
    
    try:
        app = TestFormSizeApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()