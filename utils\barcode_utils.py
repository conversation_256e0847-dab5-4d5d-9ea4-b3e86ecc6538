"""
Utilitaires pour la gestion des codes-barres
"""

import re
import random
import string
from typing import Optional, Dict, Any
from datetime import datetime


class BarcodeGenerator:
    """Générateur de codes-barres"""
    
    @staticmethod
    def generate_ean13() -> str:
        """
        Générer un code-barres EAN-13 valide
        
        Returns:
            Code-barres EAN-13 (13 chiffres)
        """
        # Générer 12 chiffres aléatoirement
        code = ''.join([str(random.randint(0, 9)) for _ in range(12)])
        
        # Calculer la clé de contrôle
        checksum = BarcodeValidator.calculate_ean13_checksum(code)
        
        return code + str(checksum)
    
    @staticmethod
    def generate_ean8() -> str:
        """
        Générer un code-barres EAN-8 valide
        
        Returns:
            Code-barres EAN-8 (8 chiffres)
        """
        # Générer 7 chiffres aléatoirement
        code = ''.join([str(random.randint(0, 9)) for _ in range(7)])
        
        # Calculer la clé de contrôle
        checksum = BarcodeValidator.calculate_ean8_checksum(code)
        
        return code + str(checksum)
    
    @staticmethod
    def generate_code128(length: int = 12) -> str:
        """
        Générer un code-barres Code 128
        
        Args:
            length: Longueur du code
            
        Returns:
            Code-barres Code 128
        """
        # Code 128 peut contenir des lettres et des chiffres
        characters = string.ascii_uppercase + string.digits
        return ''.join(random.choices(characters, k=length))
    
    @staticmethod
    def generate_custom_barcode(prefix: str = "PRD", length: int = 10) -> str:
        """
        Générer un code-barres personnalisé
        
        Args:
            prefix: Préfixe du code
            length: Longueur totale du code
            
        Returns:
            Code-barres personnalisé
        """
        remaining_length = length - len(prefix)
        if remaining_length <= 0:
            remaining_length = 6
        
        # Ajouter timestamp pour unicité
        timestamp = datetime.now().strftime("%m%d")
        random_part = ''.join([str(random.randint(0, 9)) for _ in range(remaining_length - 4)])
        
        return f"{prefix}{timestamp}{random_part}"


class BarcodeValidator:
    """Validateur de codes-barres"""
    
    @staticmethod
    def validate_ean13(barcode: str) -> bool:
        """
        Valider un code-barres EAN-13
        
        Args:
            barcode: Code-barres à valider
            
        Returns:
            True si valide, False sinon
        """
        if not barcode or len(barcode) != 13 or not barcode.isdigit():
            return False
        
        # Vérifier la clé de contrôle
        code_part = barcode[:12]
        check_digit = int(barcode[12])
        calculated_check = BarcodeValidator.calculate_ean13_checksum(code_part)
        
        return check_digit == calculated_check
    
    @staticmethod
    def validate_ean8(barcode: str) -> bool:
        """
        Valider un code-barres EAN-8
        
        Args:
            barcode: Code-barres à valider
            
        Returns:
            True si valide, False sinon
        """
        if not barcode or len(barcode) != 8 or not barcode.isdigit():
            return False
        
        # Vérifier la clé de contrôle
        code_part = barcode[:7]
        check_digit = int(barcode[7])
        calculated_check = BarcodeValidator.calculate_ean8_checksum(code_part)
        
        return check_digit == calculated_check
    
    @staticmethod
    def calculate_ean13_checksum(code: str) -> int:
        """
        Calculer la clé de contrôle EAN-13
        
        Args:
            code: Code de 12 chiffres
            
        Returns:
            Clé de contrôle (0-9)
        """
        if len(code) != 12 or not code.isdigit():
            return 0
        
        total = 0
        for i, digit in enumerate(code):
            if i % 2 == 0:
                total += int(digit)
            else:
                total += int(digit) * 3
        
        remainder = total % 10
        return (10 - remainder) % 10
    
    @staticmethod
    def calculate_ean8_checksum(code: str) -> int:
        """
        Calculer la clé de contrôle EAN-8
        
        Args:
            code: Code de 7 chiffres
            
        Returns:
            Clé de contrôle (0-9)
        """
        if len(code) != 7 or not code.isdigit():
            return 0
        
        total = 0
        for i, digit in enumerate(code):
            if i % 2 == 0:
                total += int(digit) * 3
            else:
                total += int(digit)
        
        remainder = total % 10
        return (10 - remainder) % 10
    
    @staticmethod
    def validate_barcode(barcode: str) -> Dict[str, Any]:
        """
        Valider un code-barres et déterminer son type
        
        Args:
            barcode: Code-barres à valider
            
        Returns:
            Dictionnaire avec les informations de validation
        """
        result = {
            'valid': False,
            'type': 'unknown',
            'length': len(barcode) if barcode else 0,
            'format': None
        }
        
        if not barcode:
            result['error'] = 'Code-barres vide'
            return result
        
        # Nettoyer le code-barres
        clean_barcode = re.sub(r'[^A-Za-z0-9]', '', barcode)
        result['cleaned'] = clean_barcode
        
        # Vérifier EAN-13
        if len(clean_barcode) == 13 and clean_barcode.isdigit():
            result['type'] = 'EAN-13'
            result['valid'] = BarcodeValidator.validate_ean13(clean_barcode)
            result['format'] = 'numeric'
        
        # Vérifier EAN-8
        elif len(clean_barcode) == 8 and clean_barcode.isdigit():
            result['type'] = 'EAN-8'
            result['valid'] = BarcodeValidator.validate_ean8(clean_barcode)
            result['format'] = 'numeric'
        
        # Vérifier UPC-A (12 chiffres)
        elif len(clean_barcode) == 12 and clean_barcode.isdigit():
            result['type'] = 'UPC-A'
            result['valid'] = True  # Validation simplifiée
            result['format'] = 'numeric'
        
        # Code 128 ou autres formats alphanumériques
        elif 6 <= len(clean_barcode) <= 20:
            result['type'] = 'Code128/Custom'
            result['valid'] = True  # Validation simplifiée
            result['format'] = 'alphanumeric' if not clean_barcode.isdigit() else 'numeric'
        
        else:
            result['error'] = f'Longueur invalide: {len(clean_barcode)} caractères'
        
        return result


class BarcodeFormatter:
    """Formateur de codes-barres"""
    
    @staticmethod
    def format_ean13(barcode: str) -> str:
        """
        Formater un code EAN-13 avec espaces
        
        Args:
            barcode: Code EAN-13
            
        Returns:
            Code formaté (ex: 1 234567 890123)
        """
        if len(barcode) != 13:
            return barcode
        
        return f"{barcode[0]} {barcode[1:7]} {barcode[7:13]}"
    
    @staticmethod
    def format_ean8(barcode: str) -> str:
        """
        Formater un code EAN-8 avec espaces
        
        Args:
            barcode: Code EAN-8
            
        Returns:
            Code formaté (ex: 1234 5678)
        """
        if len(barcode) != 8:
            return barcode
        
        return f"{barcode[:4]} {barcode[4:]}"
    
    @staticmethod
    def format_barcode(barcode: str, barcode_type: str = None) -> str:
        """
        Formater un code-barres selon son type
        
        Args:
            barcode: Code-barres à formater
            barcode_type: Type de code-barres
            
        Returns:
            Code-barres formaté
        """
        if not barcode:
            return ""
        
        # Auto-détection du type si non spécifié
        if not barcode_type:
            validation = BarcodeValidator.validate_barcode(barcode)
            barcode_type = validation.get('type', 'unknown')
        
        if barcode_type == 'EAN-13':
            return BarcodeFormatter.format_ean13(barcode)
        elif barcode_type == 'EAN-8':
            return BarcodeFormatter.format_ean8(barcode)
        else:
            return barcode


class BarcodeSearch:
    """Recherche et gestion des codes-barres"""
    
    @staticmethod
    def search_product_by_barcode(db_manager, barcode: str) -> Optional[Dict[str, Any]]:
        """
        Rechercher un produit par code-barres
        
        Args:
            db_manager: Gestionnaire de base de données
            barcode: Code-barres à rechercher
            
        Returns:
            Informations du produit ou None si non trouvé
        """
        try:
            # Nettoyer le code-barres
            clean_barcode = re.sub(r'[^A-Za-z0-9]', '', barcode)
            
            # Rechercher dans la base de données
            query = "SELECT * FROM produits WHERE code_barre = ? AND actif = 1"
            results = db_manager.execute_query(query, (clean_barcode,))
            
            return results[0] if results else None
            
        except Exception as e:
            print(f"Erreur lors de la recherche par code-barres: {e}")
            return None
    
    @staticmethod
    def check_barcode_exists(db_manager, barcode: str, exclude_id: int = None) -> bool:
        """
        Vérifier si un code-barres existe déjà
        
        Args:
            db_manager: Gestionnaire de base de données
            barcode: Code-barres à vérifier
            exclude_id: ID de produit à exclure de la recherche
            
        Returns:
            True si le code-barres existe, False sinon
        """
        try:
            clean_barcode = re.sub(r'[^A-Za-z0-9]', '', barcode)
            
            if exclude_id:
                query = "SELECT COUNT(*) as count FROM produits WHERE code_barre = ? AND id != ? AND actif = 1"
                params = (clean_barcode, exclude_id)
            else:
                query = "SELECT COUNT(*) as count FROM produits WHERE code_barre = ? AND actif = 1"
                params = (clean_barcode,)
            
            result = db_manager.execute_query(query, params)
            return result[0]['count'] > 0 if result else False
            
        except Exception as e:
            print(f"Erreur lors de la vérification du code-barres: {e}")
            return False
    
    @staticmethod
    def generate_unique_barcode(db_manager, barcode_type: str = 'EAN13') -> str:
        """
        Générer un code-barres unique
        
        Args:
            db_manager: Gestionnaire de base de données
            barcode_type: Type de code-barres à générer
            
        Returns:
            Code-barres unique
        """
        max_attempts = 100
        
        for _ in range(max_attempts):
            if barcode_type.upper() == 'EAN13':
                barcode = BarcodeGenerator.generate_ean13()
            elif barcode_type.upper() == 'EAN8':
                barcode = BarcodeGenerator.generate_ean8()
            elif barcode_type.upper() == 'CODE128':
                barcode = BarcodeGenerator.generate_code128()
            else:
                barcode = BarcodeGenerator.generate_custom_barcode()
            
            # Vérifier l'unicité
            if not BarcodeSearch.check_barcode_exists(db_manager, barcode):
                return barcode
        
        # Si aucun code unique trouvé, utiliser un timestamp
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"PRD{timestamp}"


# Fonctions utilitaires pour l'interface
def validate_barcode_input(barcode: str) -> Dict[str, Any]:
    """
    Valider une saisie de code-barres pour l'interface utilisateur
    
    Args:
        barcode: Code-barres saisi
        
    Returns:
        Résultat de validation avec messages d'erreur
    """
    result = BarcodeValidator.validate_barcode(barcode)
    
    if not result['valid']:
        if 'error' in result:
            result['message'] = result['error']
        elif result['type'] == 'unknown':
            result['message'] = "Format de code-barres non reconnu"
        else:
            result['message'] = f"Code-barres {result['type']} invalide"
    else:
        result['message'] = f"Code-barres {result['type']} valide"
    
    return result


def format_barcode_for_display(barcode: str) -> str:
    """
    Formater un code-barres pour l'affichage
    
    Args:
        barcode: Code-barres à formater
        
    Returns:
        Code-barres formaté pour l'affichage
    """
    if not barcode:
        return "Aucun code-barres"
    
    return BarcodeFormatter.format_barcode(barcode)