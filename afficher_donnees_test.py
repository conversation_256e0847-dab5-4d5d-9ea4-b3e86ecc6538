#!/usr/bin/env python3
"""
Script pour afficher un résumé des données de test
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def afficher_donnees_test():
    """Afficher un résumé complet des données de test"""
    print("📊 RÉSUMÉ COMPLET DES DONNÉES DE TEST")
    print("=" * 60)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    
    if not db_manager.connect():
        print("❌ Impossible de se connecter à la base de données")
        return
    
    try:
        # 1. CLIENTS
        print("\n👥 CLIENTS")
        print("-" * 30)
        
        clients = db_manager.execute_query("""
            SELECT nom, prenom, entreprise, email, telephone, ville 
            FROM clients 
            WHERE actif = 1 
            ORDER BY id
        """)
        
        for i, client in enumerate(clients, 1):
            nom_affiche = client.get('entreprise') or f"{client.get('prenom', '') or ''} {client.get('nom', '') or ''}".strip()
            ville = client.get('ville') or 'N/A'
            telephone = client.get('telephone') or 'N/A'
            print(f"{i:2d}. {nom_affiche:<25} | {ville:<12} | {telephone}")
        
        print(f"\n📊 Total clients: {len(clients)}")
        
        # 2. PRODUITS
        print("\n📦 PRODUITS")
        print("-" * 30)
        
        produits = db_manager.execute_query("""
            SELECT nom, prix_vente, stock_actuel, reference 
            FROM produits 
            WHERE actif = 1 
            ORDER BY prix_vente DESC
        """)
        
        for i, produit in enumerate(produits, 1):
            nom = produit['nom'][:30] + "..." if len(produit['nom']) > 30 else produit['nom']
            prix = produit['prix_vente']
            stock = produit['stock_actuel']
            print(f"{i:2d}. {nom:<33} | {prix:>8.0f} DH | Stock: {stock:>2d}")
        
        print(f"\n📊 Total produits: {len(produits)}")
        
        # 3. VENTES
        print("\n🛒 VENTES")
        print("-" * 30)
        
        ventes = db_manager.execute_query("""
            SELECT v.numero_facture, v.montant_ttc, v.statut, v.mode_paiement,
                   COALESCE(c.entreprise, c.prenom || ' ' || c.nom) as client_nom,
                   DATE(v.date_vente) as date_vente
            FROM ventes v
            LEFT JOIN clients c ON v.client_id = c.id
            ORDER BY v.date_vente DESC
            LIMIT 10
        """)
        
        for i, vente in enumerate(ventes, 1):
            numero = vente['numero_facture'][-8:]  # Derniers 8 caractères
            montant = vente['montant_ttc']
            statut = vente['statut']
            client = (vente['client_nom'] or 'Client inconnu')[:20]
            
            # Icône selon le statut
            statut_icon = {'En cours': '🔄', 'Payée': '✅', 'Annulée': '❌'}.get(statut, '❓')
            
            print(f"{i:2d}. {numero} | {montant:>8.0f} DH | {statut_icon} {statut:<8} | {client}")
        
        # 4. STATISTIQUES DÉTAILLÉES
        print("\n📈 STATISTIQUES DÉTAILLÉES")
        print("-" * 30)
        
        # Statistiques ventes
        stats_ventes = db_manager.execute_query("""
            SELECT 
                COUNT(*) as total_ventes,
                SUM(CASE WHEN statut = 'En cours' THEN 1 ELSE 0 END) as en_cours,
                SUM(CASE WHEN statut = 'Payée' THEN 1 ELSE 0 END) as payees,
                SUM(CASE WHEN statut = 'Annulée' THEN 1 ELSE 0 END) as annulees,
                SUM(montant_ttc) as ca_total,
                AVG(montant_ttc) as panier_moyen
            FROM ventes
        """)[0]
        
        print(f"🛒 Ventes totales: {stats_ventes['total_ventes']}")
        print(f"   • 🔄 En cours: {stats_ventes['en_cours']}")
        print(f"   • ✅ Payées: {stats_ventes['payees']}")
        print(f"   • ❌ Annulées: {stats_ventes['annulees']}")
        print(f"💰 Chiffre d'affaires: {stats_ventes['ca_total']:,.2f} DH")
        print(f"🛍️ Panier moyen: {stats_ventes['panier_moyen']:,.2f} DH")
        
        # Statistiques par mode de paiement
        print("\n💳 RÉPARTITION PAR MODE DE PAIEMENT:")
        paiements = db_manager.execute_query("""
            SELECT mode_paiement, COUNT(*) as nb_ventes, SUM(montant_ttc) as montant_total
            FROM ventes
            GROUP BY mode_paiement
            ORDER BY montant_total DESC
        """)
        
        for paiement in paiements:
            mode = paiement['mode_paiement']
            nb = paiement['nb_ventes']
            montant = paiement['montant_total']
            print(f"   • {mode:<18}: {nb:>2d} ventes | {montant:>10,.0f} DH")
        
        # Top 5 des produits les plus vendus
        print("\n🏆 TOP 5 PRODUITS LES PLUS VENDUS:")
        top_produits = db_manager.execute_query("""
            SELECT p.nom, SUM(dv.quantite) as total_vendu, SUM(dv.montant_ligne) as ca_produit
            FROM details_vente dv
            JOIN produits p ON dv.produit_id = p.id
            JOIN ventes v ON dv.vente_id = v.id
            WHERE v.statut != 'Annulée'
            GROUP BY p.id, p.nom
            ORDER BY total_vendu DESC
            LIMIT 5
        """)
        
        for i, produit in enumerate(top_produits, 1):
            nom = produit['nom'][:25] + "..." if len(produit['nom']) > 25 else produit['nom']
            quantite = produit['total_vendu']
            ca = produit['ca_produit']
            print(f"   {i}. {nom:<28} | {quantite:>2d} vendus | {ca:>8,.0f} DH")
        
        # Clients les plus actifs
        print("\n👑 TOP 5 CLIENTS LES PLUS ACTIFS:")
        top_clients = db_manager.execute_query("""
            SELECT 
                COALESCE(c.entreprise, c.prenom || ' ' || c.nom) as client_nom,
                COUNT(v.id) as nb_achats,
                SUM(v.montant_ttc) as total_achats
            FROM ventes v
            JOIN clients c ON v.client_id = c.id
            WHERE v.statut != 'Annulée'
            GROUP BY c.id, client_nom
            ORDER BY total_achats DESC
            LIMIT 5
        """)
        
        for i, client in enumerate(top_clients, 1):
            nom = (client['client_nom'] or 'Client inconnu')[:25]
            nb_achats = client['nb_achats']
            total = client['total_achats']
            print(f"   {i}. {nom:<25} | {nb_achats:>2d} achats | {total:>10,.0f} DH")
        
        print("\n🎯 DONNÉES DE TEST PRÊTES POUR LES TESTS !")
        print("=" * 60)
        print("✅ Vous pouvez maintenant tester :")
        print("   • 👥 Gestion des clients (10 clients)")
        print("   • 📦 Gestion des produits (17 produits)")
        print("   • 🛒 Système de ventes (20 ventes)")
        print("   • 🔄 Modification des statuts")
        print("   • 📊 Génération de rapports")
        print("   • 💳 Différents modes de paiement")
        print("\n🚀 Commandes utiles :")
        print("   • python launch_simple.py          # Application complète")
        print("   • python launch_ventes.py          # Module ventes direct")
        print("   • python test_ventes_ameliorees.py # Test des améliorations")
        
    except Exception as e:
        print(f"❌ Erreur lors de l'affichage: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if db_manager.connection:
            db_manager.disconnect()

if __name__ == "__main__":
    afficher_donnees_test()