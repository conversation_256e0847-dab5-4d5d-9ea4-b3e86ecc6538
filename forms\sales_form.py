"""
Formulaire de vente simplifié et fonctionnel
"""

from kivymd.uix.dialog import MDDialog
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.label import <PERSON><PERSON>abel
from kivymd.uix.textfield import MD<PERSON>extField
from kivymd.uix.button import MDRaisedButton, MDFlatButton, MDIconButton
from kivymd.uix.snackbar import MDSnackbar
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.card import MDCard
from kivymd.uix.list import MDList, OneLineAvatarIconListItem, IconLeftWidget, IconRightWidget
from kivymd.uix.gridlayout import MDGridLayout
from database.db_manager import DatabaseManager
from datetime import datetime
import uuid


class SalesFormDialog(MDDialog):
    """Formulaire de vente simplifié et fonctionnel"""
    
    def __init__(self, sale_data=None, on_save_callback=None, **kwargs):
        self.sale_data = sale_data or {}
        self.on_save_callback = on_save_callback
        self.db_manager = DatabaseManager()
        
        # Déterminer le mode (création ou modification)
        self.is_edit_mode = bool(self.sale_data and self.sale_data.get('id'))
        
        # Données pour les listes déroulantes
        self.clients_list = []
        self.filtered_clients_list = []
        self.selected_client = None
        
        # Données pour les produits
        self.products_list = []
        self.filtered_products_list = []
        self.selected_products = []  # Liste des produits sélectionnés avec quantités
        
        # Modes de paiement
        self.payment_modes = [
            "💰 Espèces",
            "💳 Carte bancaire", 
            "📄 Chèque",
            "🏦 Virement bancaire",
            "📱 Paiement électronique",
            "💸 Crédit"
        ]
        
        # Charger les données et créer le contenu
        self.load_clients()
        self.load_products()
        content = self.create_content()
        
        # Créer les boutons
        cancel_btn = MDFlatButton(
            text="❌ Annuler",
            on_release=self.dismiss
        )
        
        save_btn = MDRaisedButton(
            text="💾 Enregistrer",
            on_release=self.save_sale
        )
        
        super().__init__(
            title="✏️ Modifier la vente" if self.is_edit_mode else "🛒 Nouvelle vente",
            type="custom",
            content_cls=content,
            size_hint=(0.95, 0.85),  # 95% largeur, 85% hauteur pour afficher tout le contenu
            buttons=[cancel_btn, save_btn],
            **kwargs
        )
    
    def create_content(self):
        """Créer le contenu du formulaire avec hauteurs fixes"""
        # Container principal avec scroll pour les produits
        scroll_view = MDScrollView(
            size_hint=(1, 1),
            do_scroll_x=False,
            do_scroll_y=True
        )
        
        main_layout = MDBoxLayout(
            orientation='vertical',
            spacing="12dp",
            padding="20dp",
            size_hint_y=None,
            adaptive_height=True
        )
        
        # En-tête
        header = self.create_header()
        main_layout.add_widget(header)
        
        # Section client
        client_section = self.create_client_section()
        main_layout.add_widget(client_section)
        
        # Section produits (NOUVELLE)
        products_section = self.create_products_section()
        main_layout.add_widget(products_section)
        
        # Section montants (calculés automatiquement)
        montants_section = self.create_montants_section()
        main_layout.add_widget(montants_section)
        
        # Section paiement
        paiement_section = self.create_paiement_section()
        main_layout.add_widget(paiement_section)
        
        # Section notes
        notes_section = self.create_notes_section()
        main_layout.add_widget(notes_section)
        
        # Section informations (pour les modifications)
        if self.is_edit_mode:
            info_section = self.create_info_section()
            main_layout.add_widget(info_section)
        
        scroll_view.add_widget(main_layout)
        return scroll_view
    
    def create_header(self):
        """Créer l'en-tête"""
        header_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="60dp"  # Réduit pour économiser l'espace
        )
        
        title = MDLabel(
            text="🛒 Formulaire de Vente",
            font_style="H6",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="40dp"
        )
        
        if self.is_edit_mode:
            numero = self.sale_data.get('numero_facture', 'N/A')
            subtitle = MDLabel(
                text=f"📄 Facture: {numero}",
                font_style="Subtitle2",
                theme_text_color="Secondary",
                halign="center",
                size_hint_y=None,
                height="40dp"
            )
            header_layout.add_widget(subtitle)
        
        header_layout.add_widget(title)
        return header_layout
    
    def create_client_section(self):
        """Créer la section client avec recherche et liste déroulante"""
        client_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="160dp"  # Hauteur augmentée pour inclure le champ de recherche
        )
        
        client_label = MDLabel(
            text="👤 Client (OBLIGATOIRE)",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        # Champ de recherche client
        self.client_search_field = MDTextField(
            hint_text="🔍 Rechercher un client (nom, prénom, entreprise...)",
            mode="rectangle",
            size_hint_y=None,
            height="56dp",
            on_text=self.on_client_search_text_change
        )
        
        # Container pour la liste déroulante avec style moderne
        dropdown_container = MDCard(
            elevation=2,
            radius=[8],
            ripple_behavior=True,
            on_release=self.open_client_dropdown,
            size_hint_y=None,
            height="48dp",
            md_bg_color=(0.95, 0.95, 0.95, 1)  # Gris clair
        )
        
        # Layout interne de la carte
        internal_layout = MDBoxLayout(
            orientation='horizontal',
            spacing="8dp",
            padding=("16dp", "8dp"),
            adaptive_height=True
        )
        
        # Label pour afficher le client sélectionné
        self.client_label = MDLabel(
            text="🔽 Sélectionner un client",
            font_style="Body1",
            theme_text_color="Hint",
            valign="center"
        )
        
        # Icône de liste déroulante
        dropdown_icon = MDLabel(
            text="▼",
            font_style="Caption",
            theme_text_color="Primary",
            size_hint=(None, None),
            size=("24dp", "24dp"),
            halign="center",
            valign="center"
        )
        
        internal_layout.add_widget(self.client_label)
        internal_layout.add_widget(dropdown_icon)
        dropdown_container.add_widget(internal_layout)
        
        # Pré-remplir si modification ou client pré-sélectionné
        if self.selected_client:
            nom_complet = f"{self.selected_client.get('prenom', '')} {self.selected_client.get('nom', '')}".strip()
            if not nom_complet:
                nom_complet = self.selected_client.get('entreprise', f"Client {self.selected_client.get('id', '')}")
            
            # Mettre à jour le label
            self.client_label.text = nom_complet
            self.client_label.theme_text_color = "Primary"
            
            # Pré-remplir le champ de recherche
            self.client_search_field.text = nom_complet
        
        # Référence pour le menu
        self.client_card = dropdown_container
        
        # Initialiser la liste filtrée
        self.filtered_clients_list = self.clients_list.copy()
        
        # Créer le menu des clients
        self.create_client_menu()
        
        client_layout.add_widget(client_label)
        client_layout.add_widget(self.client_search_field)
        client_layout.add_widget(self.client_card)
        
        return client_layout
    
    def create_products_section(self):
        """Créer la section de sélection des produits"""
        products_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            adaptive_height=True
        )
        
        # En-tête de la section
        products_header = MDBoxLayout(
            orientation='horizontal',
            spacing="8dp",
            size_hint_y=None,
            height="40dp"
        )
        
        products_label = MDLabel(
            text="🛍️ Produits",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        add_product_btn = MDIconButton(
            icon="plus",
            theme_icon_color="Primary",
            on_release=self.open_product_dropdown,
            size_hint=(None, None),
            size=("40dp", "40dp")
        )
        
        products_header.add_widget(products_label)
        products_header.add_widget(add_product_btn)
        
        # Champ de recherche produit
        self.product_search_field = MDTextField(
            hint_text="🔍 Rechercher un produit (nom, référence...)",
            mode="rectangle",
            size_hint_y=None,
            height="56dp",
            on_text=self.on_product_search_text_change
        )
        
        # Container pour la liste déroulante avec style moderne
        product_dropdown_container = MDCard(
            elevation=2,
            radius=[8],
            ripple_behavior=True,
            on_release=self.open_product_dropdown,
            size_hint_y=None,
            height="48dp",
            md_bg_color=(0.95, 0.95, 0.95, 1)  # Gris clair
        )
        
        # Layout interne de la carte
        product_internal_layout = MDBoxLayout(
            orientation='horizontal',
            spacing="8dp",
            padding=("16dp", "8dp"),
            adaptive_height=True
        )
        
        # Label pour afficher le produit sélectionné
        self.product_dropdown_label = MDLabel(
            text="🔽 Sélectionner un produit",
            font_style="Body1",
            theme_text_color="Hint",
            valign="center"
        )
        
        # Icône de liste déroulante
        product_dropdown_icon = MDLabel(
            text="▼",
            font_style="Caption",
            theme_text_color="Primary",
            size_hint=(None, None),
            size=("24dp", "24dp"),
            halign="center",
            valign="center"
        )
        
        product_internal_layout.add_widget(self.product_dropdown_label)
        product_internal_layout.add_widget(product_dropdown_icon)
        product_dropdown_container.add_widget(product_internal_layout)
        
        # Référence pour le menu
        self.product_dropdown_card = product_dropdown_container
        
        # Initialiser la liste filtrée
        self.filtered_products_list = self.products_list.copy()
        
        # Créer le menu des produits
        self.create_product_menu()
        
        # Liste des produits sélectionnés
        self.products_container = MDBoxLayout(
            orientation='vertical',
            spacing="4dp",
            size_hint_y=None,
            adaptive_height=True
        )
        
        # Message si aucun produit
        self.no_products_label = MDLabel(
            text="Aucun produit sélectionné. Utilisez la recherche ci-dessus pour ajouter.",
            font_style="Caption",
            theme_text_color="Hint",
            halign="center",
            size_hint_y=None,
            height="40dp"
        )
        self.products_container.add_widget(self.no_products_label)
        
        products_layout.add_widget(products_header)
        products_layout.add_widget(self.product_search_field)
        products_layout.add_widget(product_dropdown_container)
        products_layout.add_widget(self.products_container)
        
        return products_layout
    
    def create_montants_section(self):
        """Créer la section des montants"""
        montants_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="140dp"  # Hauteur augmentée pour meilleure visibilité
        )
        
        montants_label = MDLabel(
            text="💰 Montants (Calculés automatiquement)",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        # Montant HT (lecture seule)
        self.montant_ht_field = MDTextField(
            text="0.00",
            hint_text="Montant HT (DH) - Calculé automatiquement",
            mode="rectangle",
            readonly=True,
            size_hint_y=None,
            height="40dp"
        )
        
        # Montant TTC (lecture seule)
        self.montant_ttc_field = MDTextField(
            text="0.00",
            hint_text="Montant TTC (DH) - Calculé automatiquement",
            mode="rectangle",
            readonly=True,
            size_hint_y=None,
            height="40dp"
        )
        
        montants_layout.add_widget(montants_label)
        montants_layout.add_widget(self.montant_ht_field)
        montants_layout.add_widget(self.montant_ttc_field)
        
        return montants_layout
    
    def create_paiement_section(self):
        """Créer la section paiement"""
        paiement_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="100dp"  # Hauteur augmentée pour meilleure visibilité
        )
        
        paiement_label = MDLabel(
            text="💳 Mode de Paiement",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        # Champ paiement avec menu déroulant
        self.paiement_field = MDTextField(
            text=self.sale_data.get('mode_paiement', '💰 Espèces') if self.is_edit_mode else '💰 Espèces',
            hint_text="Mode de paiement",
            mode="rectangle",
            readonly=True,
            size_hint_y=None,
            height="56dp"
        )
        
        # Créer le menu des modes de paiement
        self.create_paiement_menu()
        
        paiement_layout.add_widget(paiement_label)
        paiement_layout.add_widget(self.paiement_field)
        
        return paiement_layout
    
    def create_notes_section(self):
        """Créer la section notes"""
        notes_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="120dp"  # Hauteur augmentée pour meilleure visibilité
        )
        
        notes_label = MDLabel(
            text="📝 Notes (Optionnel)",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        self.notes_field = MDTextField(
            text=self.sale_data.get('notes', '') if self.is_edit_mode else '',
            hint_text="Notes sur la vente",
            mode="rectangle",
            multiline=True,
            size_hint_y=None,
            height="80dp"
        )
        
        notes_layout.add_widget(notes_label)
        notes_layout.add_widget(self.notes_field)
        
        return notes_layout
    
    def create_info_section(self):
        """Créer la section informations (modification)"""
        info_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="80dp"
        )
        
        info_label = MDLabel(
            text="ℹ️ Informations",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        # Date de vente
        date_vente = self.sale_data.get('date_vente', '')
        if date_vente:
            try:
                if isinstance(date_vente, str):
                    date_obj = datetime.fromisoformat(date_vente.replace('Z', '+00:00'))
                    date_formatted = date_obj.strftime('%d/%m/%Y %H:%M')
                else:
                    date_formatted = str(date_vente)[:16]
            except:
                date_formatted = str(date_vente)[:16]
        else:
            date_formatted = "Non définie"
        
        date_info = MDLabel(
            text=f"📅 Date: {date_formatted} | 🆔 ID: {self.sale_data.get('id', 'N/A')}",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="48dp"
        )
        
        info_layout.add_widget(info_label)
        info_layout.add_widget(date_info)
        
        return info_layout
    
    def load_clients(self):
        """Charger la liste des clients"""
        try:
            if not self.db_manager.connect():
                print("⚠️ Base de données non disponible - utilisation de clients de test")
                # Utiliser des clients de test si la base n'est pas disponible
                self.clients_list = [
                    {'id': 1, 'nom': 'Dupont', 'prenom': 'Jean', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0123456789'},
                    {'id': 2, 'nom': 'Martin', 'prenom': 'Marie', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0987654321'},
                    {'id': 3, 'nom': '', 'prenom': '', 'entreprise': 'Entreprise Test SARL', 'email': '<EMAIL>', 'telephone': '0555123456'}
                ]
                # Initialiser la liste filtrée
                self.filtered_clients_list = self.clients_list.copy()
                return
            
            clients = self.db_manager.execute_query("""
                SELECT id, nom, prenom, entreprise, email, telephone
                FROM clients 
                ORDER BY nom, prenom
            """)
            
            self.clients_list = clients or []
            
            # Si aucun client en base, utiliser les clients de test
            if not self.clients_list:
                print("⚠️ Aucun client en base - utilisation de clients de test")
                self.clients_list = [
                    {'id': 1, 'nom': 'Dupont', 'prenom': 'Jean', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0123456789'},
                    {'id': 2, 'nom': 'Martin', 'prenom': 'Marie', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0987654321'},
                    {'id': 3, 'nom': '', 'prenom': '', 'entreprise': 'Entreprise Test SARL', 'email': '<EMAIL>', 'telephone': '0555123456'}
                ]
            
            # Initialiser la liste filtrée avec tous les clients
            self.filtered_clients_list = self.clients_list.copy()
            
            # Si modification ou client pré-sélectionné, trouver le client
            if self.sale_data.get('client_id'):
                for client in self.clients_list:
                    if client['id'] == self.sale_data['client_id']:
                        self.selected_client = client
                        break
                
                # Si client_data est fourni directement, l'utiliser
                if self.sale_data.get('client_data') and not self.selected_client:
                    self.selected_client = self.sale_data['client_data']
            
        except Exception as e:
            print(f"Erreur chargement clients: {e}")
            # En cas d'erreur, utiliser des clients de test
            self.clients_list = [
                {'id': 1, 'nom': 'Dupont', 'prenom': 'Jean', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0123456789'},
                {'id': 2, 'nom': 'Martin', 'prenom': 'Marie', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0987654321'}
            ]
            # Initialiser la liste filtrée
            self.filtered_clients_list = self.clients_list.copy()
        finally:
            try:
                self.db_manager.close()
            except:
                pass
    
    def load_products(self):
        """Charger la liste des produits"""
        try:
            if not self.db_manager.connect():
                print("⚠️ Base de données non disponible - utilisation de produits de test")
                self.products_list = [
                    {'id': 1, 'nom': 'Produit Test 1', 'reference': 'REF001', 'prix_vente': 25.99, 'stock_actuel': 10, 'tva': 20.0},
                    {'id': 2, 'nom': 'Produit Test 2', 'reference': 'REF002', 'prix_vente': 15.50, 'stock_actuel': 5, 'tva': 20.0},
                    {'id': 3, 'nom': 'Produit Test 3', 'reference': 'REF003', 'prix_vente': 45.00, 'stock_actuel': 8, 'tva': 20.0}
                ]
                # Initialiser la liste filtrée
                self.filtered_products_list = self.products_list.copy()
                return
            
            products = self.db_manager.execute_query("""
                SELECT p.id, p.nom, p.reference, p.prix_vente, p.stock_actuel, p.tva, c.nom as categorie_nom
                FROM produits p
                LEFT JOIN categories c ON p.categorie_id = c.id
                WHERE p.actif = 1 AND p.stock_actuel > 0
                ORDER BY p.nom
            """)
            
            self.products_list = products or []
            
            if not self.products_list:
                print("⚠️ Aucun produit en stock - utilisation de produits de test")
                self.products_list = [
                    {'id': 1, 'nom': 'Produit Test 1', 'reference': 'REF001', 'prix_vente': 25.99, 'stock_actuel': 10, 'tva': 20.0},
                    {'id': 2, 'nom': 'Produit Test 2', 'reference': 'REF002', 'prix_vente': 15.50, 'stock_actuel': 5, 'tva': 20.0},
                    {'id': 3, 'nom': 'Produit Test 3', 'reference': 'REF003', 'prix_vente': 45.00, 'stock_actuel': 8, 'tva': 20.0}
                ]
            
            # Initialiser la liste filtrée
            self.filtered_products_list = self.products_list.copy()
            
        except Exception as e:
            print(f"Erreur chargement produits: {e}")
            self.products_list = [
                {'id': 1, 'nom': 'Produit Test 1', 'reference': 'REF001', 'prix_vente': 25.99, 'stock_actuel': 10, 'tva': 20.0},
                {'id': 2, 'nom': 'Produit Test 2', 'reference': 'REF002', 'prix_vente': 15.50, 'stock_actuel': 5, 'tva': 20.0}
            ]
            # Initialiser la liste filtrée
            self.filtered_products_list = self.products_list.copy()
        finally:
            try:
                self.db_manager.close()
            except:
                pass
    
    def open_client_dropdown(self, *args):
        """Ouvrir la liste déroulante des clients"""
        if hasattr(self, 'client_menu'):
            self.client_menu.open()
    
    def create_client_menu(self):
        """Créer le menu des clients basé sur la liste filtrée"""
        client_menu_items = []
        
        # Utiliser la liste filtrée au lieu de la liste complète
        clients_to_show = getattr(self, 'filtered_clients_list', self.clients_list)
        
        for client in clients_to_show:
            nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
            if not nom_complet:
                nom_complet = client.get('entreprise', f"Client {client.get('id', '')}")
            
            # Ajouter des informations supplémentaires pour faciliter l'identification
            details = []
            if client.get('entreprise') and nom_complet != client.get('entreprise'):
                details.append(client.get('entreprise'))
            if client.get('telephone'):
                details.append(client.get('telephone'))
            
            display_text = nom_complet
            if details:
                display_text += f" ({', '.join(details)})"
            
            client_menu_items.append({
                "text": display_text,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=client: self.select_client(x)
            })
        
        # Ajouter un message si aucun client trouvé
        if not client_menu_items:
            client_menu_items.append({
                "text": "Aucun client trouvé",
                "viewclass": "OneLineListItem",
                "on_release": lambda x: None
            })
        
        # Recréer le menu avec les nouveaux éléments
        if hasattr(self, 'client_menu'):
            self.client_menu.dismiss()
        
        self.client_menu = MDDropdownMenu(
            caller=self.client_card,
            items=client_menu_items,
            max_height="200dp"
        )
    
    def create_paiement_menu(self):
        """Créer le menu des modes de paiement"""
        paiement_menu_items = []
        
        for mode in self.payment_modes:
            paiement_menu_items.append({
                "text": mode,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=mode: self.select_paiement(x)
            })
        
        self.paiement_menu = MDDropdownMenu(
            caller=self.paiement_field,
            items=paiement_menu_items,
            max_height="200dp"
        )
        
        self.paiement_field.bind(on_release=self.paiement_menu.open)
    
    def on_client_search_text_change(self, instance, text):
        """Gérer les changements dans le champ de recherche client"""
        search_text = text.lower().strip()
        
        if not search_text:
            # Si le champ est vide, afficher tous les clients
            self.filtered_clients_list = self.clients_list.copy()
        else:
            # Filtrer les clients selon le texte de recherche
            self.filtered_clients_list = []
            for client in self.clients_list:
                # Rechercher dans nom, prénom, entreprise, email et téléphone
                # Gérer les valeurs NULL de la base de données
                searchable_fields = [
                    (client.get('nom') or '').lower(),
                    (client.get('prenom') or '').lower(),
                    (client.get('entreprise') or '').lower(),
                    (client.get('email') or '').lower(),
                    (client.get('telephone') or '').lower()
                ]
                
                # Vérifier si le texte de recherche est présent dans l'un des champs
                if any(search_text in field for field in searchable_fields):
                    self.filtered_clients_list.append(client)
        
        # Mettre à jour le menu des clients
        self.create_client_menu()
        
        # Si un seul client correspond exactement, le sélectionner automatiquement
        if len(self.filtered_clients_list) == 1:
            exact_match = self.filtered_clients_list[0]
            nom_complet = f"{exact_match.get('prenom', '')} {exact_match.get('nom', '')}".strip()
            if not nom_complet:
                nom_complet = exact_match.get('entreprise', f"Client {exact_match.get('id', '')}")
            
            # Vérifier si le texte de recherche correspond exactement au nom complet
            if search_text == nom_complet.lower():
                self.select_client(exact_match)
    
    def select_client(self, client):
        """Sélectionner un client"""
        self.selected_client = client
        nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
        if not nom_complet:
            nom_complet = client.get('entreprise', f"Client {client.get('id', '')}")
        
        # Mettre à jour le label
        self.client_label.text = nom_complet
        self.client_label.theme_text_color = "Primary"
        
        # Mettre à jour le champ de recherche avec le nom sélectionné
        self.client_search_field.text = nom_complet
        
        # Réinitialiser la liste filtrée pour afficher tous les clients
        self.filtered_clients_list = self.clients_list.copy()
        self.create_client_menu()
        
        self.client_menu.dismiss()
    
    def on_product_search_text_change(self, instance, text):
        """Gérer les changements dans le champ de recherche produit"""
        search_text = text.lower().strip()
        
        if not search_text:
            # Si le champ est vide, afficher tous les produits
            self.filtered_products_list = self.products_list.copy()
        else:
            # Filtrer les produits selon le texte de recherche
            self.filtered_products_list = []
            for product in self.products_list:
                # Rechercher dans nom, référence et description
                # Gérer les valeurs NULL de la base de données
                searchable_fields = [
                    (product.get('nom') or '').lower(),
                    (product.get('reference') or '').lower(),
                    (product.get('description') or '').lower(),
                    (product.get('categorie_nom') or '').lower()
                ]
                
                # Vérifier si le texte de recherche est présent dans l'un des champs
                if any(search_text in field for field in searchable_fields):
                    self.filtered_products_list.append(product)
        
        # Mettre à jour le menu des produits
        self.create_product_menu()
        
        # Si un seul produit correspond exactement, le sélectionner automatiquement
        if len(self.filtered_products_list) == 1:
            exact_match = self.filtered_products_list[0]
            product_name = exact_match.get('nom', '')
            
            # Vérifier si le texte de recherche correspond exactement au nom du produit
            if search_text == product_name.lower():
                self.add_product_to_sale_from_search(exact_match)
    
    def create_product_menu(self):
        """Créer le menu des produits basé sur la liste filtrée"""
        product_menu_items = []
        
        # Utiliser la liste filtrée au lieu de la liste complète
        products_to_show = getattr(self, 'filtered_products_list', self.products_list)
        
        for product in products_to_show:
            # Vérifier si le produit n'est pas déjà sélectionné
            already_selected = any(p['id'] == product['id'] for p in self.selected_products)
            if already_selected:
                continue
                
            # Créer le texte d'affichage avec plus d'informations
            product_text = f"{product['nom']} - {product['prix_vente']:.2f} DH"
            if product.get('reference'):
                product_text += f" ({product['reference']})"
            if product.get('stock_actuel') is not None:
                product_text += f" - Stock: {product['stock_actuel']}"
            
            product_menu_items.append({
                "text": product_text,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=product: self.add_product_to_sale_from_dropdown(x)
            })
        
        # Ajouter un message si aucun produit trouvé
        if not product_menu_items:
            product_menu_items.append({
                "text": "Aucun produit trouvé",
                "viewclass": "OneLineListItem",
                "on_release": lambda x: None
            })
        
        # Recréer le menu avec les nouveaux éléments
        if hasattr(self, 'product_dropdown_menu'):
            self.product_dropdown_menu.dismiss()
        
        self.product_dropdown_menu = MDDropdownMenu(
            caller=self.product_dropdown_card,
            items=product_menu_items,
            max_height="300dp"
        )
    
    def open_product_dropdown(self, *args):
        """Ouvrir le menu déroulant des produits"""
        if not self.products_list:
            self.show_error("Aucun produit disponible")
            return
        
        # S'assurer que le menu est créé
        if not hasattr(self, 'product_dropdown_menu') or not self.product_dropdown_menu:
            self.create_product_menu()
        
        # Ouvrir le menu
        if hasattr(self, 'product_dropdown_menu') and self.product_dropdown_menu:
            self.product_dropdown_menu.open()
        else:
            self.show_error("Erreur lors de la création du menu des produits")
    
    def add_product_to_sale_from_search(self, product):
        """Ajouter un produit à la vente depuis la recherche"""
        self.add_product_to_sale(product)
        # Vider le champ de recherche après ajout
        self.product_search_field.text = ""
        # Réinitialiser la liste filtrée
        self.filtered_products_list = self.products_list.copy()
        self.create_product_menu()
    
    def add_product_to_sale_from_dropdown(self, product):
        """Ajouter un produit à la vente depuis le menu déroulant"""
        self.product_dropdown_menu.dismiss()
        self.add_product_to_sale(product)
        # Vider le champ de recherche après ajout
        self.product_search_field.text = ""
        # Réinitialiser la liste filtrée
        self.filtered_products_list = self.products_list.copy()
        self.create_product_menu()
    
    def add_product_to_sale(self, product):
        """Ajouter un produit à la vente"""
        # Fermer les menus s'ils sont ouverts
        if hasattr(self, 'product_menu') and self.product_menu:
            self.product_menu.dismiss()
        if hasattr(self, 'product_dropdown_menu') and self.product_dropdown_menu:
            self.product_dropdown_menu.dismiss()
        
        # Ajouter le produit avec quantité 1 par défaut
        product_item = {
            'id': product['id'],
            'nom': product['nom'],
            'reference': product.get('reference', ''),
            'prix_unitaire': product['prix_vente'],
            'quantite': 1,
            'tva': product.get('tva', 20.0),
            'stock_actuel': product.get('stock_actuel', 0)
        }
        
        self.selected_products.append(product_item)
        self.update_products_display()
        self.calculate_totals()
    
    def update_products_display(self):
        """Mettre à jour l'affichage des produits sélectionnés"""
        # Vider le container
        self.products_container.clear_widgets()
        
        if not self.selected_products:
            self.products_container.add_widget(self.no_products_label)
            return
        
        # Ajouter chaque produit sélectionné
        for i, product in enumerate(self.selected_products):
            product_card = self.create_product_card(product, i)
            self.products_container.add_widget(product_card)
    
    def create_product_card(self, product, index):
        """Créer une carte pour un produit sélectionné"""
        card = MDCard(
            elevation=1,
            radius=[5],
            size_hint_y=None,
            height="80dp",
            padding="8dp"
        )
        
        layout = MDBoxLayout(
            orientation='horizontal',
            spacing="8dp"
        )
        
        # Informations du produit
        info_layout = MDBoxLayout(
            orientation='vertical',
            spacing="2dp"
        )
        
        product_name = MDLabel(
            text=f"{product['nom']} ({product.get('reference', 'N/A')})",
            font_style="Body2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="20dp"
        )
        
        product_price = MDLabel(
            text=f"{product['prix_unitaire']:.2f} DH × {product['quantite']} = {product['prix_unitaire'] * product['quantite']:.2f} DH",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="16dp"
        )
        
        info_layout.add_widget(product_name)
        info_layout.add_widget(product_price)
        
        # Contrôles de quantité
        controls_layout = MDBoxLayout(
            orientation='horizontal',
            spacing="4dp",
            size_hint=(None, None),
            size=("120dp", "40dp")
        )
        
        # Bouton diminuer
        minus_btn = MDIconButton(
            icon="minus",
            theme_icon_color="Primary",
            on_release=lambda x, idx=index: self.decrease_quantity(idx),
            size_hint=(None, None),
            size=("32dp", "32dp")
        )
        
        # Champ quantité
        quantity_field = MDTextField(
            text=str(product['quantite']),
            input_filter="int",
            size_hint=(None, None),
            size=("40dp", "32dp"),
            font_size="12sp",
            on_text_validate=lambda x, idx=index: self.update_quantity(idx, x.text)
        )
        
        # Bouton augmenter
        plus_btn = MDIconButton(
            icon="plus",
            theme_icon_color="Primary",
            on_release=lambda x, idx=index: self.increase_quantity(idx),
            size_hint=(None, None),
            size=("32dp", "32dp")
        )
        
        controls_layout.add_widget(minus_btn)
        controls_layout.add_widget(quantity_field)
        controls_layout.add_widget(plus_btn)
        
        # Bouton supprimer
        delete_btn = MDIconButton(
            icon="delete",
            theme_icon_color="Error",
            on_release=lambda x, idx=index: self.remove_product(idx),
            size_hint=(None, None),
            size=("40dp", "40dp")
        )
        
        layout.add_widget(info_layout)
        layout.add_widget(controls_layout)
        layout.add_widget(delete_btn)
        
        card.add_widget(layout)
        return card
    
    def increase_quantity(self, index):
        """Augmenter la quantité d'un produit"""
        if index < len(self.selected_products):
            product = self.selected_products[index]
            if product['quantite'] < product['stock_actuel']:
                product['quantite'] += 1
                self.update_products_display()
                self.calculate_totals()
            else:
                self.show_error(f"Stock insuffisant (max: {product['stock_actuel']})")
    
    def decrease_quantity(self, index):
        """Diminuer la quantité d'un produit"""
        if index < len(self.selected_products):
            product = self.selected_products[index]
            if product['quantite'] > 1:
                product['quantite'] -= 1
                self.update_products_display()
                self.calculate_totals()
    
    def update_quantity(self, index, new_quantity_text):
        """Mettre à jour la quantité d'un produit"""
        try:
            new_quantity = int(new_quantity_text)
            if new_quantity <= 0:
                self.show_error("La quantité doit être supérieure à 0")
                return
            
            if index < len(self.selected_products):
                product = self.selected_products[index]
                if new_quantity <= product['stock_actuel']:
                    product['quantite'] = new_quantity
                    self.update_products_display()
                    self.calculate_totals()
                else:
                    self.show_error(f"Stock insuffisant (max: {product['stock_actuel']})")
                    self.update_products_display()  # Restaurer l'ancienne valeur
        except ValueError:
            self.show_error("Quantité invalide")
            self.update_products_display()  # Restaurer l'ancienne valeur
    
    def remove_product(self, index):
        """Supprimer un produit de la sélection"""
        if index < len(self.selected_products):
            self.selected_products.pop(index)
            self.update_products_display()
            self.calculate_totals()
    
    def calculate_totals(self):
        """Calculer les totaux automatiquement"""
        if not hasattr(self, 'montant_ht_field') or not hasattr(self, 'montant_ttc_field'):
            return
        
        total_ht = 0
        total_tva = 0
        
        for product in self.selected_products:
            ligne_ht = product['prix_unitaire'] * product['quantite']
            ligne_tva = ligne_ht * (product['tva'] / 100)
            total_ht += ligne_ht
            total_tva += ligne_tva
        
        total_ttc = total_ht + total_tva
        
        # Mettre à jour les champs
        self.montant_ht_field.text = f"{total_ht:.2f}"
        self.montant_ttc_field.text = f"{total_ttc:.2f}"
    
    def select_paiement(self, mode):
        """Sélectionner un mode de paiement"""
        self.paiement_field.text = mode
        self.paiement_menu.dismiss()
    
    def validate_form(self):
        """Valider le formulaire"""
        errors = []
        
        # Client obligatoire
        if not self.selected_client:
            errors.append("Veuillez sélectionner un client")
        
        # Produits obligatoires
        if not self.selected_products:
            errors.append("Veuillez sélectionner au moins un produit")
        
        # Vérifier les quantités des produits
        for i, product in enumerate(self.selected_products):
            if product['quantite'] <= 0:
                errors.append(f"Quantité invalide pour {product['nom']}")
            if product['quantite'] > product['stock_actuel']:
                errors.append(f"Stock insuffisant pour {product['nom']} (disponible: {product['stock_actuel']})")
        
        # Montant TTC doit être > 0 (calculé automatiquement)
        try:
            montant_ttc = float(self.montant_ttc_field.text or 0)
            if montant_ttc <= 0:
                errors.append("Le montant total doit être supérieur à 0")
        except ValueError:
            errors.append("Erreur de calcul des montants")
        
        return errors
    
    def save_sale(self, *args):
        """Sauvegarder la vente"""
        # Validation
        errors = self.validate_form()
        if errors:
            self.show_error("Erreurs de validation:\n" + "\n".join(f"• {error}" for error in errors))
            return
        
        try:
            # Préparer les données
            montant_ttc = float(self.montant_ttc_field.text or 0)
            montant_ht = float(self.montant_ht_field.text or montant_ttc)
            
            # Nettoyer le mode de paiement (enlever l'emoji)
            mode_paiement = self.paiement_field.text
            for emoji_mode in self.payment_modes:
                if mode_paiement == emoji_mode:
                    mode_paiement = emoji_mode.split(' ', 1)[1]  # Enlever l'emoji
                    break
            
            # Calculer la TVA
            montant_tva = montant_ttc - montant_ht
            
            sale_data = {
                'client_id': self.selected_client['id'],
                'montant_ht': montant_ht,
                'montant_tva': montant_tva,
                'montant_ttc': montant_ttc,
                'mode_paiement': mode_paiement,
                'notes': self.notes_field.text.strip(),
                'numero_facture': f"FAC-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}",
                'produits': self.selected_products  # Ajouter les produits
            }
            
            if self.is_edit_mode:
                # Modification
                success = self.update_sale(self.sale_data['id'], sale_data)
                if success:
                    self.show_success("Vente modifiée avec succès")
                    result_data = {'id': self.sale_data['id'], **sale_data}
                else:
                    self.show_error("Erreur lors de la modification")
                    return
            else:
                # Création
                sale_id = self.create_sale_with_products(sale_data)
                if sale_id:
                    self.show_success("Vente créée avec succès")
                    result_data = {'id': sale_id, **sale_data}
                else:
                    self.show_error("Erreur lors de la création")
                    return
            
            # Callback
            if self.on_save_callback:
                self.on_save_callback(result_data)
            
            self.dismiss()
            
        except Exception as e:
            self.show_error(f"Erreur inattendue: {str(e)}")
    
    def create_sale_with_products(self, sale_data):
        """Créer une nouvelle vente avec ses produits"""
        try:
            if not self.db_manager.connect():
                print("⚠️ Base de données non disponible - simulation de création")
                return 999  # ID simulé pour les tests
            
            cursor = self.db_manager.connection.cursor()
            
            # Commencer une transaction
            cursor.execute("BEGIN TRANSACTION")
            
            try:
                # 1. Insérer la vente
                cursor.execute("""
                    INSERT INTO ventes (
                        numero_facture, client_id, montant_ht, montant_tva, 
                        montant_ttc, mode_paiement, notes, statut
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, 'Validée')
                """, (
                    sale_data['numero_facture'],
                    sale_data['client_id'],
                    sale_data['montant_ht'],
                    sale_data['montant_tva'],
                    sale_data['montant_ttc'],
                    sale_data['mode_paiement'],
                    sale_data['notes']
                ))
                
                sale_id = cursor.lastrowid
                
                # 2. Insérer les détails de vente (produits)
                for product in sale_data['produits']:
                    montant_ligne = product['prix_unitaire'] * product['quantite']
                    
                    cursor.execute("""
                        INSERT INTO details_vente (
                            vente_id, produit_id, quantite, prix_unitaire, 
                            remise, montant_ligne
                        ) VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        sale_id,
                        product['id'],
                        product['quantite'],
                        product['prix_unitaire'],
                        0,  # Pas de remise pour l'instant
                        montant_ligne
                    ))
                    
                    # 3. Mettre à jour le stock
                    cursor.execute("""
                        UPDATE produits 
                        SET stock_actuel = stock_actuel - ? 
                        WHERE id = ?
                    """, (product['quantite'], product['id']))
                    
                    # 4. Enregistrer le mouvement de stock
                    cursor.execute("""
                        INSERT INTO mouvements_stock (
                            produit_id, type_mouvement, quantite, prix_unitaire,
                            reference_document, commentaire
                        ) VALUES (?, 'SORTIE', ?, ?, ?, ?)
                    """, (
                        product['id'],
                        product['quantite'],
                        product['prix_unitaire'],
                        sale_data['numero_facture'],
                        f"Vente - {product['nom']}"
                    ))
                
                # Valider la transaction
                cursor.execute("COMMIT")
                print(f"✅ Vente créée avec succès - ID: {sale_id}")
                return sale_id
                
            except Exception as e:
                # Annuler la transaction en cas d'erreur
                cursor.execute("ROLLBACK")
                print(f"❌ Erreur lors de la création de la vente: {e}")
                return None
                
        except Exception as e:
            print(f"❌ Erreur de connexion: {e}")
            return None
        finally:
            try:
                self.db_manager.close()
            except:
                pass
    
    def create_sale(self, sale_data):
        """Créer une nouvelle vente"""
        try:
            if not self.db_manager.connect():
                print("⚠️ Base de données non disponible - simulation de création")
                # Simuler la création pour les tests
                numero_facture = f"FAC-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
                sale_data['numero_facture'] = numero_facture
                return 999  # ID fictif pour les tests
            
            # Générer un numéro de facture unique
            numero_facture = f"FAC-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
            
            sale_id = self.db_manager.execute_insert("""
                INSERT INTO ventes (numero_facture, client_id, montant_ht, montant_ttc, mode_paiement, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                numero_facture,
                sale_data['client_id'],
                sale_data['montant_ht'],
                sale_data['montant_ttc'],
                sale_data['mode_paiement'],
                sale_data['notes']
            ))
            
            return sale_id
            
        except Exception as e:
            print(f"Erreur création vente: {e}")
            return None
        finally:
            try:
                self.db_manager.close()
            except:
                pass
    
    def update_sale(self, sale_id, sale_data):
        """Mettre à jour une vente"""
        try:
            if not self.db_manager.connect():
                return False
            
            success = self.db_manager.execute_update("""
                UPDATE ventes 
                SET client_id = ?, montant_ht = ?, montant_ttc = ?, mode_paiement = ?, notes = ?
                WHERE id = ?
            """, (
                sale_data['client_id'],
                sale_data['montant_ht'],
                sale_data['montant_ttc'],
                sale_data['mode_paiement'],
                sale_data['notes'],
                sale_id
            ))
            
            return success
            
        except Exception as e:
            print(f"Erreur modification vente: {e}")
            return False
        finally:
            self.db_manager.close()
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"❌ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"❌ {message}")
    
    def show_success(self, message):
        """Afficher un message de succès"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"✅ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"✅ {message}")