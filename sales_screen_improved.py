"""
Écran de gestion des ventes amélioré avec nouveau formulaire
Utilise le formulaire de vente avec listes déroulantes
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.snackbar import MDSnackbar
from kivymd.uix.dialog import MDDialog
from kivymd.app import MDApp
from kivy.clock import Clock
from datetime import datetime
import threading
from database.db_manager import DatabaseManager
from sales_form_improved import ImprovedSaleFormDialog


class ImprovedSaleCard(MDCard):
    """Carte d'affichage d'une vente améliorée"""
    
    def __init__(self, sale_data, on_edit_callback=None, on_delete_callback=None, **kwargs):
        super().__init__(**kwargs)
        self.sale_data = sale_data
        self.on_edit_callback = on_edit_callback
        self.on_delete_callback = on_delete_callback
        
        # Style de la carte
        self.elevation = 2
        self.radius = [8]
        self.size_hint_y = None
        self.height = "140dp"
        self.padding = "16dp"
        self.spacing = "8dp"
        
        self.create_content()
    
    def create_content(self):
        """Créer le contenu de la carte"""
        layout = MDBoxLayout(orientation='vertical')
        
        # En-tête avec numéro de facture et statut
        header_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="40dp"
        )
        
        # Numéro de facture
        numero_facture = self.sale_data.get('numero_facture', 'N/A')
        facture_label = MDLabel(
            text=f"📄 {numero_facture}",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_x=0.5
        )
        
        # Statut avec couleur
        statut = self.sale_data.get('statut', 'En cours')
        statut_colors = {
            'En cours': [1, 0.6, 0, 1],    # Orange
            'Payée': [0, 0.8, 0, 1],       # Vert
            'Annulée': [0.8, 0, 0, 1],     # Rouge
            'En attente': [0.6, 0.6, 0.6, 1]  # Gris
        }
        
        statut_label = MDLabel(
            text=f"📊 {statut}",
            font_style="Subtitle2",
            theme_text_color="Custom",
            text_color=statut_colors.get(statut, [0.5, 0.5, 0.5, 1]),
            size_hint_x=0.3,
            bold=True
        )
        
        # Boutons d'action
        buttons_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_x=0.2,
            spacing="8dp"
        )
        
        edit_btn = MDIconButton(
            icon="pencil",
            theme_icon_color="Custom",
            icon_color=[0.2, 0.6, 1, 1],
            on_release=self.edit_sale
        )
        
        delete_btn = MDIconButton(
            icon="delete",
            theme_icon_color="Custom",
            icon_color=[1, 0.3, 0.3, 1],
            on_release=self.delete_sale
        )
        
        buttons_layout.add_widget(edit_btn)
        buttons_layout.add_widget(delete_btn)
        
        header_layout.add_widget(facture_label)
        header_layout.add_widget(statut_label)
        header_layout.add_widget(buttons_layout)
        
        # Informations client et date
        client_date_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="32dp"
        )
        
        # Client (récupéré via la FOREIGN KEY)
        client_nom = self.sale_data.get('client_nom', 'Client non spécifié')
        client_label = MDLabel(
            text=f"👤 {client_nom}",
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_x=0.6
        )
        
        # Date de vente
        date_vente = self.sale_data.get('date_vente', '')
        if date_vente:
            try:
                if isinstance(date_vente, str):
                    date_obj = datetime.fromisoformat(date_vente.replace('Z', '+00:00'))
                    date_formatted = date_obj.strftime('%d/%m/%Y %H:%M')
                else:
                    date_formatted = str(date_vente)[:16]
            except:
                date_formatted = str(date_vente)[:16]
        else:
            date_formatted = "Date inconnue"
        
        date_label = MDLabel(
            text=f"📅 {date_formatted}",
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_x=0.4
        )
        
        client_date_layout.add_widget(client_label)
        client_date_layout.add_widget(date_label)
        
        # Montants et mode de paiement
        montants_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="32dp"
        )
        
        # Montants
        montant_ht = self.sale_data.get('montant_ht', 0)
        montant_ttc = self.sale_data.get('montant_ttc', 0)
        
        montants_label = MDLabel(
            text=f"💰 HT: {montant_ht:.2f} DH | TTC: {montant_ttc:.2f} DH",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_x=0.6
        )
        
        # Mode de paiement
        mode_paiement = self.sale_data.get('mode_paiement', 'Non spécifié')
        paiement_label = MDLabel(
            text=f"💳 {mode_paiement}",
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_x=0.4
        )
        
        montants_layout.add_widget(montants_label)
        montants_layout.add_widget(paiement_label)
        
        # Notes (si présentes)
        notes_layout = MDBoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height="24dp"
        )
        
        notes = self.sale_data.get('notes', '')
        if notes:
            if len(notes) > 60:
                notes = notes[:60] + "..."
            
            notes_label = MDLabel(
                text=f"📝 {notes}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="24dp"
            )
            notes_layout.add_widget(notes_label)
        
        layout.add_widget(header_layout)
        layout.add_widget(client_date_layout)
        layout.add_widget(montants_layout)
        layout.add_widget(notes_layout)
        
        self.add_widget(layout)
    
    def edit_sale(self, *args):
        """Modifier la vente"""
        if self.on_edit_callback:
            self.on_edit_callback(self.sale_data)
    
    def delete_sale(self, *args):
        """Supprimer la vente"""
        if self.on_delete_callback:
            self.on_delete_callback(self.sale_data)


class ImprovedSalesScreen(MDScreen):
    """Écran de gestion des ventes amélioré"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.db_manager = DatabaseManager()
        self.sales_data = []
        self.create_interface()
        self.load_sales()
    
    def create_interface(self):
        """Créer l'interface optimisée"""
        main_layout = MDBoxLayout(orientation='vertical')
        
        # Barre d'outils
        toolbar = MDTopAppBar(
            title="🛒 Gestion des Ventes",
            elevation=2
            # left_action_items supprimés temporairement pour éviter l'avertissement width_mult
        )
        
        # Contenu principal
        content_layout = MDBoxLayout(
            orientation='vertical',
            padding="16dp",
            spacing="16dp"
        )
        
        # Boutons d'action (remplacent les right_action_items)
        action_layout = MDBoxLayout(
            orientation='horizontal',
            spacing="16dp",
            size_hint_y=None,
            height="48dp"
        )
        
        add_btn = MDRaisedButton(
            text="➕ Nouvelle Vente",
            on_release=self.add_sale,
            size_hint_x=0.5
        )
        
        refresh_btn = MDRaisedButton(
            text="🔄 Actualiser",
            on_release=self.refresh_sales,
            size_hint_x=0.5
        )
        
        action_layout.add_widget(add_btn)
        action_layout.add_widget(refresh_btn)
        
        # Zone de défilement pour les ventes
        self.scroll_view = MDScrollView()
        self.sales_container = MDBoxLayout(
            orientation='vertical',
            spacing="12dp",
            size_hint_y=None,
            height="0dp"
        )
        self.sales_container.bind(minimum_height=self.sales_container.setter('height'))
        
        self.scroll_view.add_widget(self.sales_container)
        
        content_layout.add_widget(action_layout)
        content_layout.add_widget(self.scroll_view)
        
        main_layout.add_widget(toolbar)
        main_layout.add_widget(content_layout)
        
        self.add_widget(main_layout)
    
    def load_sales(self):
        """Charger les ventes depuis la base de données"""
        def load_in_background():
            try:
                if not self.db_manager.connect():
                    Clock.schedule_once(lambda dt: self.show_error("Impossible de se connecter à la base de données"))
                    return
                
                # Requête basée sur la structure de la table ventes avec JOIN pour le client
                sales = self.db_manager.execute_query("""
                    SELECT 
                        v.id,
                        v.numero_facture,
                        v.client_id,
                        v.date_vente,
                        v.montant_ht,
                        v.montant_ttc,
                        v.mode_paiement,
                        v.statut,
                        v.notes,
                        COALESCE(c.prenom || ' ' || c.nom, c.entreprise, 'Client inconnu') as client_nom
                    FROM ventes v
                    LEFT JOIN clients c ON v.client_id = c.id
                    ORDER BY v.date_vente DESC
                """)
                
                self.sales_data = sales or []
                Clock.schedule_once(lambda dt: self.update_sales_display())
                
            except Exception as e:
                Clock.schedule_once(lambda dt: self.show_error(f"Erreur lors du chargement: {str(e)}"))
            finally:
                self.db_manager.close()
        
        threading.Thread(target=load_in_background, daemon=True).start()
    
    def update_sales_display(self):
        """Mettre à jour l'affichage des ventes"""
        # Vider le conteneur
        self.sales_container.clear_widgets()
        
        if not self.sales_data:
            # Message si aucune vente
            no_data_label = MDLabel(
                text="🛒 Aucune vente trouvée\n\nCliquez sur ➕ pour créer votre première vente",
                font_style="H6",
                theme_text_color="Secondary",
                halign="center",
                size_hint_y=None,
                height="200dp"
            )
            self.sales_container.add_widget(no_data_label)
        else:
            # Ajouter les cartes de ventes
            for sale in self.sales_data:
                card = ImprovedSaleCard(
                    sale_data=sale,
                    on_edit_callback=self.edit_sale,
                    on_delete_callback=self.confirm_delete_sale
                )
                self.sales_container.add_widget(card)
    
    def add_sale(self, *args):
        """Ajouter une nouvelle vente avec le formulaire amélioré"""
        dialog = ImprovedSaleFormDialog(
            on_save_callback=self.on_sale_saved
        )
        dialog.open()
    
    def edit_sale(self, sale_data):
        """Modifier une vente existante avec le formulaire amélioré"""
        dialog = ImprovedSaleFormDialog(
            sale_data=sale_data,
            on_save_callback=self.on_sale_saved
        )
        dialog.open()
    
    def on_sale_saved(self, sale_data):
        """Callback appelé après sauvegarde d'une vente"""
        numero_facture = sale_data.get('numero_facture', 'N/A')
        self.show_success(f"Vente '{numero_facture}' sauvegardée avec succès")
        self.refresh_sales()
    
    def confirm_delete_sale(self, sale_data):
        """Confirmer la suppression d'une vente"""
        numero_facture = sale_data.get('numero_facture', 'N/A')
        montant_ttc = sale_data.get('montant_ttc', 0)
        
        message = f"Êtes-vous sûr de vouloir supprimer la vente ?\n\n" \
                 f"📄 Facture: {numero_facture}\n" \
                 f"💰 Montant: {montant_ttc:.2f} DH TTC\n\n" \
                 f"⚠️ Cette action est irréversible !"
        
        dialog = MDDialog(
            title="🗑️ Confirmer la suppression",
            text=message,
            buttons=[
                MDFlatButton(
                    text="❌ Annuler",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDRaisedButton(
                    text="🗑️ Supprimer",
                    on_release=lambda x: self.delete_sale(sale_data, dialog)
                )
            ]
        )
        dialog.open()
    
    def delete_sale(self, sale_data, dialog):
        """Supprimer une vente"""
        dialog.dismiss()
        
        def delete_in_background():
            try:
                if not self.db_manager.connect():
                    Clock.schedule_once(lambda dt: self.show_error("Impossible de se connecter à la base de données"))
                    return
                
                # Supprimer de la table ventes
                success = self.db_manager.execute_update(
                    "DELETE FROM ventes WHERE id = ?",
                    (sale_data['id'],)
                )
                
                if success:
                    numero_facture = sale_data.get('numero_facture', 'N/A')
                    Clock.schedule_once(lambda dt: self.show_success(f"Vente '{numero_facture}' supprimée"))
                    Clock.schedule_once(lambda dt: self.refresh_sales())
                else:
                    Clock.schedule_once(lambda dt: self.show_error("Erreur lors de la suppression"))
                
            except Exception as e:
                Clock.schedule_once(lambda dt: self.show_error(f"Erreur: {str(e)}"))
            finally:
                self.db_manager.close()
        
        threading.Thread(target=delete_in_background, daemon=True).start()
    
    def refresh_sales(self, *args):
        """Actualiser la liste des ventes"""
        self.load_sales()
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"❌ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"❌ {message}")
    
    def show_success(self, message):
        """Afficher un message de succès"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"✅ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"✅ {message}")
    
    def go_back(self, *args):
        """Retourner à l'écran précédent"""
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = 'main'