"""
Écran de gestion des produits - Version corrigée
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import <PERSON><PERSON><PERSON>t<PERSON>ield
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.app import MDApp
from kivy.clock import Clock
import threading
from utils.helpers import format_currency


class ProductCard(MDCard):
    """Carte pour afficher un produit"""
    
    def __init__(self, product_data, on_edit_callback, on_delete_callback, **kwargs):
        super().__init__(**kwargs)
        self.product_data = product_data
        self.elevation = 2
        self.padding = "16dp"
        self.size_hint_y = None
        self.height = "160dp"
        self.spacing = "8dp"
        
        layout = MDBoxLayout(orientation='vertical', spacing="4dp")
        
        # En-tête avec nom et actions
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="32dp")
        
        nom_label = MDLabel(
            text=product_data.get('nom', 'Produit sans nom'),
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        
        # Boutons d'action
        actions_layout = MDBoxLayout(orientation='horizontal', size_hint_x=0.3, spacing="4dp")
        
        edit_btn = MDIconButton(
            icon="pencil",
            theme_icon_color="Primary",
            on_release=lambda x: on_edit_callback(product_data)
        )
        
        delete_btn = MDIconButton(
            icon="delete",
            theme_icon_color="Error",
            on_release=lambda x: on_delete_callback(product_data)
        )
        
        actions_layout.add_widget(edit_btn)
        actions_layout.add_widget(delete_btn)
        
        header_layout.add_widget(nom_label)
        header_layout.add_widget(actions_layout)
        
        # Informations du produit
        info_layout = MDBoxLayout(orientation='vertical', spacing="2dp")
        
        # Référence et code-barres
        ref_label = MDLabel(
            text=f"Réf: {product_data.get('reference', 'N/A')}",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="16dp"
        )
        
        if product_data.get('code_barre'):
            barcode_label = MDLabel(
                text=f"Code-barres: {product_data.get('code_barre')}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="16dp"
            )
            info_layout.add_widget(barcode_label)
        
        # Prix
        prix_vente = product_data.get('prix_vente', 0)
        prix_label = MDLabel(
            text=f"Prix: {format_currency(prix_vente)}",
            font_style="Body1",
            theme_text_color="Primary",
            size_hint_y=None,
            height="20dp"
        )
        
        # Stock
        stock_actuel = product_data.get('stock_actuel', 0)
        stock_minimum = product_data.get('stock_minimum', 0)
        
        if stock_actuel <= stock_minimum and stock_minimum > 0:
            stock_color = (0.8, 0, 0, 1)  # Rouge pour stock bas
            stock_text = f"Stock: {stock_actuel} (⚠️ Stock bas)"
        else:
            stock_color = (0, 0.7, 0, 1)  # Vert pour stock OK
            stock_text = f"Stock: {stock_actuel} unités"
        
        stock_label = MDLabel(
            text=stock_text,
            font_style="Body2",
            theme_text_color="Custom",
            text_color=stock_color,
            size_hint_y=None,
            height="20dp"
        )
        
        # Statut actif/inactif
        statut = "Actif" if product_data.get('actif', True) else "Inactif"
        statut_color = (0, 0.7, 0, 1) if product_data.get('actif', True) else (0.8, 0, 0, 1)
        
        statut_label = MDLabel(
            text=f"Statut: {statut}",
            font_style="Caption",
            theme_text_color="Custom",
            text_color=statut_color,
            size_hint_y=None,
            height="16dp"
        )
        
        info_layout.add_widget(ref_label)
        info_layout.add_widget(prix_label)
        info_layout.add_widget(stock_label)
        info_layout.add_widget(statut_label)
        
        layout.add_widget(header_layout)
        layout.add_widget(info_layout)
        self.add_widget(layout)


class ProductFormDialog(MDDialog):
    """Dialog pour ajouter/modifier un produit - Version simplifiée"""
    
    def __init__(self, product_data=None, on_save_callback=None, **kwargs):
        self.product_data = product_data or {}
        self.on_save_callback = on_save_callback
        
        # Créer le contenu du formulaire
        content_cls = self._create_form_content()
        
        # Boutons
        buttons = [
            MDFlatButton(
                text="ANNULER",
                on_release=self.dismiss
            ),
            MDRaisedButton(
                text="ENREGISTRER",
                on_release=self.save_product
            )
        ]
        
        title = "Modifier le produit" if product_data else "Nouveau produit"
        
        super().__init__(
            title=title,
            type="custom",
            content_cls=content_cls,
            buttons=buttons,
            size_hint=(0.9, None),
            height="600dp",
            **kwargs
        )
        
        # Configuration de la navigation par tabulation
        Clock.schedule_once(self._setup_tab_navigation, 0.1)
    
    def _create_form_content(self):
        """Créer le contenu du formulaire"""
        form_layout = MDBoxLayout(
            orientation='vertical', 
            spacing="16dp", 
            adaptive_height=True,
            padding="20dp"
        )
        
        # Champs principaux
        self.nom_field = MDTextField(
            hint_text="Nom du produit *",
            text=self.product_data.get('nom', ''),
            required=True
        )
        
        self.description_field = MDTextField(
            hint_text="Description",
            text=self.product_data.get('description', ''),
            multiline=True,
            max_height="80dp"
        )
        
        self.reference_field = MDTextField(
            hint_text="Référence *",
            text=self.product_data.get('reference', ''),
            required=True
        )
        
        # Code-barres simplifié
        self.code_barre_field = MDTextField(
            hint_text="Code-barres",
            text=self.product_data.get('code_barre', '')
        )
        
        # Prix
        self.prix_achat_field = MDTextField(
            hint_text="Prix d'achat (DH)",
            text=str(self.product_data.get('prix_achat', 0)),
            input_filter="float"
        )
        
        self.prix_vente_field = MDTextField(
            hint_text="Prix de vente (DH) *",
            text=str(self.product_data.get('prix_vente', 0)),
            input_filter="float",
            required=True
        )
        
        # Stock
        self.stock_actuel_field = MDTextField(
            hint_text="Stock actuel",
            text=str(self.product_data.get('stock_actuel', 0)),
            input_filter="int"
        )
        
        self.stock_minimum_field = MDTextField(
            hint_text="Stock minimum",
            text=str(self.product_data.get('stock_minimum', 0)),
            input_filter="int"
        )
        
        # TVA
        self.tva_field = MDTextField(
            hint_text="TVA (%)",
            text=str(self.product_data.get('tva', 20.0)),
            input_filter="float"
        )
        
        # Checkbox pour actif/inactif
        checkbox_layout = MDBoxLayout(
            orientation='horizontal', 
            size_hint_y=None, 
            height="48dp"
        )
        
        checkbox_label = MDLabel(
            text="Produit actif", 
            size_hint_x=0.7
        )
        
        self.actif_checkbox = MDCheckbox(
            active=bool(self.product_data.get('actif', True)),
            size_hint_x=0.3
        )
        
        checkbox_layout.add_widget(checkbox_label)
        checkbox_layout.add_widget(self.actif_checkbox)
        
        # Ordre des champs pour la navigation par tabulation
        self.fields_order = [
            self.nom_field,
            self.description_field,
            self.reference_field,
            self.code_barre_field,
            self.prix_achat_field,
            self.prix_vente_field,
            self.stock_actuel_field,
            self.stock_minimum_field,
            self.tva_field
        ]
        
        # Ajout des champs au formulaire
        form_layout.add_widget(self.nom_field)
        form_layout.add_widget(self.description_field)
        form_layout.add_widget(self.reference_field)
        form_layout.add_widget(self.code_barre_field)
        form_layout.add_widget(self.prix_achat_field)
        form_layout.add_widget(self.prix_vente_field)
        form_layout.add_widget(self.stock_actuel_field)
        form_layout.add_widget(self.stock_minimum_field)
        form_layout.add_widget(self.tva_field)
        form_layout.add_widget(checkbox_layout)
        
        return form_layout
    
    def _setup_tab_navigation(self, dt):
        """Configure la navigation par tabulation entre les champs"""
        try:
            for i, field in enumerate(self.fields_order):
                if hasattr(field, 'bind'):
                    field.bind(on_text_validate=self._on_tab_pressed)
                    field.tab_index = i
            
            # Focus sur le premier champ
            if self.fields_order:
                self.fields_order[0].focus = True
        except Exception as e:
            print(f"Erreur navigation tabulation: {e}")
    
    def _on_tab_pressed(self, instance):
        """Gérer la navigation par tabulation"""
        try:
            current_index = getattr(instance, 'tab_index', -1)
            if current_index >= 0 and current_index < len(self.fields_order) - 1:
                next_field = self.fields_order[current_index + 1]
                next_field.focus = True
        except Exception as e:
            print(f"Erreur navigation tabulation: {e}")
    
    def save_product(self, *args):
        """Enregistrer le produit"""
        # Validation basique
        if not self.nom_field.text.strip():
            self.nom_field.error = True
            self.nom_field.helper_text = "Le nom est obligatoire"
            return
        
        if not self.reference_field.text.strip():
            self.reference_field.error = True
            self.reference_field.helper_text = "La référence est obligatoire"
            return
        
        try:
            prix_vente = float(self.prix_vente_field.text or 0)
            if prix_vente <= 0:
                self.prix_vente_field.error = True
                self.prix_vente_field.helper_text = "Le prix de vente doit être supérieur à 0"
                return
        except ValueError:
            self.prix_vente_field.error = True
            self.prix_vente_field.helper_text = "Prix de vente invalide"
            return
        
        # Préparer les données
        product_data = {
            'nom': self.nom_field.text.strip(),
            'description': self.description_field.text.strip(),
            'reference': self.reference_field.text.strip(),
            'code_barre': self.code_barre_field.text.strip(),
            'prix_achat': float(self.prix_achat_field.text or 0),
            'prix_vente': float(self.prix_vente_field.text or 0),
            'stock_actuel': int(self.stock_actuel_field.text or 0),
            'stock_minimum': int(self.stock_minimum_field.text or 0),
            'tva': float(self.tva_field.text or 20.0),
            'actif': self.actif_checkbox.active
        }
        
        # Ajouter l'ID si c'est une modification
        if self.product_data.get('id'):
            product_data['id'] = self.product_data['id']
        
        # Appeler le callback
        if self.on_save_callback:
            self.on_save_callback(product_data)
        
        self.dismiss()


class ProductsScreen(MDScreen):
    """Écran de gestion des produits - Version simplifiée"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "products"
        self.products = []
        self.filtered_products = []
        self.build_ui()
    
    def build_ui(self):
        """Construire l'interface utilisateur"""
        layout = MDBoxLayout(orientation='vertical', padding="16dp", spacing="16dp")
        
        # En-tête avec titre et boutons
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="56dp")
        
        title_label = MDLabel(
            text="Gestion des Produits",
            font_style="H5",
            theme_text_color="Primary",
            size_hint_x=0.4
        )
        
        # Boutons d'action
        buttons_layout = MDBoxLayout(orientation='horizontal', size_hint_x=0.6, spacing="8dp")
        
        add_btn = MDRaisedButton(
            text="Nouveau Produit",
            icon="plus",
            on_release=self.add_product,
            size_hint_x=0.3
        )
        
        export_btn = MDRaisedButton(
            text="Exporter",
            icon="download",
            on_release=self.export_products,
            size_hint_x=0.2
        )
        
        low_stock_btn = MDRaisedButton(
            text="Stock Bas",
            icon="alert",
            on_release=self.show_low_stock,
            size_hint_x=0.2
        )
        
        buttons_layout.add_widget(add_btn)
        buttons_layout.add_widget(export_btn)
        buttons_layout.add_widget(low_stock_btn)
        
        header_layout.add_widget(title_label)
        header_layout.add_widget(buttons_layout)
        
        # Barre de recherche
        self.search_field = MDTextField(
            hint_text="Rechercher un produit...",
            icon_right="magnify",
            size_hint_y=None,
            height="56dp"
        )
        self.search_field.bind(text=self.filter_products)
        
        # Zone de contenu avec scroll
        self.scroll_view = MDScrollView()
        self.products_layout = MDGridLayout(
            cols=1,
            spacing="8dp",
            adaptive_height=True,
            padding="8dp"
        )
        
        self.scroll_view.add_widget(self.products_layout)
        
        # Assemblage final
        layout.add_widget(header_layout)
        layout.add_widget(self.search_field)
        layout.add_widget(self.scroll_view)
        
        self.add_widget(layout)
        
        # Charger les produits
        Clock.schedule_once(self.load_products, 0.1)
    
    def load_products(self, dt=None):
        """Charger les produits depuis la base de données"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'db_manager'):
                self.products = app.db_manager.get_all_products()
                self.filtered_products = self.products.copy()
                self.update_products_display()
            else:
                # Données de test si pas de base de données
                self.products = [
                    {
                        'id': 1,
                        'nom': 'Produit Test 1',
                        'reference': 'TEST001',
                        'prix_vente': 100.0,
                        'stock_actuel': 10,
                        'stock_minimum': 5,
                        'actif': True
                    },
                    {
                        'id': 2,
                        'nom': 'Produit Test 2',
                        'reference': 'TEST002',
                        'prix_vente': 250.0,
                        'stock_actuel': 2,
                        'stock_minimum': 5,
                        'actif': True
                    }
                ]
                self.filtered_products = self.products.copy()
                self.update_products_display()
        except Exception as e:
            print(f"Erreur lors du chargement des produits: {e}")
    
    def update_products_display(self):
        """Mettre à jour l'affichage des produits"""
        self.products_layout.clear_widgets()
        
        if not self.filtered_products:
            no_products_label = MDLabel(
                text="Aucun produit trouvé",
                halign="center",
                font_style="H6",
                theme_text_color="Secondary"
            )
            self.products_layout.add_widget(no_products_label)
            return
        
        for product in self.filtered_products:
            card = ProductCard(
                product_data=product,
                on_edit_callback=self.edit_product,
                on_delete_callback=self.delete_product
            )
            self.products_layout.add_widget(card)
    
    def filter_products(self, instance, text):
        """Filtrer les produits selon le texte de recherche"""
        if not text.strip():
            self.filtered_products = self.products.copy()
        else:
            search_text = text.lower()
            self.filtered_products = [
                product for product in self.products
                if (search_text in product.get('nom', '').lower() or
                    search_text in product.get('reference', '').lower() or
                    search_text in product.get('code_barre', '').lower())
            ]
        
        self.update_products_display()
    
    def add_product(self, *args):
        """Ajouter un nouveau produit"""
        dialog = ProductFormDialog(
            on_save_callback=self.save_product
        )
        dialog.open()
    
    def edit_product(self, product_data):
        """Éditer un produit existant"""
        dialog = ProductFormDialog(
            product_data=product_data,
            on_save_callback=self.save_product
        )
        dialog.open()
    
    def save_product(self, product_data):
        """Sauvegarder un produit"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'db_manager'):
                if product_data.get('id'):
                    # Modification
                    app.db_manager.update_product(product_data)
                else:
                    # Nouveau produit
                    app.db_manager.add_product(product_data)
                
                # Recharger les produits
                self.load_products()
            else:
                # Mode test sans base de données
                if product_data.get('id'):
                    # Modification
                    for i, product in enumerate(self.products):
                        if product['id'] == product_data['id']:
                            self.products[i] = product_data
                            break
                else:
                    # Nouveau produit
                    product_data['id'] = len(self.products) + 1
                    self.products.append(product_data)
                
                self.filtered_products = self.products.copy()
                self.update_products_display()
            
            print(f"Produit sauvegardé: {product_data.get('nom')}")
            
        except Exception as e:
            print(f"Erreur lors de la sauvegarde: {e}")
    
    def delete_product(self, product_data):
        """Supprimer un produit"""
        # TODO: Ajouter une confirmation
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'db_manager'):
                app.db_manager.delete_product(product_data['id'])
                self.load_products()
            else:
                # Mode test
                self.products = [p for p in self.products if p['id'] != product_data['id']]
                self.filtered_products = self.products.copy()
                self.update_products_display()
            
            print(f"Produit supprimé: {product_data.get('nom')}")
            
        except Exception as e:
            print(f"Erreur lors de la suppression: {e}")
    
    def export_products(self, *args):
        """Exporter les produits en CSV"""
        try:
            import csv
            from datetime import datetime
            
            filename = f"produits_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['nom', 'reference', 'code_barre', 'prix_achat', 'prix_vente', 
                             'stock_actuel', 'stock_minimum', 'tva', 'actif']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for product in self.products:
                    row = {field: product.get(field, '') for field in fieldnames}
                    writer.writerow(row)
            
            print(f"Export réussi: {filename}")
            
        except Exception as e:
            print(f"Erreur lors de l'export: {e}")
    
    def show_low_stock(self, *args):
        """Afficher les produits avec stock bas"""
        low_stock_products = [
            product for product in self.products
            if (product.get('stock_actuel', 0) <= product.get('stock_minimum', 0) 
                and product.get('stock_minimum', 0) > 0)
        ]
        
        self.filtered_products = low_stock_products
        self.update_products_display()
        
        if not low_stock_products:
            print("Aucun produit avec stock bas")
        else:
            print(f"{len(low_stock_products)} produit(s) avec stock bas")