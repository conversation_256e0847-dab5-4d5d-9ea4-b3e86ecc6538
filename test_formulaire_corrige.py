#!/usr/bin/env python3
"""
Test avec formulaire de catégorie corrigé - champs visibles
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton, MDFlatButton
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.dialog import MDDialog
from kivymd.uix.snackbar import MDSnackbar
from database.db_manager import DatabaseManager


class CategoryFormDialogFixed(MDDialog):
    """Formulaire de catégorie corrigé avec champs visibles"""
    
    def __init__(self, category_data=None, on_save_callback=None, **kwargs):
        self.category_data = category_data or {}
        self.on_save_callback = on_save_callback
        self.db_manager = DatabaseManager()
        
        # Créer les boutons d'abord
        self.cancel_btn = MDFlatButton(
            text="❌ Annuler",
            on_release=self.dismiss_dialog
        )
        
        self.save_btn = MDRaisedButton(
            text="💾 Enregistrer",
            on_release=self.save_category
        )
        
        super().__init__(
            title="✏️ Modifier la catégorie" if category_data else "➕ Nouvelle catégorie",
            type="custom",
            size_hint=(0.9, None),
            height="600dp",
            buttons=[self.cancel_btn, self.save_btn],
            **kwargs
        )
        
        self.create_form()
    
    def create_form(self):
        """Créer le formulaire avec champs garantis visibles"""
        # Container principal
        main_container = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="20dp",
            size_hint_y=None,
            height="480dp"
        )
        
        # Titre du formulaire
        title_label = MDLabel(
            text="📝 Informations de la catégorie",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp",
            halign="left"
        )
        main_container.add_widget(title_label)
        
        # Container pour les champs
        fields_container = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="400dp"
        )
        
        # Champ nom avec label séparé
        nom_container = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="80dp"
        )
        
        nom_label = MDLabel(
            text="📂 Nom de la catégorie *",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        self.nom_field = MDTextField(
            text=self.category_data.get('nom', ''),
            hint_text="Saisissez le nom de la catégorie",
            size_hint_y=None,
            height="56dp",
            mode="rectangle",
            line_color_normal=[0.2, 0.2, 0.2, 1],
            line_color_focus=[0.1, 0.5, 0.8, 1],
            text_color_normal=[0, 0, 0, 1],
            text_color_focus=[0, 0, 0, 1],
            hint_text_color_normal=[0.4, 0.4, 0.4, 1],
            hint_text_color_focus=[0.1, 0.5, 0.8, 1],
            fill_color_normal=[0.95, 0.95, 0.95, 1],
            fill_color_focus=[0.98, 0.98, 0.98, 1],
            required=True,
            max_text_length=100
        )
        
        nom_container.add_widget(nom_label)
        nom_container.add_widget(self.nom_field)
        
        # Champ description avec label séparé
        desc_container = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="120dp"
        )
        
        desc_label = MDLabel(
            text="📝 Description (optionnelle)",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        self.description_field = MDTextField(
            text=self.category_data.get('description', ''),
            hint_text="Description détaillée de la catégorie",
            multiline=True,
            size_hint_y=None,
            height="88dp",
            mode="rectangle",
            line_color_normal=[0.2, 0.2, 0.2, 1],
            line_color_focus=[0.1, 0.5, 0.8, 1],
            text_color_normal=[0, 0, 0, 1],
            text_color_focus=[0, 0, 0, 1],
            hint_text_color_normal=[0.4, 0.4, 0.4, 1],
            hint_text_color_focus=[0.1, 0.5, 0.8, 1],
            fill_color_normal=[0.95, 0.95, 0.95, 1],
            fill_color_focus=[0.98, 0.98, 0.98, 1],
            max_text_length=500
        )
        
        desc_container.add_widget(desc_label)
        desc_container.add_widget(self.description_field)
        
        fields_container.add_widget(nom_container)
        fields_container.add_widget(desc_container)
        
        # Informations supplémentaires si modification
        if self.category_data and self.category_data.get('id'):
            info_container = MDBoxLayout(
                orientation='vertical',
                spacing="8dp",
                size_hint_y=None,
                height="100dp"
            )
            
            # Ligne 1: ID et nombre de produits
            info_row1 = MDBoxLayout(
                orientation='horizontal',
                spacing="16dp",
                size_hint_y=None,
                height="30dp"
            )
            
            id_label = MDLabel(
                text=f"🆔 ID: {self.category_data.get('id', 'N/A')}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_x=0.5
            )
            
            products_count = self.category_data.get('products_count', 0)
            products_label = MDLabel(
                text=f"📦 {products_count} produit(s) lié(s)",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_x=0.5
            )
            
            info_row1.add_widget(id_label)
            info_row1.add_widget(products_label)
            
            # Ligne 2: Date de création
            date_creation = self.category_data.get('date_creation', '')
            if date_creation:
                try:
                    from datetime import datetime
                    if isinstance(date_creation, str):
                        date_obj = datetime.fromisoformat(date_creation.replace('Z', '+00:00'))
                        date_formatted = date_obj.strftime('%d/%m/%Y à %H:%M')
                    else:
                        date_formatted = str(date_creation)[:16]
                except:
                    date_formatted = str(date_creation)[:16]
            else:
                date_formatted = "Non définie"
            
            date_label = MDLabel(
                text=f"📅 Créée le: {date_formatted}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="30dp"
            )
            
            info_container.add_widget(info_row1)
            info_container.add_widget(date_label)
            fields_container.add_widget(info_container)
        
        main_container.add_widget(fields_container)
        
        # IMPORTANT: Assigner le contenu au dialog
        self.content_cls = main_container
    
    def save_category(self, *args):
        """Sauvegarder la catégorie avec validation"""
        nom = self.nom_field.text.strip()
        if not nom:
            self.show_error("Le nom de la catégorie est obligatoire")
            return
        
        if len(nom) < 2:
            self.show_error("Le nom doit contenir au moins 2 caractères")
            return
        
        description = self.description_field.text.strip()
        
        try:
            if not self.db_manager.connect():
                self.show_error("Impossible de se connecter à la base de données")
                return
            
            if self.category_data and self.category_data.get('id'):  # Modification
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ? AND id != ?",
                    (nom, self.category_data['id'])
                )
                
                if existing:
                    self.show_error(f"Une catégorie avec le nom '{nom}' existe déjà")
                    return
                
                success = self.db_manager.execute_update(
                    "UPDATE categories SET nom = ?, description = ? WHERE id = ?",
                    (nom, description, self.category_data['id'])
                )
                
                if success:
                    self.show_success("Catégorie modifiée avec succès")
                    category_data = {
                        'id': self.category_data['id'],
                        'nom': nom,
                        'description': description
                    }
                else:
                    self.show_error("Erreur lors de la modification")
                    return
            
            else:  # Création
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ?",
                    (nom,)
                )
                
                if existing:
                    self.show_error(f"Une catégorie avec le nom '{nom}' existe déjà")
                    return
                
                category_id = self.db_manager.execute_insert(
                    "INSERT INTO categories (nom, description) VALUES (?, ?)",
                    (nom, description)
                )
                
                if category_id:
                    self.show_success("Catégorie créée avec succès")
                    category_data = {
                        'id': category_id,
                        'nom': nom,
                        'description': description
                    }
                else:
                    self.show_error("Erreur lors de la création")
                    return
            
            if self.on_save_callback:
                self.on_save_callback(category_data)
            
            self.dismiss()
            
        except Exception as e:
            self.show_error(f"Erreur lors de la sauvegarde: {str(e)}")
        
        finally:
            self.db_manager.close()
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"❌ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception as e:
            print(f"Erreur Snackbar: {e}")
            print(f"❌ {message}")
    
    def show_success(self, message):
        """Afficher un message de succès"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"✅ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception as e:
            print(f"Erreur Snackbar: {e}")
            print(f"✅ {message}")
    
    def dismiss_dialog(self, *args):
        """Fermer le dialog"""
        self.dismiss()


class TestFormulaireCorrigeApp(MDApp):
    """Application de test pour le formulaire corrigé"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Formulaire Catégorie Corrigé"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construction de l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        # Titre
        title = MDLabel(
            text="🔧 Formulaire Catégorie Corrigé",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Instructions
        instructions = MDLabel(
            text="Ce formulaire corrigé devrait afficher :\n"
                 "✅ Champs nom et description visibles\n"
                 "✅ Boutons Annuler et Enregistrer fonctionnels\n"
                 "✅ Validation et messages d'erreur/succès\n\n"
                 "Testez les deux types de formulaires ci-dessous :",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="140dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="140dp"
        )
        
        # Test nouveau formulaire
        new_btn = MDRaisedButton(
            text="➕ Nouveau Formulaire Corrigé",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_new_form
        )
        
        # Test modification
        edit_btn = MDRaisedButton(
            text="✏️ Modification avec Données",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_edit_form
        )
        
        buttons_layout.add_widget(new_btn)
        buttons_layout.add_widget(edit_btn)
        
        # Résultats
        self.result_label = MDLabel(
            text="Cliquez sur les boutons pour tester le formulaire corrigé.\n"
                 "Vérifiez que tous les champs sont visibles !",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(instructions)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_new_form(self, *args):
        """Tester le nouveau formulaire"""
        self.result_label.text = "🔧 Formulaire nouveau ouvert !\n" \
                                "Vérifiez que les champs nom et description sont visibles."
        
        dialog = CategoryFormDialogFixed(
            on_save_callback=self.on_save_callback
        )
        dialog.open()
        print("✅ Nouveau formulaire corrigé ouvert")
    
    def test_edit_form(self, *args):
        """Tester le formulaire de modification"""
        self.result_label.text = "🔧 Formulaire modification ouvert !\n" \
                                "Avec données pré-remplies et informations supplémentaires."
        
        # Données de test
        test_data = {
            'id': 1,
            'nom': 'Électronique',
            'description': 'Produits électroniques et informatiques',
            'products_count': 5,
            'date_creation': '2024-01-15T10:30:00'
        }
        
        dialog = CategoryFormDialogFixed(
            category_data=test_data,
            on_save_callback=self.on_save_callback
        )
        dialog.open()
        print("✅ Formulaire de modification corrigé ouvert")
    
    def on_save_callback(self, category_data):
        """Callback de sauvegarde"""
        nom = category_data.get('nom', 'Sans nom')
        description = category_data.get('description', 'Aucune')
        
        self.result_label.text = f"🎉 SUCCÈS - Formulaire Fonctionnel !\n" \
                                f"Nom: {nom}\n" \
                                f"Description: {description[:50]}{'...' if len(description) > 50 else ''}\n" \
                                f"Les champs étaient bien visibles !"
        
        print("🎉 SUCCÈS - Catégorie sauvegardée:")
        print(f"  - Nom: {nom}")
        print(f"  - Description: {description}")
        print(f"  - ID: {category_data.get('id', 'Nouveau')}")


def main():
    """Fonction principale"""
    print("🔧 Test - Formulaire Catégorie Corrigé")
    print("=" * 50)
    print("Ce test utilise un formulaire corrigé qui devrait afficher:")
    print("✅ Champs nom et description visibles")
    print("✅ Boutons fonctionnels")
    print("✅ Validation complète")
    print("=" * 50)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestFormulaireCorrigeApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()