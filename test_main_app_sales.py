#!/usr/bin/env python3
"""
Test du formulaire de vente dans l'environnement exact de l'application principale
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

# Configuration pour Windows (comme dans main.py)
if sys.platform == 'win32':
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'

from kivymd.app import MDApp
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel

# Import exact comme dans main.py
from screens.sales_screen import SalesScreen


class TestMainAppSalesApp(MDApp):
    """Test dans l'environnement exact de l'application principale"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Environnement Main App"
        # Configuration exacte comme dans main.py
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construction exacte comme dans main.py"""
        # Screen manager comme dans main.py
        self.screen_manager = MDScreenManager()
        
        # Écran de test
        test_screen = MDScreen(name='test')
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        title = MDLabel(
            text="🧪 Test dans l'Environnement\nExact de l'Application Principale",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Test avec SalesScreen exactement comme dans main.py
        test_sales_screen_btn = MDRaisedButton(
            text="📊 Tester SalesScreen (comme main.py)",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_sales_screen
        )
        
        # Test direct du formulaire
        test_form_btn = MDRaisedButton(
            text="🛒 Tester Formulaire Direct",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_form_direct
        )
        
        self.result_label = MDLabel(
            text="Test dans l'environnement exact de l'application principale",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(test_sales_screen_btn)
        layout.add_widget(test_form_btn)
        layout.add_widget(self.result_label)
        
        test_screen.add_widget(layout)
        
        # Ajouter les écrans comme dans main.py
        self.screen_manager.add_widget(test_screen)
        
        # Ajouter SalesScreen exactement comme dans main.py
        try:
            sales_screen = SalesScreen(name='sales')
            self.screen_manager.add_widget(sales_screen)
            self.result_label.text += "\n✅ SalesScreen ajouté avec succès"
        except Exception as e:
            self.result_label.text += f"\n❌ Erreur SalesScreen: {str(e)}"
            print(f"❌ Erreur SalesScreen: {e}")
            import traceback
            traceback.print_exc()
        
        return self.screen_manager
    
    def test_sales_screen(self, *args):
        """Tester l'écran des ventes exactement comme dans l'app"""
        try:
            # Naviguer vers l'écran des ventes
            self.screen_manager.current = 'sales'
            
            # Obtenir l'écran des ventes
            sales_screen = None
            for screen in self.screen_manager.screens:
                if screen.name == 'sales':
                    sales_screen = screen
                    break
            
            if sales_screen:
                # Appeler add_sale() directement sur l'écran
                sales_screen.add_sale()
                
                self.result_label.text = "✅ SalesScreen.add_sale() appelé !\n\n" \
                                       "Le formulaire devrait s'ouvrir\n" \
                                       "exactement comme dans l'application."
            else:
                self.result_label.text = "❌ SalesScreen non trouvé"
                
        except Exception as e:
            self.result_label.text = f"❌ Erreur test SalesScreen:\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def test_form_direct(self, *args):
        """Tester le formulaire directement"""
        try:
            from forms.sales_form import SalesFormDialog
            
            dialog = SalesFormDialog(
                on_save_callback=self.on_save_test
            )
            dialog.open()
            
            self.result_label.text = "✅ Formulaire direct ouvert !\n\n" \
                                   "Comparaison avec SalesScreen.add_sale()"
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur formulaire direct:\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def on_save_test(self, data):
        """Callback de test"""
        self.result_label.text = "🎉 Callback de sauvegarde appelé !\n\n" \
                               "Le formulaire fonctionne parfaitement."


def main():
    """Fonction principale"""
    print("🧪 Test Formulaire de Vente - Environnement Main App")
    print("=" * 60)
    print("OBJECTIF : Reproduire exactement l'environnement de main.py")
    print()
    print("CONFIGURATION :")
    print("• Theme: Light, Blue (comme main.py)")
    print("• Screen Manager (comme main.py)")
    print("• SalesScreen ajouté (comme main.py)")
    print("• Appel direct de add_sale() sur SalesScreen")
    print()
    print("COMPARAISON :")
    print("• Test 1: SalesScreen.add_sale() (comme dans l'app)")
    print("• Test 2: Formulaire direct (pour comparaison)")
    print("=" * 60)
    
    try:
        app = TestMainAppSalesApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()