#!/usr/bin/env python3
"""
Test du formulaire produit avec la nouvelle liste déroulante
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from database.db_manager import DatabaseManager
from screens.products_screen import ProductFormDialog

class TestFormApp(MDApp):
    def build(self):
        self.title = "Test Formulaire Produit - Liste Catégories"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Initialiser la base de données
        self.db_manager = DatabaseManager()
        if not self.db_manager.connect():
            print("❌ Impossible de se connecter à la base de données")
            return
        
        if not self.db_manager.initialize_database():
            print("❌ Impossible d'initialiser la base de données")
            return
        
        print("✅ Base de données initialisée")
        
        screen = MDScreen()
        layout = MDBoxLayout(
            orientation='vertical', 
            padding="20dp", 
            spacing="20dp"
        )
        
        # Titre
        title = MDLabel(
            text="🧪 Test de la Nouvelle Liste Déroulante",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        subtitle = MDLabel(
            text="Testez le formulaire produit avec le nouveau bouton de sélection de catégorie",
            font_style="Body1",
            halign="center",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="40dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="16dp",
            size_hint_y=None,
            height="200dp"
        )
        
        # Test nouveau produit
        new_product_btn = MDRaisedButton(
            text="➕ Nouveau Produit (Test Liste)",
            size_hint_y=None,
            height="48dp",
            on_release=self.test_new_product
        )
        
        # Test modification produit
        edit_product_btn = MDRaisedButton(
            text="✏️ Modifier Produit Existant",
            size_hint_y=None,
            height="48dp",
            on_release=self.test_edit_product
        )
        
        # Test sans catégorie
        no_category_btn = MDRaisedButton(
            text="📭 Test Sans Catégorie",
            size_hint_y=None,
            height="48dp",
            on_release=self.test_no_category
        )
        
        # Instructions
        instructions = MDLabel(
            text="""📋 Instructions de test :
            
1. Cliquez sur 'Nouveau Produit' pour tester la sélection de catégorie
2. Dans le formulaire, cherchez le bouton 'Catégorie' (coloré)
3. Cliquez sur le bouton pour ouvrir la liste déroulante
4. Sélectionnez une catégorie et observez le changement de couleur
5. Remplissez les autres champs et enregistrez
6. Testez aussi 'Aucune catégorie' pour voir l'affichage gris

🎨 États du bouton :
• 🔄 Bleu : Chargement
• 📭 Gris : Aucune catégorie
• 📂 Vert : Catégorie sélectionnée
• ❌ Rouge : Erreur""",
            font_style="Caption",
            theme_text_color="Secondary",
            text_size=(None, None)
        )
        
        buttons_layout.add_widget(new_product_btn)
        buttons_layout.add_widget(edit_product_btn)
        buttons_layout.add_widget(no_category_btn)
        
        layout.add_widget(title)
        layout.add_widget(subtitle)
        layout.add_widget(buttons_layout)
        layout.add_widget(instructions)
        
        screen.add_widget(layout)
        return screen
    
    def test_new_product(self, *args):
        """Tester la création d'un nouveau produit"""
        print("🧪 Test : Nouveau produit avec liste déroulante")
        
        dialog = ProductFormDialog(
            product_data={},
            on_save_callback=self.on_product_saved
        )
        dialog.open()
    
    def test_edit_product(self, *args):
        """Tester la modification d'un produit existant"""
        print("🧪 Test : Modification produit existant")
        
        # Récupérer un produit existant
        products = self.db_manager.execute_query("""
            SELECT p.*, c.nom as categorie_nom
            FROM produits p
            LEFT JOIN categories c ON p.categorie_id = c.id
            WHERE p.actif = 1
            LIMIT 1
        """)
        
        if products:
            product = products[0]
            print(f"📦 Produit sélectionné : {product['nom']}")
            if product.get('categorie_nom'):
                print(f"📂 Catégorie actuelle : {product['categorie_nom']}")
            else:
                print("📭 Aucune catégorie actuellement")
            
            dialog = ProductFormDialog(
                product_data=product,
                on_save_callback=self.on_product_saved
            )
            dialog.open()
        else:
            print("❌ Aucun produit trouvé pour le test")
    
    def test_no_category(self, *args):
        """Tester avec un produit sans catégorie"""
        print("🧪 Test : Produit sans catégorie")
        
        dialog = ProductFormDialog(
            product_data={
                'nom': 'Produit Test Sans Catégorie',
                'reference': 'TEST-NO-CAT',
                'prix_vente': 100.0,
                'categorie_id': None  # Pas de catégorie
            },
            on_save_callback=self.on_product_saved
        )
        dialog.open()
    
    def on_product_saved(self, product_data):
        """Callback quand un produit est sauvegardé"""
        print("✅ Produit sauvegardé avec succès !")
        print(f"📦 Nom : {product_data.get('nom')}")
        print(f"📂 Catégorie ID : {product_data.get('categorie_id', 'NULL')}")
        
        # Vérifier la liaison en base
        if product_data.get('id'):
            result = self.db_manager.execute_query("""
                SELECT p.nom, c.nom as categorie_nom
                FROM produits p
                LEFT JOIN categories c ON p.categorie_id = c.id
                WHERE p.id = ?
            """, (product_data['id'],))
            
            if result:
                product = result[0]
                cat_name = product.get('categorie_nom', 'Aucune')
                print(f"🔗 Liaison vérifiée : {product['nom']} → {cat_name}")

if __name__ == "__main__":
    print("🧪 LANCEMENT DU TEST FORMULAIRE AVEC LISTE DÉROULANTE")
    print("=" * 60)
    print("🎯 Ce test permet de vérifier la nouvelle implémentation")
    print("📋 Bouton de sélection de catégorie au lieu du champ texte")
    print("🎨 États visuels colorés selon la sélection")
    print("=" * 60)
    
    try:
        app = TestFormApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()
        input("Appuyez sur Entrée pour quitter...")