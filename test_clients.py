#!/usr/bin/env python3
"""
Test de la gestion des clients pour GesComPro_LibTam
"""

import os
import sys
import tempfile
from datetime import datetime

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def test_clients_management():
    """Test complet de la gestion des clients"""
    print("🧪 Test de la gestion des clients")
    print("=" * 50)
    
    # Créer une base de données temporaire
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        test_db_path = tmp_file.name
    
    try:
        # Initialiser la base de données
        db_manager = DatabaseManager(test_db_path)
        if not db_manager.connect():
            print("❌ Erreur de connexion à la base de données")
            return False
        
        db_manager.initialize_database()
        print("✅ Base de données initialisée")
        
        # Test 1: Ajouter des clients
        print("\n🔸 Test 1: Ajout de clients")
        
        clients_test = [
            {
                'nom': 'Dupont',
                'prenom': 'Jean',
                'entreprise': 'SA<PERSON> Dupont',
                'email': '<EMAIL>',
                'telephone': '0123456789',
                'adresse': '123 Rue de la Paix',
                'ville': 'Paris',
                'code_postal': '75001',
                'pays': 'France'
            },
            {
                'nom': 'Martin',
                'prenom': 'Marie',
                'entreprise': '',
                'email': '<EMAIL>',
                'telephone': '0987654321',
                'adresse': '456 Avenue des Champs',
                'ville': 'Lyon',
                'code_postal': '69000',
                'pays': 'France'
            },
            {
                'nom': 'TechCorp',
                'prenom': '',
                'entreprise': 'TechCorp Solutions',
                'email': '<EMAIL>',
                'telephone': '0555123456',
                'adresse': '789 Boulevard Tech',
                'ville': 'Marseille',
                'code_postal': '13000',
                'pays': 'France'
            }
        ]
        
        client_ids = []
        for i, client in enumerate(clients_test):
            query = """
                INSERT INTO clients 
                (nom, prenom, entreprise, email, telephone, adresse, ville, code_postal, pays)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                client['nom'], client['prenom'], client['entreprise'],
                client['email'], client['telephone'], client['adresse'],
                client['ville'], client['code_postal'], client['pays']
            )
            
            client_id = db_manager.execute_insert(query, params)
            if client_id:
                client_ids.append(client_id)
                print(f"  ✅ Client {i+1} ajouté (ID: {client_id})")
            else:
                print(f"  ❌ Erreur lors de l'ajout du client {i+1}")
                return False
        
        # Test 2: Lister les clients
        print("\n🔸 Test 2: Liste des clients")
        
        clients = db_manager.execute_query("""
            SELECT * FROM clients WHERE actif = 1 ORDER BY nom, prenom
        """)
        
        if clients and len(clients) == 3:
            print(f"  ✅ {len(clients)} clients trouvés")
            for client in clients:
                nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
                if not nom_complet:
                    nom_complet = client.get('entreprise', 'Sans nom')
                print(f"    - {nom_complet} ({client.get('email', 'Pas d\'email')})")
        else:
            print(f"  ❌ Nombre de clients incorrect: {len(clients) if clients else 0}")
            return False
        
        # Test 3: Modifier un client
        print("\n🔸 Test 3: Modification d'un client")
        
        if client_ids:
            client_id = client_ids[0]
            update_query = """
                UPDATE clients SET 
                telephone = ?, ville = ?
                WHERE id = ?
            """
            success = db_manager.execute_update(update_query, ('0111222333', 'Toulouse', client_id))
            
            if success:
                # Vérifier la modification
                updated_client = db_manager.execute_query(
                    "SELECT * FROM clients WHERE id = ?", (client_id,)
                )
                if updated_client and updated_client[0]['telephone'] == '0111222333':
                    print(f"  ✅ Client {client_id} modifié avec succès")
                else:
                    print(f"  ❌ Modification non appliquée")
                    return False
            else:
                print(f"  ❌ Erreur lors de la modification")
                return False
        
        # Test 4: Recherche de clients
        print("\n🔸 Test 4: Recherche de clients")
        
        # Recherche par nom
        search_results = db_manager.execute_query("""
            SELECT * FROM clients 
            WHERE actif = 1 AND (nom LIKE ? OR prenom LIKE ? OR entreprise LIKE ?)
        """, ('%Dupont%', '%Dupont%', '%Dupont%'))
        
        if search_results and len(search_results) == 1:
            print("  ✅ Recherche par nom fonctionnelle")
        else:
            print(f"  ❌ Recherche incorrecte: {len(search_results) if search_results else 0} résultats")
            return False
        
        # Test 5: Désactivation d'un client
        print("\n🔸 Test 5: Désactivation d'un client")
        
        if client_ids and len(client_ids) > 1:
            client_id = client_ids[1]
            success = db_manager.execute_update(
                "UPDATE clients SET actif = 0 WHERE id = ?", (client_id,)
            )
            
            if success:
                # Vérifier que le client n'apparaît plus dans la liste active
                active_clients = db_manager.execute_query("""
                    SELECT * FROM clients WHERE actif = 1
                """)
                
                if len(active_clients) == 2:  # Il devrait rester 2 clients actifs
                    print(f"  ✅ Client {client_id} désactivé avec succès")
                else:
                    print(f"  ❌ Désactivation incorrecte: {len(active_clients)} clients actifs")
                    return False
            else:
                print(f"  ❌ Erreur lors de la désactivation")
                return False
        
        # Test 6: Validation des contraintes
        print("\n🔸 Test 6: Validation des contraintes")
        
        # Tenter d'ajouter un client avec un email déjà existant
        try:
            duplicate_query = """
                INSERT INTO clients (nom, email) VALUES (?, ?)
            """
            success = db_manager.execute_update(duplicate_query, ('Test', '<EMAIL>'))
            
            if not success:
                print("  ✅ Contrainte d'unicité de l'email respectée")
            else:
                print("  ❌ Contrainte d'unicité non respectée")
                return False
        except Exception as e:
            print("  ✅ Contrainte d'unicité de l'email respectée (exception)")
        
        print("\n" + "=" * 50)
        print("🎉 TOUS LES TESTS DE GESTION DES CLIENTS RÉUSSIS!")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur lors des tests: {e}")
        return False
    
    finally:
        # Nettoyer
        if db_manager:
            db_manager.disconnect()
        try:
            os.unlink(test_db_path)
            print("🧹 Fichier de test supprimé")
        except:
            pass

def test_client_screen_integration():
    """Test d'intégration avec l'écran des clients"""
    print("\n🧪 Test d'intégration de l'écran clients")
    print("=" * 50)
    
    try:
        # Tester l'import des modules
        from screens.clients_screen import ClientsScreen, ClientFormDialog, ClientCard
        print("✅ Import de l'écran clients réussi")
        
        # Tester les données de test
        test_client_data = {
            'id': 1,
            'nom': 'Test',
            'prenom': 'Client',
            'entreprise': 'Test Corp',
            'email': '<EMAIL>',
            'telephone': '0123456789',
            'adresse': '123 Test Street',
            'ville': 'Test City',
            'code_postal': '12345',
            'pays': 'France'
        }
        print("✅ Données de test préparées")
        
        # Vérifier que les classes sont bien définies
        assert ClientsScreen is not None
        assert ClientFormDialog is not None
        assert ClientCard is not None
        print("✅ Classes d'interface définies")
        
        # Tester la logique de formatage des noms
        nom_complet = f"{test_client_data.get('prenom', '')} {test_client_data.get('nom', '')}".strip()
        assert nom_complet == "Client Test"
        print("✅ Logique de formatage des noms fonctionnelle")
        
        print("\n🎉 TESTS D'INTÉGRATION RÉUSSIS!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors des tests d'intégration: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Tests de la gestion des clients - GesComPro_LibTam")
    print("=" * 60)
    
    # Test de la base de données
    db_success = test_clients_management()
    
    # Test d'intégration
    ui_success = test_client_screen_integration()
    
    print("\n" + "=" * 60)
    print("📊 RÉSULTATS FINAUX")
    print("=" * 60)
    print(f"Gestion base de données  {'✅ RÉUSSI' if db_success else '❌ ÉCHOUÉ'}")
    print(f"Intégration interface    {'✅ RÉUSSI' if ui_success else '❌ ÉCHOUÉ'}")
    
    if db_success and ui_success:
        print("\n🎉 LA GESTION DES CLIENTS EST ENTIÈREMENT FONCTIONNELLE!")
        print("🚀 Vous pouvez utiliser l'écran clients dans l'application!")
    else:
        print("\n⚠️ CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    print("\nAppuyez sur Entrée pour continuer...")
    input()