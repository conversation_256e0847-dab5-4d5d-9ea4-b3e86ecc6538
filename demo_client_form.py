#!/usr/bin/env python3
"""
Démonstration visuelle du formulaire client avec disposition en deux colonnes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from screens.clients_screen import ClientFormDialog

class DemoClientFormApp(MDApp):
    def build(self):
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        screen = MDScreen()
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="40dp",
            pos_hint={'center_x': 0.5, 'center_y': 0.5},
            size_hint=(None, None),
            size=("400dp", "300dp")
        )
        
        # Titre
        title = MDLabel(
            text="🧪 Démonstration Formulaire Client",
            font_style="H5",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="48dp"
        )
        
        # Description
        desc = MDLabel(
            text="Testez la nouvelle disposition en deux colonnes\navec sections organisées et ScrollView",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="64dp"
        )
        
        # Bouton pour nouveau client
        btn_nouveau = MDRaisedButton(
            text="➕ Nouveau Client",
            size_hint_y=None,
            height="48dp",
            on_release=self.show_new_client_dialog
        )
        
        # Bouton pour modifier client (avec données test)
        btn_modifier = MDRaisedButton(
            text="✏️ Modifier Client Test",
            size_hint_y=None,
            height="48dp",
            on_release=self.show_edit_client_dialog
        )
        
        layout.add_widget(title)
        layout.add_widget(desc)
        layout.add_widget(btn_nouveau)
        layout.add_widget(btn_modifier)
        screen.add_widget(layout)
        
        return screen
    
    def show_new_client_dialog(self, *args):
        """Afficher le dialog pour un nouveau client"""
        print("🆕 Ouverture du formulaire nouveau client...")
        dialog = ClientFormDialog(
            client_data=None,
            on_save_callback=self.on_client_saved
        )
        dialog.open()
    
    def show_edit_client_dialog(self, *args):
        """Afficher le dialog pour modifier un client avec des données test"""
        print("✏️ Ouverture du formulaire modification client...")
        test_data = {
            'nom': 'Dupont',
            'prenom': 'Jean-Pierre',
            'entreprise': 'SARL Dupont & Associés',
            'email': '<EMAIL>',
            'telephone': '01 23 45 67 89',
            'adresse': '123 Avenue des Champs-Élysées\nBâtiment A, Étage 4\nAppartement 42',
            'ville': 'Paris',
            'code_postal': '75008',
            'pays': 'France'
        }
        
        dialog = ClientFormDialog(
            client_data=test_data,
            on_save_callback=self.on_client_saved
        )
        dialog.open()
    
    def on_client_saved(self, client_data):
        """Callback appelé quand un client est sauvegardé"""
        print("\n✅ Client sauvegardé avec succès!")
        print("📋 Données reçues:")
        for key, value in client_data.items():
            if value:  # Afficher seulement les champs non vides
                print(f"   • {key.replace('_', ' ').title()}: {value}")
        print()

if __name__ == "__main__":
    print("🚀 Démonstration du formulaire client - GesComPro_LibTam")
    print("=" * 60)
    print("🎯 Nouvelles fonctionnalités:")
    print("   • 📋 Sections organisées avec icônes")
    print("   • 🔄 Disposition en deux colonnes optimisée")
    print("   • 📜 ScrollView pour navigation fluide")
    print("   • 📱 Interface responsive")
    print("=" * 60)
    
    try:
        app = DemoClientFormApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()