"""
Script de lancement optimisé pour GesComPro_LibTam avec base de données haute performance
"""

import sys
import os
import time
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

def check_dependencies():
    """Vérifier les dépendances essentielles"""
    missing_deps = []
    
    try:
        import kivy
        print(f"✅ Kivy {kivy.__version__}")
    except ImportError:
        missing_deps.append("kivy")
    
    try:
        import kivymd
        print(f"✅ KivyMD")
    except ImportError:
        missing_deps.append("kivymd")
    
    try:
        import matplotlib
        print(f"✅ Matplotlib {matplotlib.__version__}")
    except ImportError:
        missing_deps.append("matplotlib")
    
    try:
        import reportlab
        print(f"✅ ReportLab")
    except ImportError:
        missing_deps.append("reportlab")
    
    if missing_deps:
        print(f"\n❌ Dépendances manquantes: {', '.join(missing_deps)}")
        print("Installez-les avec: pip install " + " ".join(missing_deps))
        return False
    
    return True


def optimize_database():
    """Optimiser la base de données au démarrage"""
    print("\n⚡ Optimisation de la base de données...")
    
    try:
        from database.db_manager import DatabaseManager
        
        start_time = time.time()
        db = DatabaseManager()
        
        if db.connect():
            # Initialiser si nécessaire
            db.initialize_database()
            
            # Vérifier si l'optimisation est nécessaire
            cursor = db.connection.cursor()
            
            # Vérifier le mode journal
            journal_mode = cursor.execute("PRAGMA journal_mode").fetchone()
            if journal_mode and journal_mode[0] != 'wal':
                print("   🔧 Application des optimisations SQLite...")
                # Les optimisations sont déjà appliquées dans _apply_sqlite_optimizations
            
            # Créer les index manquants rapidement
            critical_indexes = [
                "CREATE INDEX IF NOT EXISTS idx_produits_actif_categorie ON produits(actif, categorie_id)",
                "CREATE INDEX IF NOT EXISTS idx_ventes_date_client ON ventes(date_vente, client_id)",
                "CREATE INDEX IF NOT EXISTS idx_clients_actif ON clients(actif)"
            ]
            
            for index_sql in critical_indexes:
                try:
                    cursor.execute(index_sql)
                except:
                    pass  # Index peut déjà exister
            
            db.disconnect()
            
            optimization_time = time.time() - start_time
            print(f"   ✅ Base de données optimisée en {optimization_time:.3f}s")
            return True
            
        else:
            print("   ❌ Impossible de se connecter à la base de données")
            return False
            
    except Exception as e:
        print(f"   ⚠️ Erreur lors de l'optimisation: {e}")
        return False


def preload_critical_modules():
    """Précharger les modules critiques pour de meilleures performances"""
    print("\n📦 Préchargement des modules critiques...")
    
    start_time = time.time()
    
    try:
        # Précharger les modules de base de données
        from database.db_manager import DatabaseManager
        
        # Précharger les écrans principaux
        from screens.dashboard_screen import DashboardScreen
        from screens.categories_screen import CategoriesScreen
        from screens.products_screen import ProductsScreen
        
        preload_time = time.time() - start_time
        print(f"   ✅ Modules préchargés en {preload_time:.3f}s")
        return True
        
    except Exception as e:
        print(f"   ⚠️ Erreur lors du préchargement: {e}")
        return False


def main():
    """Fonction principale optimisée"""
    print("🚀 LANCEMENT OPTIMISÉ DE GesComPro_LibTam")
    print("=" * 50)
    
    total_start_time = time.time()
    
    # 1. Vérifier les dépendances
    if not check_dependencies():
        input("\nAppuyez sur Entrée pour quitter...")
        return
    
    # 2. Optimiser la base de données
    if not optimize_database():
        print("⚠️ Avertissement: Optimisation de la base de données échouée")
    
    # 3. Précharger les modules critiques
    if not preload_critical_modules():
        print("⚠️ Avertissement: Préchargement des modules échoué")
    
    # 4. Démarrer l'application
    print("\n📱 Démarrage de l'application optimisée...")
    
    try:
        app_start_time = time.time()
        
        # Importer et lancer l'application
        from main import GesComApp
        
        app = GesComApp()
        
        app_init_time = time.time() - app_start_time
        total_time = time.time() - total_start_time
        
        print(f"   ✅ Application initialisée en {app_init_time:.3f}s")
        print(f"   🎯 Temps total de démarrage: {total_time:.3f}s")
        print("\n🎉 Application prête ! Performances optimisées activées.")
        
        # Lancer l'application
        app.run()
        
    except Exception as e:
        print(f"\n❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()
        input("\nAppuyez sur Entrée pour quitter...")


if __name__ == "__main__":
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    main()
