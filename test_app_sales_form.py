#!/usr/bin/env python3
"""
Test du formulaire de vente exactement comme dans l'application
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel

# Import exactement comme dans l'application
from forms.sales_form import SalesFormDialog


class TestAppSalesFormApp(MDApp):
    """Test du formulaire de vente comme dans l'application"""
    
    def build(self):
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        title = MDLabel(
            text="🧪 Test Formulaire\nComme dans l'Application",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Test exactement comme add_sale() dans sales_screen.py
        add_sale_btn = MDRaisedButton(
            text="🛒 add_sale() - Comme dans l'App",
            size_hint_y=None,
            height="60dp",
            on_release=self.add_sale
        )
        
        # Test exactement comme edit_sale() dans sales_screen.py
        edit_sale_btn = MDRaisedButton(
            text="✏️ edit_sale() - Comme dans l'App",
            size_hint_y=None,
            height="60dp",
            on_release=self.edit_sale
        )
        
        self.result_label = MDLabel(
            text="Test du formulaire exactement comme dans l'application",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(add_sale_btn)
        layout.add_widget(edit_sale_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def add_sale(self, *args):
        """Reproduire exactement la méthode add_sale() de sales_screen.py"""
        try:
            print("🛒 Début add_sale() - comme dans l'application")
            
            # Code exact de sales_screen.py
            dialog = SalesFormDialog(
                on_save_callback=self.on_sale_saved
            )
            dialog.open()
            print("🛒 Nouveau formulaire de vente avec listes déroulantes ouvert")
            
            self.result_label.text = "✅ add_sale() exécuté avec succès !\n\n" \
                                   "Le formulaire devrait s'afficher\n" \
                                   "exactement comme dans l'application."
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur dans add_sale() :\n{str(e)}"
            print(f"❌ Erreur add_sale(): {e}")
            import traceback
            traceback.print_exc()
    
    def edit_sale(self, *args):
        """Reproduire exactement la méthode edit_sale() de sales_screen.py"""
        try:
            print("✏️ Début edit_sale() - comme dans l'application")
            
            # Données de test comme dans l'application
            sale_data = {
                'id': 1,
                'numero_facture': 'FAC-TEST-001',
                'client_id': 1,
                'montant_ht': 100.00,
                'montant_ttc': 120.00,
                'mode_paiement': 'Espèces',
                'notes': 'Test depuis l\'application',
                'date_vente': '2024-01-01 10:00:00'
            }
            
            # Code exact de sales_screen.py
            dialog = SalesFormDialog(
                sale_data=sale_data,
                on_save_callback=self.on_sale_saved
            )
            dialog.open()
            print(f"✏️ Modification de la vente {sale_data.get('numero_facture', 'N/A')}")
            
            self.result_label.text = "✅ edit_sale() exécuté avec succès !\n\n" \
                                   "Le formulaire de modification devrait\n" \
                                   "s'afficher avec les données pré-remplies."
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur dans edit_sale() :\n{str(e)}"
            print(f"❌ Erreur edit_sale(): {e}")
            import traceback
            traceback.print_exc()
    
    def on_sale_saved(self, sale_data):
        """Reproduire exactement on_sale_saved() de sales_screen.py"""
        try:
            numero_facture = sale_data.get('numero_facture', 'N/A')
            print(f"💾 Vente sauvegardée: {numero_facture}")
            
            self.result_label.text = f"🎉 Vente sauvegardée avec succès !\n\n" \
                                   f"Numéro: {numero_facture}\n" \
                                   f"Le callback fonctionne parfaitement !"
            
            # Dans l'app réelle, on ferait: self.load_sales()
            print("📊 Dans l'app réelle: self.load_sales() serait appelé")
            
        except Exception as e:
            print(f"❌ Erreur callback: {e}")
            self.result_label.text = f"❌ Erreur callback: {str(e)}"


def main():
    """Fonction principale"""
    print("🧪 Test Formulaire de Vente - Reproduction Exacte de l'Application")
    print("=" * 70)
    print("OBJECTIF : Reproduire exactement les appels de sales_screen.py")
    print()
    print("MÉTHODES TESTÉES :")
    print("🛒 add_sale() - Création d'une nouvelle vente")
    print("✏️ edit_sale() - Modification d'une vente existante")
    print("💾 on_sale_saved() - Callback de sauvegarde")
    print()
    print("IMPORT UTILISÉ :")
    print("from forms.sales_form import SalesFormDialog")
    print("=" * 70)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestAppSalesFormApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()