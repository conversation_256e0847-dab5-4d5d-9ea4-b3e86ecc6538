#!/usr/bin/env python3
"""
Test final du formulaire produit - Démonstration complète
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton, MDFlatButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.gridlayout import MDGridLayout
from utils.helpers import format_currency

class TestFinalApp(MDApp):
    def build(self):
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Données de test
        self.products = []
        
        screen = MDScreen()
        layout = MDBoxLayout(orientation='vertical', padding="16dp", spacing="16dp")
        
        # En-tête
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="60dp")
        
        title = MDLabel(
            text="🛍️ Test Final - Formulaire Produit",
            font_style="H5",
            theme_text_color="Primary",
            size_hint_x=0.6
        )
        
        # Boutons
        buttons_layout = MDBoxLayout(orientation='horizontal', size_hint_x=0.4, spacing="8dp")
        
        add_btn = MDRaisedButton(
            text="Nouveau Produit",
            icon="plus",
            on_release=self.add_product,
            size_hint_x=0.5
        )
        
        test_btn = MDRaisedButton(
            text="Produit Test",
            icon="test-tube",
            on_release=self.add_test_product,
            size_hint_x=0.5
        )
        
        buttons_layout.add_widget(add_btn)
        buttons_layout.add_widget(test_btn)
        
        header_layout.add_widget(title)
        header_layout.add_widget(buttons_layout)
        
        # Zone de statut
        self.status_label = MDLabel(
            text="✅ Formulaire produit prêt - Cliquez sur 'Nouveau Produit'",
            font_style="Body1",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp",
            halign="center"
        )
        
        # Liste des produits
        self.scroll_view = MDScrollView()
        self.products_layout = MDGridLayout(
            cols=1,
            spacing="8dp",
            adaptive_height=True,
            padding="8dp"
        )
        self.scroll_view.add_widget(self.products_layout)
        
        # Assemblage
        layout.add_widget(header_layout)
        layout.add_widget(self.status_label)
        layout.add_widget(self.scroll_view)
        
        screen.add_widget(layout)
        return screen
    
    def add_product(self, *args):
        """Ouvrir le formulaire pour un nouveau produit"""
        try:
            from screens.products_screen import ProductFormDialog
            
            dialog = ProductFormDialog(
                on_save_callback=self.on_product_saved
            )
            dialog.open()
            
            self.status_label.text = "📝 Formulaire ouvert - Remplissez les champs"
            
        except Exception as e:
            self.status_label.text = f"❌ Erreur: {str(e)}"
            print(f"Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def add_test_product(self, *args):
        """Ajouter un produit de test pré-rempli"""
        try:
            from screens.products_screen import ProductFormDialog
            
            test_data = {
                'nom': 'Smartphone Galaxy Pro',
                'description': 'Smartphone haut de gamme avec écran OLED',
                'reference': 'SMART-001',
                'code_barre': '1234567890123',
                'prix_achat': 800.0,
                'prix_vente': 1200.0,
                'stock_actuel': 15,
                'stock_minimum': 5,
                'tva': 20.0,
                'actif': True
            }
            
            dialog = ProductFormDialog(
                product_data=test_data,
                on_save_callback=self.on_product_saved
            )
            dialog.open()
            
            self.status_label.text = "📝 Formulaire test ouvert - Données pré-remplies"
            
        except Exception as e:
            self.status_label.text = f"❌ Erreur: {str(e)}"
            print(f"Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def on_product_saved(self, product_data):
        """Callback quand un produit est sauvegardé"""
        try:
            # Ajouter un ID si pas présent
            if not product_data.get('id'):
                product_data['id'] = len(self.products) + 1
            
            # Ajouter à la liste
            self.products.append(product_data)
            
            # Mettre à jour l'affichage
            self.update_products_display()
            
            # Mettre à jour le statut
            nom = product_data.get('nom', 'Sans nom')
            prix = format_currency(product_data.get('prix_vente', 0))
            self.status_label.text = f"✅ Produit sauvegardé: {nom} - {prix}"
            
            print(f"✅ Produit sauvegardé: {product_data}")
            
        except Exception as e:
            self.status_label.text = f"❌ Erreur sauvegarde: {str(e)}"
            print(f"Erreur sauvegarde: {e}")
    
    def update_products_display(self):
        """Mettre à jour l'affichage des produits"""
        self.products_layout.clear_widgets()
        
        if not self.products:
            no_products = MDLabel(
                text="Aucun produit ajouté",
                halign="center",
                font_style="Body1",
                theme_text_color="Secondary"
            )
            self.products_layout.add_widget(no_products)
            return
        
        for product in self.products:
            card = self.create_product_card(product)
            self.products_layout.add_widget(card)
    
    def create_product_card(self, product):
        """Créer une carte produit"""
        card = MDCard(
            elevation=2,
            padding="16dp",
            size_hint_y=None,
            height="120dp",
            spacing="8dp"
        )
        
        layout = MDBoxLayout(orientation='vertical', spacing="4dp")
        
        # Nom et prix
        header = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="32dp")
        
        nom_label = MDLabel(
            text=product.get('nom', 'Sans nom'),
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        
        prix_label = MDLabel(
            text=format_currency(product.get('prix_vente', 0)),
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_x=0.3,
            halign="right"
        )
        
        header.add_widget(nom_label)
        header.add_widget(prix_label)
        
        # Informations
        ref_label = MDLabel(
            text=f"Réf: {product.get('reference', 'N/A')}",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="16dp"
        )
        
        stock_label = MDLabel(
            text=f"Stock: {product.get('stock_actuel', 0)} unités",
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="20dp"
        )
        
        # Statut
        statut = "✅ Actif" if product.get('actif', True) else "❌ Inactif"
        statut_label = MDLabel(
            text=statut,
            font_style="Caption",
            theme_text_color="Primary" if product.get('actif', True) else "Error",
            size_hint_y=None,
            height="16dp"
        )
        
        layout.add_widget(header)
        layout.add_widget(ref_label)
        layout.add_widget(stock_label)
        layout.add_widget(statut_label)
        
        card.add_widget(layout)
        return card

if __name__ == "__main__":
    print("🚀 TEST FINAL - FORMULAIRE PRODUIT")
    print("=" * 50)
    print("✅ Fonctionnalités testées:")
    print("   - Ouverture du formulaire")
    print("   - Saisie des données")
    print("   - Navigation par tabulation")
    print("   - Validation des champs")
    print("   - Sauvegarde des produits")
    print("   - Affichage en Dirham (DH)")
    print("   - Interface moderne")
    print("=" * 50)
    
    try:
        app = TestFinalApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()