#!/usr/bin/env python3
"""
Diagnostic des performances de l'application GesComPro_LibTam
"""

import os
import sys
import time
import psutil
import threading
from pathlib import Path

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_startup_performance():
    """Analyser les performances de démarrage"""
    print("🔍 DIAGNOSTIC DES PERFORMANCES")
    print("=" * 50)
    
    # 1. Analyser les imports
    print("\n📦 1. ANALYSE DES IMPORTS")
    print("-" * 30)
    
    import_times = {}
    
    # Mesurer le temps d'import de Kivy/KivyMD
    start = time.time()
    try:
        import kivy
        import_times['kivy'] = time.time() - start
        print(f"✅ Kivy importé en {import_times['kivy']:.3f}s")
    except Exception as e:
        print(f"❌ Erreur import Kivy: {e}")
    
    start = time.time()
    try:
        import kivymd
        import_times['kivymd'] = time.time() - start
        print(f"✅ KivyMD importé en {import_times['kivymd']:.3f}s")
    except Exception as e:
        print(f"❌ Erreur import KivyMD: {e}")
    
    # Mesurer les imports de l'application
    start = time.time()
    try:
        from database.db_manager import DatabaseManager
        import_times['db_manager'] = time.time() - start
        print(f"✅ DatabaseManager importé en {import_times['db_manager']:.3f}s")
    except Exception as e:
        print(f"❌ Erreur import DatabaseManager: {e}")
    
    # 2. Analyser la base de données
    print("\n🗄️ 2. ANALYSE DE LA BASE DE DONNÉES")
    print("-" * 30)
    
    try:
        start = time.time()
        db_manager = DatabaseManager()
        
        connect_time = time.time()
        if db_manager.connect():
            connect_duration = time.time() - connect_time
            print(f"✅ Connexion DB en {connect_duration:.3f}s")
            
            init_time = time.time()
            if db_manager.initialize_database():
                init_duration = time.time() - init_time
                print(f"✅ Initialisation DB en {init_duration:.3f}s")
                
                # Tester quelques requêtes
                query_time = time.time()
                categories = db_manager.execute_query("SELECT COUNT(*) as count FROM categories")
                products = db_manager.execute_query("SELECT COUNT(*) as count FROM produits")
                query_duration = time.time() - query_time
                
                print(f"✅ Requêtes de test en {query_duration:.3f}s")
                if categories:
                    print(f"   📂 {categories[0]['count']} catégories")
                if products:
                    print(f"   📦 {products[0]['count']} produits")
            else:
                print("❌ Échec initialisation DB")
        else:
            print("❌ Échec connexion DB")
        
        db_manager.disconnect()
        total_db_time = time.time() - start
        print(f"📊 Temps total DB: {total_db_time:.3f}s")
        
    except Exception as e:
        print(f"❌ Erreur analyse DB: {e}")
    
    # 3. Analyser les fichiers
    print("\n📁 3. ANALYSE DES FICHIERS")
    print("-" * 30)
    
    project_root = Path(__file__).parent
    
    # Compter les fichiers Python
    py_files = list(project_root.rglob("*.py"))
    print(f"📄 {len(py_files)} fichiers Python trouvés")
    
    # Analyser la taille des fichiers
    large_files = []
    total_size = 0
    
    for file_path in py_files:
        try:
            size = file_path.stat().st_size
            total_size += size
            if size > 10000:  # Plus de 10KB
                large_files.append((file_path.name, size))
        except:
            pass
    
    print(f"📊 Taille totale: {total_size / 1024:.1f} KB")
    
    if large_files:
        print("📋 Fichiers volumineux (>10KB):")
        for name, size in sorted(large_files, key=lambda x: x[1], reverse=True)[:5]:
            print(f"   📄 {name}: {size / 1024:.1f} KB")
    
    # 4. Analyser les ressources système
    print("\n💻 4. ANALYSE DES RESSOURCES SYSTÈME")
    print("-" * 30)
    
    # Mémoire
    memory = psutil.virtual_memory()
    print(f"🧠 Mémoire disponible: {memory.available / (1024**3):.1f} GB / {memory.total / (1024**3):.1f} GB")
    print(f"📊 Utilisation mémoire: {memory.percent}%")
    
    # CPU
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"⚡ Utilisation CPU: {cpu_percent}%")
    print(f"🔢 Nombre de cœurs: {psutil.cpu_count()}")
    
    # Disque
    try:
        disk = psutil.disk_usage(str(project_root))
        print(f"💾 Espace disque libre: {disk.free / (1024**3):.1f} GB / {disk.total / (1024**3):.1f} GB")
    except Exception as e:
        print(f"⚠️ Impossible d'analyser l'espace disque: {e}")
    
    return import_times

def test_screen_loading():
    """Tester le temps de chargement des écrans"""
    print("\n🖥️ 5. TEST DE CHARGEMENT DES ÉCRANS")
    print("-" * 30)
    
    screens_to_test = [
        ('products_screen', 'ProductsScreen'),
        ('dashboard_screen', 'DashboardScreen'),
        ('clients_screen', 'ClientsScreen'),
    ]
    
    for module_name, class_name in screens_to_test:
        try:
            start = time.time()
            module = __import__(f'screens.{module_name}', fromlist=[class_name])
            screen_class = getattr(module, class_name)
            load_time = time.time() - start
            
            print(f"✅ {class_name} chargé en {load_time:.3f}s")
            
        except Exception as e:
            print(f"❌ Erreur chargement {class_name}: {e}")

def identify_bottlenecks():
    """Identifier les goulots d'étranglement"""
    print("\n🚨 6. IDENTIFICATION DES GOULOTS D'ÉTRANGLEMENT")
    print("-" * 30)
    
    bottlenecks = []
    
    # Analyser les imports lents
    import_times = analyze_startup_performance()
    
    for module, duration in import_times.items():
        if duration > 1.0:  # Plus d'1 seconde
            bottlenecks.append(f"Import lent: {module} ({duration:.3f}s)")
    
    # Vérifier les problèmes courants
    common_issues = [
        ("Trop de threads", "Vérifier l'utilisation de threading"),
        ("Imports circulaires", "Vérifier les dépendances entre modules"),
        ("Chargement synchrone", "Utiliser le chargement asynchrone"),
        ("Requêtes DB lentes", "Optimiser les requêtes et ajouter des index"),
        ("Widgets lourds", "Utiliser le lazy loading pour les widgets")
    ]
    
    print("⚠️ Problèmes potentiels identifiés:")
    for issue, solution in common_issues:
        print(f"   • {issue}")
        print(f"     💡 Solution: {solution}")
    
    return bottlenecks

def suggest_optimizations():
    """Suggérer des optimisations"""
    print("\n💡 7. SUGGESTIONS D'OPTIMISATION")
    print("-" * 30)
    
    optimizations = [
        "🚀 OPTIMISATIONS DE DÉMARRAGE:",
        "   • Lazy loading des écrans (charger à la demande)",
        "   • Import conditionnel des modules lourds",
        "   • Cache des requêtes DB fréquentes",
        "   • Initialisation asynchrone des composants",
        "",
        "⚡ OPTIMISATIONS D'INTERFACE:",
        "   • Utiliser RecycleView pour les listes longues",
        "   • Limiter le nombre de widgets simultanés",
        "   • Optimiser les animations et transitions",
        "   • Réduire la profondeur des layouts",
        "",
        "🗄️ OPTIMISATIONS BASE DE DONNÉES:",
        "   • Ajouter des index sur les colonnes fréquemment utilisées",
        "   • Utiliser des requêtes préparées",
        "   • Implémenter un pool de connexions",
        "   • Cache des résultats de requêtes",
        "",
        "🧠 OPTIMISATIONS MÉMOIRE:",
        "   • Libérer les ressources non utilisées",
        "   • Utiliser des générateurs au lieu de listes",
        "   • Optimiser les images et ressources",
        "   • Implémenter un garbage collector personnalisé"
    ]
    
    for optimization in optimizations:
        print(optimization)

def create_performance_report():
    """Créer un rapport de performance"""
    print("\n📊 8. RAPPORT DE PERFORMANCE")
    print("-" * 30)
    
    report = []
    
    # Mesurer le temps total de diagnostic
    start_time = time.time()
    
    # Exécuter tous les tests
    import_times = analyze_startup_performance()
    test_screen_loading()
    bottlenecks = identify_bottlenecks()
    
    total_time = time.time() - start_time
    
    report.append(f"📊 RAPPORT DE PERFORMANCE GESCOMPRO_LIBTAM")
    report.append(f"=" * 50)
    report.append(f"🕐 Temps de diagnostic: {total_time:.2f}s")
    report.append(f"📅 Date: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # Résumé des imports
    report.append("📦 TEMPS D'IMPORT:")
    for module, duration in import_times.items():
        status = "⚠️" if duration > 1.0 else "✅"
        report.append(f"   {status} {module}: {duration:.3f}s")
    
    report.append("")
    
    # Goulots d'étranglement
    if bottlenecks:
        report.append("🚨 GOULOTS D'ÉTRANGLEMENT IDENTIFIÉS:")
        for bottleneck in bottlenecks:
            report.append(f"   ⚠️ {bottleneck}")
    else:
        report.append("✅ Aucun goulot d'étranglement majeur identifié")
    
    report.append("")
    report.append("💡 Voir les suggestions d'optimisation ci-dessus")
    
    # Sauvegarder le rapport
    report_content = "\n".join(report)
    
    try:
        with open("performance_report.txt", "w", encoding="utf-8") as f:
            f.write(report_content)
        print("📄 Rapport sauvegardé dans 'performance_report.txt'")
    except Exception as e:
        print(f"❌ Erreur sauvegarde rapport: {e}")
    
    return report_content

def main():
    """Fonction principale du diagnostic"""
    print("🔍 DIAGNOSTIC COMPLET DES PERFORMANCES")
    print("🎯 Analyse de GesComPro_LibTam")
    print("=" * 60)
    
    try:
        # Exécuter le diagnostic complet
        import_times = analyze_startup_performance()
        test_screen_loading()
        bottlenecks = identify_bottlenecks()
        suggest_optimizations()
        report = create_performance_report()
        
        print("\n🎉 DIAGNOSTIC TERMINÉ")
        print("=" * 30)
        print("📋 Actions recommandées:")
        print("   1. Utiliser 'launch_optimized.py' au lieu de 'launch_simple.py'")
        print("   2. Implémenter le lazy loading des écrans")
        print("   3. Optimiser les requêtes de base de données")
        print("   4. Réduire les imports au démarrage")
        print("   5. Consulter le rapport détaillé dans 'performance_report.txt'")
        
    except Exception as e:
        print(f"❌ Erreur lors du diagnostic: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()