#!/usr/bin/env python3
"""
Test de l'interface utilisateur pour la modification de client
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_client_ui():
    """Test de l'interface utilisateur"""
    print("🧪 Test de l'interface utilisateur - Modification client")
    print("=" * 60)
    
    try:
        # Importer les modules nécessaires
        print("🔸 Import des modules...")
        from kivymd.app import MDApp
        from screens.clients_screen import ClientsScreen, ClientFormDialog, ClientCard
        print("✅ Modules importés")
        
        # Créer une app minimale pour les tests
        print("🔸 Création d'une app de test...")
        
        class TestApp(MDApp):
            def build(self):
                return ClientsScreen()
        
        # Données de test
        test_client_data = {
            'id': 1,
            'nom': 'Du<PERSON>',
            'prenom': '<PERSON>',
            'entreprise': '<PERSON><PERSON> Dupont',
            'email': '<EMAIL>',
            'telephone': '0123456789',
            'adresse': '123 Rue Test',
            'ville': 'Paris',
            'code_postal': '75001',
            'pays': 'France'
        }
        
        print("✅ Données de test préparées")
        
        # Test de création du dialog sans app
        print("🔸 Test de création du dialog...")
        try:
            dialog = ClientFormDialog(client_data=test_client_data)
            print("✅ Dialog créé avec succès")
            
            # Vérifier les champs
            print("🔸 Vérification des champs du dialog...")
            print(f"  - Nom field: {dialog.nom_field.text}")
            print(f"  - Prénom field: {dialog.prenom_field.text}")
            print(f"  - Email field: {dialog.email_field.text}")
            print("✅ Champs du dialog corrects")
            
        except Exception as e:
            print(f"❌ Erreur lors de la création du dialog: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test de création de la carte client
        print("🔸 Test de création de la carte client...")
        try:
            def dummy_edit_callback(data):
                print(f"Edit callback appelé avec: {data}")
            
            def dummy_delete_callback(data):
                print(f"Delete callback appelé avec: {data}")
            
            card = ClientCard(
                test_client_data,
                dummy_edit_callback,
                dummy_delete_callback
            )
            print("✅ Carte client créée avec succès")
            
        except Exception as e:
            print(f"❌ Erreur lors de la création de la carte: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n🎉 TOUS LES TESTS D'INTERFACE RÉUSSIS!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors des tests d'interface: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Test de l'interface utilisateur - Modification client")
    print("=" * 70)
    
    success = test_client_ui()
    
    if success:
        print("\n✅ L'interface utilisateur fonctionne correctement")
        print("🔍 Le problème doit être dans l'intégration avec l'app")
    else:
        print("\n❌ Problème identifié dans l'interface utilisateur")
    
    print("\nAppuyez sur Entrée pour continuer...")
    input()