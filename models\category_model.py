"""
Modèle de données pour les catégories
"""

from datetime import datetime
from database.db_manager import DatabaseManager


class CategoryModel:
    """Modèle pour la gestion des catégories"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def get_all_categories(self):
        """Récupérer toutes les catégories"""
        try:
            if not self.db_manager.connect():
                return []
            
            categories = self.db_manager.execute_query("""
                SELECT id, nom, description, date_creation
                FROM categories 
                ORDER BY nom ASC
            """)
            
            return categories or []
            
        except Exception as e:
            print(f"Erreur lors de la récupération des catégories: {e}")
            return []
        finally:
            self.db_manager.close()
    
    def get_category_by_id(self, category_id):
        """Récupérer une catégorie par son ID"""
        try:
            if not self.db_manager.connect():
                return None
            
            category = self.db_manager.execute_query("""
                SELECT id, nom, description, date_creation
                FROM categories 
                WHERE id = ?
            """, (category_id,))
            
            return category[0] if category else None
            
        except Exception as e:
            print(f"Erreur lors de la récupération de la catégorie {category_id}: {e}")
            return None
        finally:
            self.db_manager.close()
    
    def create_category(self, category_data):
        """Créer une nouvelle catégorie"""
        try:
            if not self.db_manager.connect():
                return None
            
            # Vérifier si le nom existe déjà
            existing = self.db_manager.execute_query("""
                SELECT id FROM categories WHERE nom = ?
            """, (category_data['nom'],))
            
            if existing:
                raise ValueError(f"Une catégorie avec le nom '{category_data['nom']}' existe déjà")
            
            # Insérer la nouvelle catégorie
            category_id = self.db_manager.execute_insert("""
                INSERT INTO categories (nom, description)
                VALUES (?, ?)
            """, (
                category_data['nom'],
                category_data.get('description', '')
            ))
            
            return category_id
            
        except Exception as e:
            print(f"Erreur lors de la création de la catégorie: {e}")
            raise e
        finally:
            self.db_manager.close()
    
    def update_category(self, category_id, category_data):
        """Mettre à jour une catégorie"""
        try:
            if not self.db_manager.connect():
                return False
            
            # Vérifier si le nom existe déjà (sauf pour la catégorie actuelle)
            existing = self.db_manager.execute_query("""
                SELECT id FROM categories WHERE nom = ? AND id != ?
            """, (category_data['nom'], category_id))
            
            if existing:
                raise ValueError(f"Une catégorie avec le nom '{category_data['nom']}' existe déjà")
            
            # Mettre à jour la catégorie
            success = self.db_manager.execute_update("""
                UPDATE categories 
                SET nom = ?, description = ?
                WHERE id = ?
            """, (
                category_data['nom'],
                category_data.get('description', ''),
                category_id
            ))
            
            return success
            
        except Exception as e:
            print(f"Erreur lors de la mise à jour de la catégorie {category_id}: {e}")
            raise e
        finally:
            self.db_manager.close()
    
    def delete_category(self, category_id):
        """Supprimer une catégorie"""
        try:
            if not self.db_manager.connect():
                return False
            
            # Vérifier s'il y a des produits liés à cette catégorie
            products = self.db_manager.execute_query("""
                SELECT COUNT(*) as count FROM produits WHERE categorie_id = ?
            """, (category_id,))
            
            if products and products[0]['count'] > 0:
                raise ValueError(f"Impossible de supprimer la catégorie: {products[0]['count']} produit(s) y sont liés")
            
            # Supprimer la catégorie
            success = self.db_manager.execute_update("""
                DELETE FROM categories WHERE id = ?
            """, (category_id,))
            
            return success
            
        except Exception as e:
            print(f"Erreur lors de la suppression de la catégorie {category_id}: {e}")
            raise e
        finally:
            self.db_manager.close()
    
    def search_categories(self, search_term):
        """Rechercher des catégories"""
        try:
            if not self.db_manager.connect():
                return []
            
            categories = self.db_manager.execute_query("""
                SELECT id, nom, description, date_creation
                FROM categories 
                WHERE nom LIKE ? OR description LIKE ?
                ORDER BY nom ASC
            """, (f"%{search_term}%", f"%{search_term}%"))
            
            return categories or []
            
        except Exception as e:
            print(f"Erreur lors de la recherche de catégories: {e}")
            return []
        finally:
            self.db_manager.close()
    
    def get_category_stats(self, category_id):
        """Obtenir les statistiques d'une catégorie"""
        try:
            if not self.db_manager.connect():
                return {}
            
            # Nombre de produits dans la catégorie
            products_count = self.db_manager.execute_query("""
                SELECT COUNT(*) as count FROM produits WHERE categorie_id = ?
            """, (category_id,))
            
            # Valeur totale du stock dans la catégorie (utiliser prix_vente)
            stock_value = self.db_manager.execute_query("""
                SELECT SUM(prix_vente * stock_actuel) as total_value
                FROM produits 
                WHERE categorie_id = ?
            """, (category_id,))
            
            return {
                'products_count': products_count[0]['count'] if products_count else 0,
                'stock_value': stock_value[0]['total_value'] if stock_value and stock_value[0]['total_value'] else 0
            }
            
        except Exception as e:
            print(f"Erreur lors de la récupération des statistiques: {e}")
            return {}
        finally:
            self.db_manager.close()
    
    def get_active_categories(self):
        """Récupérer uniquement les catégories actives"""
        try:
            if not self.db_manager.connect():
                return []
            
            categories = self.db_manager.execute_query("""
                SELECT id, nom, description
                FROM categories 
                ORDER BY nom ASC
            """)
            
            return categories or []
            
        except Exception as e:
            print(f"Erreur lors de la récupération des catégories actives: {e}")
            return []
        finally:
            self.db_manager.close()