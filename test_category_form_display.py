#!/usr/bin/env python3
"""
Test d'affichage du formulaire de catégorie
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from forms.category_form import CategoryFormDialog


class TestCategoryFormDisplayApp(MDApp):
    """Test d'affichage du formulaire de catégorie"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Affichage Formulaire Catégorie"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construction de l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        # Titre
        title = MDLabel(
            text="🧪 Test d'Affichage\nFormulaire Catégorie",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Instructions
        instructions = MDLabel(
            text="Cliquez sur les boutons ci-dessous pour tester\n"
                 "l'affichage du formulaire de catégorie :\n\n"
                 "• Formulaire de création\n"
                 "• Formulaire de modification\n\n"
                 "Vérifiez que tous les champs sont visibles !",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="150dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="200dp"
        )
        
        # Test formulaire création
        create_btn = MDRaisedButton(
            text="🆕 Tester Formulaire de Création",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_create_form
        )
        
        # Test formulaire modification
        edit_btn = MDRaisedButton(
            text="✏️ Tester Formulaire de Modification",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_edit_form
        )
        
        # Test formulaire simple
        simple_btn = MDRaisedButton(
            text="📝 Tester Formulaire Simple",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_simple_form
        )
        
        buttons_layout.add_widget(create_btn)
        buttons_layout.add_widget(edit_btn)
        buttons_layout.add_widget(simple_btn)
        
        # Résultats
        self.result_label = MDLabel(
            text="Prêt pour le test d'affichage du formulaire !",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(instructions)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_create_form(self, *args):
        """Tester le formulaire de création"""
        try:
            self.result_label.text = "🆕 Ouverture du formulaire de création..."
            
            dialog = CategoryFormDialog(
                on_save_callback=self.on_test_save
            )
            dialog.open()
            
            self.result_label.text = "✅ Formulaire de création ouvert !\n\n" \
                                   "Vérifiez que vous voyez :\n" \
                                   "• Titre du formulaire\n" \
                                   "• Champ nom (obligatoire)\n" \
                                   "• Champ description (optionnel)\n" \
                                   "• Boutons Annuler et Enregistrer"
            
            print("🆕 Formulaire de création ouvert")
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur ouverture formulaire création :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def test_edit_form(self, *args):
        """Tester le formulaire de modification"""
        try:
            self.result_label.text = "✏️ Ouverture du formulaire de modification..."
            
            # Données de test pour la modification
            test_category = {
                'id': 1,
                'nom': 'Catégorie Test',
                'description': 'Description de test pour la modification',
                'date_creation': '2024-01-01 10:00:00'
            }
            
            dialog = CategoryFormDialog(
                category_data=test_category,
                on_save_callback=self.on_test_save
            )
            dialog.open()
            
            self.result_label.text = "✅ Formulaire de modification ouvert !\n\n" \
                                   "Vérifiez que vous voyez :\n" \
                                   "• Titre 'Modifier la catégorie'\n" \
                                   "• Champs pré-remplis\n" \
                                   "• Informations de base de données\n" \
                                   "• Boutons Annuler et Enregistrer"
            
            print("✏️ Formulaire de modification ouvert")
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur ouverture formulaire modification :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def test_simple_form(self, *args):
        """Tester un formulaire simple sans scroll"""
        try:
            from kivymd.uix.dialog import MDDialog
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.textfield import MDTextField
            from kivymd.uix.button import MDFlatButton, MDRaisedButton
            
            # Créer un contenu simple
            content = MDBoxLayout(
                orientation='vertical',
                spacing="20dp",
                size_hint_y=None,
                height="200dp"
            )
            
            nom_field = MDTextField(
                hint_text="Nom de la catégorie",
                mode="rectangle"
            )
            
            desc_field = MDTextField(
                hint_text="Description",
                mode="rectangle"
            )
            
            content.add_widget(nom_field)
            content.add_widget(desc_field)
            
            # Créer le dialog simple
            dialog = MDDialog(
                title="📝 Formulaire Simple",
                type="custom",
                content_cls=content,
                buttons=[
                    MDFlatButton(text="Annuler", on_release=lambda x: dialog.dismiss()),
                    MDRaisedButton(text="OK", on_release=lambda x: dialog.dismiss())
                ]
            )
            
            dialog.open()
            
            self.result_label.text = "✅ Formulaire simple ouvert !\n\n" \
                                   "Ce formulaire devrait afficher :\n" \
                                   "• Titre\n" \
                                   "• Deux champs de texte\n" \
                                   "• Boutons en bas"
            
            print("📝 Formulaire simple ouvert")
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur formulaire simple :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def on_test_save(self, category_data):
        """Callback de test"""
        nom = category_data.get('nom', 'N/A')
        self.result_label.text = f"🎉 Test de sauvegarde réussi !\n\n" \
                               f"Catégorie : {nom}\n" \
                               f"Le formulaire fonctionne correctement !"
        print(f"🎉 Test sauvegarde: {nom}")


def main():
    """Fonction principale"""
    print("🧪 Test d'Affichage - Formulaire Catégorie")
    print("=" * 50)
    print("OBJECTIF : Vérifier l'affichage du formulaire")
    print()
    print("TESTS DISPONIBLES :")
    print("🆕 Formulaire de création")
    print("✏️ Formulaire de modification")
    print("📝 Formulaire simple de référence")
    print()
    print("VÉRIFICATIONS À EFFECTUER :")
    print("✅ Titre du formulaire visible")
    print("✅ Champs de saisie visibles")
    print("✅ Boutons en bas visibles")
    print("✅ Contenu scrollable si nécessaire")
    print("=" * 50)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestCategoryFormDisplayApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()