#!/usr/bin/env python3
"""
Test d'affichage du formulaire de vente
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from sales_form_improved import ImprovedSaleFormDialog


class TestSalesFormDisplayApp(MDApp):
    """Test d'affichage du formulaire de vente"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Affichage Formulaire Vente"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Green"
    
    def build(self):
        """Construction de l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        # Titre
        title = MDLabel(
            text="🧪 Test d'Affichage\nFormulaire de Vente",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Instructions
        instructions = MDLabel(
            text="Cliquez sur les boutons ci-dessous pour tester\n"
                 "l'affichage du formulaire de vente :\n\n"
                 "• Formulaire de création\n"
                 "• Formulaire de modification\n\n"
                 "Vérifiez que tous les champs sont visibles !",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="150dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="160dp"
        )
        
        # Test formulaire création
        create_btn = MDRaisedButton(
            text="🛒 Tester Formulaire de Création",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_create_form
        )
        
        # Test formulaire modification
        edit_btn = MDRaisedButton(
            text="✏️ Tester Formulaire de Modification",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_edit_form
        )
        
        buttons_layout.add_widget(create_btn)
        buttons_layout.add_widget(edit_btn)
        
        # Résultats
        self.result_label = MDLabel(
            text="Prêt pour le test d'affichage du formulaire de vente !",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(instructions)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_create_form(self, *args):
        """Tester le formulaire de création"""
        try:
            self.result_label.text = "🛒 Ouverture du formulaire de création..."
            
            dialog = ImprovedSaleFormDialog(
                on_save_callback=self.on_test_save
            )
            dialog.open()
            
            self.result_label.text = "✅ Formulaire de création ouvert !\n\n" \
                                   "Vérifiez que vous voyez :\n" \
                                   "• Titre du formulaire\n" \
                                   "• Section client (liste déroulante)\n" \
                                   "• Section mode de paiement\n" \
                                   "• Section produits\n" \
                                   "• Section notes\n" \
                                   "• Section totaux\n" \
                                   "• Boutons Annuler et Enregistrer"
            
            print("🛒 Formulaire de création de vente ouvert")
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur ouverture formulaire création :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def test_edit_form(self, *args):
        """Tester le formulaire de modification"""
        try:
            self.result_label.text = "✏️ Ouverture du formulaire de modification..."
            
            # Données de test pour la modification
            test_sale = {
                'id': 1,
                'numero_facture': 'FAC-2024-001',
                'client_id': 1,
                'montant_ht': 100.00,
                'montant_ttc': 120.00,
                'mode_paiement': 'Espèces',
                'statut': 'En cours',
                'notes': 'Vente de test',
                'date_vente': '2024-01-01 10:00:00'
            }
            
            dialog = ImprovedSaleFormDialog(
                sale_data=test_sale,
                on_save_callback=self.on_test_save
            )
            dialog.open()
            
            self.result_label.text = "✅ Formulaire de modification ouvert !\n\n" \
                                   "Vérifiez que vous voyez :\n" \
                                   "• Titre 'Modifier la vente'\n" \
                                   "• Numéro de facture affiché\n" \
                                   "• Champs pré-remplis\n" \
                                   "• Informations de base de données\n" \
                                   "• Boutons Annuler et Enregistrer"
            
            print("✏️ Formulaire de modification de vente ouvert")
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur ouverture formulaire modification :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def on_test_save(self, sale_data):
        """Callback de test"""
        numero = sale_data.get('numero_facture', 'N/A')
        self.result_label.text = f"🎉 Test de sauvegarde réussi !\n\n" \
                               f"Facture : {numero}\n" \
                               f"Le formulaire fonctionne correctement !"
        print(f"🎉 Test sauvegarde: {numero}")


def main():
    """Fonction principale"""
    print("🧪 Test d'Affichage - Formulaire de Vente")
    print("=" * 50)
    print("OBJECTIF : Vérifier l'affichage du formulaire de vente")
    print()
    print("TESTS DISPONIBLES :")
    print("🛒 Formulaire de création")
    print("✏️ Formulaire de modification")
    print()
    print("VÉRIFICATIONS À EFFECTUER :")
    print("✅ Titre du formulaire visible")
    print("✅ Section client avec liste déroulante")
    print("✅ Section mode de paiement")
    print("✅ Section produits")
    print("✅ Section notes")
    print("✅ Section totaux")
    print("✅ Boutons en bas visibles")
    print("✅ Contenu scrollable")
    print("=" * 50)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestSalesFormDisplayApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()