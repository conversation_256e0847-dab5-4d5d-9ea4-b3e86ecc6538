"""
Écran de gestion des clients
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import MDTextField
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.list import MDList, OneLineAvatarIconListItem, IconLeftWidget, IconRightWidget
from kivymd.app import MDApp
from kivy.clock import Clock
import threading

# Import du formulaire de vente
from forms.sales_form import SalesFormDialog


class ClientCard(MDCard):
    """Carte pour afficher un client"""
    
    def __init__(self, client_data, on_edit_callback, on_delete_callback, on_new_sale_callback=None, **kwargs):
        super().__init__(**kwargs)
        self.client_data = client_data
        self.elevation = 2
        self.padding = "6dp"
        self.size_hint_y = None
        self.height = "140dp"
        self.spacing = "8dp"
        
        layout = MDBoxLayout(orientation='vertical', spacing="2dp")
        
        # En-tête avec nom et actions
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="32dp")
        
        # Nom du client
        nom_complet = f"{client_data.get('prenom', '')} {client_data.get('nom', '')}".strip()
        if not nom_complet:
            nom_complet = client_data.get('entreprise', 'Client sans nom')
        
        nom_label = MDLabel(
            text=nom_complet,
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_x=0.6
        )
        
        # Boutons d'action
        actions_layout = MDBoxLayout(orientation='horizontal', size_hint_x=0.4, spacing="2dp")
        
        # Bouton nouvelle vente
        if on_new_sale_callback:
            print(f"🛒 Création bouton nouvelle vente pour client: {client_data.get('nom', 'Inconnu')}")
            new_sale_btn = MDIconButton(
                icon="cart-plus",
                theme_icon_color="Custom",
                md_bg_color=(0.1, 0.8, 0.2, 1),  # Couleur verte plus vive
                icon_size="24dp",  # Taille d'icône plus grande
                size_hint=(None, None),
                size=("40dp", "40dp"),  # Taille du bouton plus grande
                on_release=lambda x: on_new_sale_callback(client_data)
            )
            actions_layout.add_widget(new_sale_btn)
        else:
            print(f"⚠️ Pas de callback nouvelle vente pour client: {client_data.get('nom', 'Inconnu')}")
        
        edit_btn = MDIconButton(
            icon="pencil",
            theme_icon_color="Primary",
            on_release=lambda x: on_edit_callback(client_data)
        )
        
        delete_btn = MDIconButton(
            icon="delete",
            theme_icon_color="Error",
            on_release=lambda x: on_delete_callback(client_data)
        )
        
        actions_layout.add_widget(edit_btn)
        actions_layout.add_widget(delete_btn)
        
        header_layout.add_widget(nom_label)
        header_layout.add_widget(actions_layout)
        
        # Informations du client
        info_layout = MDBoxLayout(orientation='vertical', spacing="2dp")
        
        if client_data.get('entreprise'):
            entreprise_label = MDLabel(
                text=f"Entreprise: {client_data['entreprise']}",
                font_style="Caption",
                theme_text_color="Secondary"
            )
            info_layout.add_widget(entreprise_label)
        
        if client_data.get('email'):
            email_label = MDLabel(
                text=f"Email: {client_data['email']}",
                font_style="Caption",
                theme_text_color="Secondary"
            )
            info_layout.add_widget(email_label)
        
        if client_data.get('telephone'):
            tel_label = MDLabel(
                text=f"Téléphone: {client_data['telephone']}",
                font_style="Caption",
                theme_text_color="Secondary"
            )
            info_layout.add_widget(tel_label)
        
        if client_data.get('ville'):
            ville_label = MDLabel(
                text=f"Ville: {client_data['ville']}",
                font_style="Caption",
                theme_text_color="Secondary"
            )
            info_layout.add_widget(ville_label)
        
        layout.add_widget(header_layout)
        layout.add_widget(info_layout)
        self.add_widget(layout)


class ClientFormDialog(MDDialog):
    """Dialog pour ajouter/modifier un client"""
    
    def __init__(self, client_data=None, on_save_callback=None, **kwargs):
        # Vérifier que l'app est initialisée
        app = MDApp.get_running_app()
        if not app:
            raise RuntimeError("L'application MDApp doit être initialisée avant de créer ce dialog")
        
        self.client_data = client_data or {}
        self.on_save_callback = on_save_callback
        
        # Création du formulaire
        form_layout = MDBoxLayout(orientation='vertical', spacing="10dp", size_hint_y=None,height="660dp")
        
        # Fonction helper pour convertir les valeurs en string sécurisé
        def safe_str(value):
            return str(value) if value is not None else ''
        
        # Création des champs avec navigation par tabulation
        self.nom_field = MDTextField(
            hint_text="Nom *",
            text=safe_str(self.client_data.get('nom', '')),
            required=True
        )
        
        self.prenom_field = MDTextField(
            hint_text="Prénom",
            text=safe_str(self.client_data.get('prenom', ''))
        )
        
        self.entreprise_field = MDTextField(
            hint_text="Entreprise",
            text=safe_str(self.client_data.get('entreprise', ''))
        )
        
        self.email_field = MDTextField(
            hint_text="Email",
            text=safe_str(self.client_data.get('email', '')),
            helper_text="Format: <EMAIL>",
            helper_text_mode="on_focus"
        )
        
        self.telephone_field = MDTextField(
            hint_text="Téléphone",
            text=safe_str(self.client_data.get('telephone', ''))
        )
        
        self.adresse_field = MDTextField(
            hint_text="Adresse",
            text=safe_str(self.client_data.get('adresse', '')),
            multiline=True,
            max_height="80dp"
        )
        
        self.ville_field = MDTextField(
            hint_text="Ville",
            text=safe_str(self.client_data.get('ville', ''))
        )
        
        self.code_postal_field = MDTextField(
            hint_text="Code postal",
            text=safe_str(self.client_data.get('code_postal', ''))
        )
        
        self.pays_field = MDTextField(
            hint_text="Pays",
            text=safe_str(self.client_data.get('pays', 'France'))
        )
        
        # Configuration de l'ordre de tabulation
        self.fields_order = [
            self.nom_field,
            self.prenom_field, 
            self.entreprise_field,
            self.email_field,
            self.telephone_field,
            self.adresse_field,
            self.ville_field,
            self.code_postal_field,
            self.pays_field
        ]
        
        # Ajout des champs au formulaire dans l'ordre de tabulation
        for field in self.fields_order:
            form_layout.add_widget(field)
        
        # Configuration de la navigation par tabulation
        self._setup_tab_navigation()
        
        # Boutons
        buttons = [
            MDFlatButton(
                text="ANNULER",
                on_release=self.dismiss
            ),
            MDRaisedButton(
                text="ENREGISTRER",
                on_release=self.save_client
            )
        ]
        
        title = "Modifier le client" if client_data else "Nouveau client"
        
        super().__init__(
            title=title,
            type="custom",
            content_cls=form_layout,
            buttons=buttons,
            size_hint=(0.9, None),
            height="600dp",
            **kwargs
        )
    
    def _setup_tab_navigation(self):
        """Configure la navigation par tabulation entre les champs"""
        # Configuration de l'ordre de tabulation pour chaque champ
        for i, field in enumerate(self.fields_order):
            # Définir le champ suivant pour la navigation Tab
            if i < len(self.fields_order) - 1:
                field.next_field = self.fields_order[i + 1]
            else:
                field.next_field = self.fields_order[0]  # Boucle vers le premier
            
            # Définir le champ précédent pour Shift+Tab
            if i > 0:
                field.prev_field = self.fields_order[i - 1]
            else:
                field.prev_field = self.fields_order[-1]  # Boucle vers le dernier
        
        # Donner le focus au premier champ
        if self.fields_order:
            from kivy.clock import Clock
            Clock.schedule_once(lambda dt: setattr(self.fields_order[0], 'focus', True), 0.1)
    
    def save_client(self, *args):
        """Enregistrer le client"""
        # Validation basique
        if not self.nom_field.text.strip():
            self.nom_field.error = True
            self.nom_field.helper_text = "Le nom est obligatoire"
            return
        
        # Préparer les données
        client_data = {
            'nom': self.nom_field.text.strip(),
            'prenom': self.prenom_field.text.strip(),
            'entreprise': self.entreprise_field.text.strip(),
            'email': self.email_field.text.strip(),
            'telephone': self.telephone_field.text.strip(),
            'adresse': self.adresse_field.text.strip(),
            'ville': self.ville_field.text.strip(),
            'code_postal': self.code_postal_field.text.strip(),
            'pays': self.pays_field.text.strip() or 'France'
        }
        
        # Ajouter l'ID si c'est une modification
        if self.client_data.get('id'):
            client_data['id'] = self.client_data['id']
        
        # Appeler le callback
        if self.on_save_callback:
            self.on_save_callback(client_data)
        
        self.dismiss()


class ClientsScreen(MDScreen):
    """Écran de gestion des clients"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.clients_data = []
        self.filtered_clients = []
        self.build_ui()
    
    def build_ui(self):
        """Construction de l'interface utilisateur"""
        main_layout = MDBoxLayout(orientation='vertical', padding="16dp", spacing="16dp")
        
        # En-tête avec titre et bouton d'ajout
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="48dp")
        
        title_label = MDLabel(
            text="Gestion des Clients",
            font_style="H5",
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        
        add_button = MDRaisedButton(
            text="Nouveau Client",
            icon="plus",
            on_release=self.add_client,
            size_hint_x=0.3
        )
        
        header_layout.add_widget(title_label)
        header_layout.add_widget(add_button)
        
        # Barre de recherche
        self.search_field = MDTextField(
            hint_text="Rechercher un client...",
            icon_left="magnify",
            size_hint_y=None,
            height="56dp",
            on_text=self.filter_clients
        )
        
        # ScrollView pour la liste des clients
        self.scroll = MDScrollView()
        self.clients_layout = MDBoxLayout(orientation='vertical', spacing="8dp", adaptive_height=True)
        self.scroll.add_widget(self.clients_layout)
        
        # Message quand aucun client
        self.no_clients_label = MDLabel(
            text="Aucun client trouvé",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        main_layout.add_widget(header_layout)
        main_layout.add_widget(self.search_field)
        main_layout.add_widget(self.scroll)
        
        self.add_widget(main_layout)
    
    def on_enter(self):
        """Actions à effectuer lors de l'entrée sur l'écran"""
        self.load_clients()
    
    def load_clients(self):
        """Charger la liste des clients"""
        def load_data():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                # Récupérer tous les clients
                self.clients_data = db_manager.execute_query("""
                    SELECT * FROM clients 
                    WHERE actif = 1 
                    ORDER BY nom, prenom
                """)
                
                # Mettre à jour l'interface
                Clock.schedule_once(self.update_clients_ui, 0)
                
            except Exception as e:
                print(f"Erreur lors du chargement des clients: {e}")
        
        threading.Thread(target=load_data, daemon=True).start()
    
    def update_clients_ui(self, dt):
        """Mettre à jour l'interface des clients"""
        self.clients_layout.clear_widgets()
        
        clients_to_show = self.filtered_clients if hasattr(self, 'filtered_clients') and self.filtered_clients else self.clients_data
        
        if not clients_to_show:
            self.clients_layout.add_widget(self.no_clients_label)
        else:
            for client in clients_to_show:
                client_card = ClientCard(
                    client,
                    self.edit_client,
                    self.delete_client,
                    self.new_sale_for_client
                )
                self.clients_layout.add_widget(client_card)
    
    def filter_clients(self, instance, text):
        """Filtrer les clients selon le texte de recherche"""
        if not text.strip():
            self.filtered_clients = self.clients_data
        else:
            search_text = text.lower()
            self.filtered_clients = []
            
            for client in self.clients_data:
                # Recherche dans nom, prénom, entreprise, email
                searchable_fields = [
                    client.get('nom', ''),
                    client.get('prenom', ''),
                    client.get('entreprise', ''),
                    client.get('email', '')
                ]
                
                if any(search_text in field.lower() for field in searchable_fields if field):
                    self.filtered_clients.append(client)
        
        self.update_clients_ui(None)
    
    def add_client(self, *args):
        """Ajouter un nouveau client"""
        try:
            # Vérifier que l'app est bien initialisée
            app = MDApp.get_running_app()
            if not app:
                print("❌ Application non initialisée")
                return
            
            dialog = ClientFormDialog(on_save_callback=self.save_client)
            dialog.open()
            
        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du dialog d'ajout: {e}")
            import traceback
            traceback.print_exc()
    
    def edit_client(self, client_data):
        """Modifier un client existant"""
        try:
            # Vérifier que l'app est bien initialisée
            app = MDApp.get_running_app()
            if not app:
                print("❌ Application non initialisée")
                return
            
            # Convertir client_data en dictionnaire si nécessaire
            if hasattr(client_data, 'keys'):
                client_dict = dict(client_data)
            else:
                client_dict = client_data
            
            dialog = ClientFormDialog(client_data=client_dict, on_save_callback=self.save_client)
            dialog.open()
            
        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du dialog de modification: {e}")
            import traceback
            traceback.print_exc()
    
    def save_client(self, client_data):
        """Enregistrer un client (nouveau ou modifié)"""
        def save_data():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                if client_data.get('id'):
                    # Modification
                    query = """
                        UPDATE clients SET 
                        nom = ?, prenom = ?, entreprise = ?, email = ?, 
                        telephone = ?, adresse = ?, ville = ?, code_postal = ?, pays = ?
                        WHERE id = ?
                    """
                    params = (
                        client_data['nom'], client_data['prenom'], client_data['entreprise'],
                        client_data['email'], client_data['telephone'], client_data['adresse'],
                        client_data['ville'], client_data['code_postal'], client_data['pays'],
                        client_data['id']
                    )
                else:
                    # Nouveau client
                    query = """
                        INSERT INTO clients 
                        (nom, prenom, entreprise, email, telephone, adresse, ville, code_postal, pays)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    params = (
                        client_data['nom'], client_data['prenom'], client_data['entreprise'],
                        client_data['email'], client_data['telephone'], client_data['adresse'],
                        client_data['ville'], client_data['code_postal'], client_data['pays']
                    )
                
                success = db_manager.execute_update(query, params)
                
                if success:
                    # Recharger la liste
                    Clock.schedule_once(lambda dt: self.load_clients(), 0)
                else:
                    print("Erreur lors de l'enregistrement du client")
                
            except Exception as e:
                print(f"Erreur lors de l'enregistrement: {e}")
        
        threading.Thread(target=save_data, daemon=True).start()
    
    def delete_client(self, client_data):
        """Supprimer un client (désactivation)"""
        def confirm_delete():
            def delete_data():
                try:
                    app = MDApp.get_running_app()
                    db_manager = app.db_manager
                    
                    # Désactiver le client au lieu de le supprimer
                    success = db_manager.execute_update(
                        "UPDATE clients SET actif = 0 WHERE id = ?",
                        (client_data['id'],)
                    )
                    
                    if success:
                        # Recharger la liste
                        Clock.schedule_once(lambda dt: self.load_clients(), 0)
                    else:
                        print("Erreur lors de la suppression du client")
                
                except Exception as e:
                    print(f"Erreur lors de la suppression: {e}")
            
            threading.Thread(target=delete_data, daemon=True).start()
        
        # Dialog de confirmation
        nom_complet = f"{client_data.get('prenom', '')} {client_data.get('nom', '')}".strip()
        
        confirm_dialog = MDDialog(
            title="Confirmer la suppression",
            text=f"Êtes-vous sûr de vouloir supprimer le client '{nom_complet}' ?",
            buttons=[
                MDFlatButton(
                    text="ANNULER",
                    on_release=lambda x: confirm_dialog.dismiss()
                ),
                MDRaisedButton(
                    text="SUPPRIMER",
                    md_bg_color=(1, 0.3, 0.3, 1),
                    on_release=lambda x: (confirm_delete(), confirm_dialog.dismiss())
                )
            ]
        )
        confirm_dialog.open()
    
    def new_sale_for_client(self, client_data):
        """Créer une nouvelle vente pour ce client"""
        print(f"🛒 BOUTON CLIQUÉ - Nouvelle vente pour: {client_data.get('nom', 'Inconnu')}")
        try:
            # Créer le formulaire de vente avec le client pré-sélectionné
            print(f"📋 Données client: ID={client_data.get('id')}, Nom={client_data.get('nom')}")
            dialog = SalesFormDialog(
                sale_data={'client_id': client_data['id'], 'client_data': client_data},
                on_save_callback=self.on_sale_saved
            )
            print("✅ Formulaire de vente créé, ouverture...")
            dialog.open()
            
        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du formulaire de vente: {e}")
            import traceback
            traceback.print_exc()
            
            # Dialog d'erreur pour l'utilisateur
            error_dialog = MDDialog(
                title="Erreur",
                text=f"Impossible d'ouvrir le formulaire de vente.\nErreur: {str(e)}",
                buttons=[
                    MDFlatButton(
                        text="OK",
                        on_release=lambda x: error_dialog.dismiss()
                    )
                ]
            )
            error_dialog.open()
    
    def on_sale_saved(self, sale_data):
        """Callback appelé quand une vente est sauvegardée"""
        try:
            print(f"✅ Vente sauvegardée pour le client: {sale_data.get('client_id')}")
            
            # Afficher un message de confirmation
            success_dialog = MDDialog(
                title="Succès",
                text="La vente a été créée avec succès !",
                buttons=[
                    MDFlatButton(
                        text="OK",
                        on_release=lambda x: success_dialog.dismiss()
                    )
                ]
            )
            success_dialog.open()
            
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde: {e}")