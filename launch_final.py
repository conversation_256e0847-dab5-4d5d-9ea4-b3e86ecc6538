"""
Script de lancement final pour GesComPro_LibTam
Avec toutes les optimisations et corrections appliquées
"""

import sys
import os
import warnings
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

# Supprimer les avertissements
warnings.filterwarnings("ignore")

def configure_environment():
    """Configurer l'environnement pour un lancement optimal"""
    # Configuration Kivy
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    os.environ['KIVY_LOG_MODE'] = 'PYTHON'
    
    # Configuration matplotlib
    try:
        import matplotlib
        matplotlib.use('Agg')
        import matplotlib.pyplot as plt
        plt.ioff()
        print("✅ Matplotlib configuré en mode sécurisé")
    except Exception as e:
        print(f"⚠️ Configuration matplotlib: {e}")
        os.environ['GESCOM_DISABLE_MATPLOTLIB'] = '1'


def check_dependencies():
    """Vérifier les dépendances essentielles"""
    print("🔍 Vérification des dépendances...")
    
    required_deps = {
        'kivy': 'Interface utilisateur',
        'kivymd': 'Material Design',
        'reportlab': 'Génération PDF'
    }
    
    missing = []
    for dep, desc in required_deps.items():
        try:
            __import__(dep)
            print(f"   ✅ {desc}")
        except ImportError:
            missing.append(dep)
            print(f"   ❌ {desc} manquant")
    
    # Matplotlib optionnel
    try:
        import matplotlib
        print(f"   ✅ Graphiques matplotlib")
    except ImportError:
        print(f"   ⚠️ Graphiques en mode texte")
    
    if missing:
        print(f"\n❌ Dépendances manquantes: {', '.join(missing)}")
        print("Installez avec: pip install " + " ".join(missing))
        return False
    
    return True


def optimize_database():
    """Optimiser la base de données rapidement"""
    print("⚡ Optimisation de la base de données...")
    
    try:
        from database.db_manager import DatabaseManager
        
        db = DatabaseManager()
        if db.connect():
            # Initialiser si nécessaire
            db.initialize_database()
            print("   ✅ Base de données optimisée et prête")
            return True
        else:
            print("   ⚠️ Problème de connexion à la base de données")
            return False
    except Exception as e:
        print(f"   ⚠️ Erreur DB: {e}")
        return False


def main():
    """Fonction principale optimisée"""
    print("🚀 LANCEMENT FINAL DE GesComPro_LibTam")
    print("=" * 45)
    print("📱 Gestion Commerciale Professionnelle")
    print("🔧 Version optimisée avec corrections")
    print("=" * 45)
    
    # Configuration de l'environnement
    configure_environment()
    
    # Vérification des dépendances
    if not check_dependencies():
        input("\nAppuyez sur Entrée pour quitter...")
        return
    
    # Optimisation de la base de données
    db_ok = optimize_database()
    if not db_ok:
        print("⚠️ Avertissement: Problème avec la base de données")
    
    # Démarrage de l'application
    print("\n📱 Démarrage de l'application...")
    
    try:
        # Supprimer les logs verbeux
        import logging
        logging.getLogger('kivy').setLevel(logging.ERROR)
        logging.getLogger('kivymd').setLevel(logging.ERROR)
        
        # Importer et lancer l'application
        from main import GesComApp
        
        print("   ✅ Application initialisée")
        print("   🎯 Interface utilisateur prête")
        print("   📊 Base de données optimisée")
        print("   🔧 Toutes les corrections appliquées")
        
        print("\n🎉 Lancement de GesComPro_LibTam...")
        print("=" * 45)
        
        # Créer et lancer l'application
        app = GesComApp()
        app.run()
        
    except Exception as e:
        print(f"\n❌ Erreur lors du lancement: {e}")
        
        # Diagnostic spécifique
        error_str = str(e).lower()
        
        if 'figurecanvaskivyagg' in error_str or 'resize_event' in error_str:
            print("\n🔧 ERREUR MATPLOTLIB DÉTECTÉE:")
            print("   ✅ Cette erreur a été corrigée!")
            print("   🔄 Relancez l'application - elle devrait fonctionner")
            print("   📊 Les graphiques sont maintenant en mode sécurisé")
        
        elif 'kivymd' in error_str:
            print("\n🔧 ERREUR KIVYMD:")
            print("   1. Vérifiez: pip show kivymd")
            print("   2. Réinstallez: pip install kivymd")
        
        elif 'database' in error_str or 'sqlite' in error_str:
            print("\n🔧 ERREUR BASE DE DONNÉES:")
            print("   1. Vérifiez les permissions du dossier data/")
            print("   2. Exécutez: python optimiser_db_automatique.py")
        
        else:
            print("\n🔧 DIAGNOSTIC:")
            print("   1. Toutes les optimisations ont été appliquées")
            print("   2. Vérifiez les dépendances: pip install -r requirements.txt")
            print("   3. Consultez les logs pour plus de détails")
        
        print(f"\n📋 INFORMATIONS TECHNIQUES:")
        print(f"   • Python: {sys.version}")
        print(f"   • Plateforme: {sys.platform}")
        print(f"   • Répertoire: {os.getcwd()}")
        
        import traceback
        print(f"\n🔍 TRACE DÉTAILLÉE:")
        traceback.print_exc()
        
        input("\nAppuyez sur Entrée pour quitter...")


if __name__ == "__main__":
    main()
