#!/usr/bin/env python3
"""
Test du formulaire client avec organisation en deux colonnes et navigation par tabulation
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_formulaire_deux_colonnes():
    """Test du formulaire avec deux colonnes et tabulation"""
    print("🧪 Test du formulaire client - Deux colonnes + Tabulation")
    print("=" * 65)
    
    try:
        # Importer les modules nécessaires
        from kivymd.app import MDApp
        from screens.clients_screen import ClientFormDialog
        from main import GesComApp
        
        print("✅ Modules importés")
        
        # Créer une app de test
        class TestApp(GesComApp):
            def build(self):
                return super().build()
        
        app = TestApp()
        print("✅ Application créée")
        
        # Données de test
        test_client_data = {
            'nom': 'Dupont',
            'prenom': '<PERSON>',
            'entreprise': 'SARL Dupont & Fils',
            'email': '<EMAIL>',
            'telephone': '01 23 45 67 89',
            'adresse': '123 Avenue des Champs-Élysées\nAppartement 4B',
            'ville': 'Paris',
            'code_postal': '75008',
            'pays': 'France'
        }
        
        print("✅ Données de test préparées")
        
        # Test 1: Création du dialog avec organisation en colonnes
        print("🔸 Test 1: Organisation en deux colonnes...")
        try:
            dialog = ClientFormDialog(client_data=test_client_data)
            print("  ✅ Dialog créé avec succès")
            
            # Vérifier que tous les champs existent
            required_fields = [
                'nom_field', 'prenom_field', 'entreprise_field',
                'email_field', 'telephone_field', 'adresse_field',
                'ville_field', 'code_postal_field', 'pays_field'
            ]
            
            for field_name in required_fields:
                assert hasattr(dialog, field_name), f"Champ {field_name} manquant"
                field = getattr(dialog, field_name)
                assert field is not None, f"Champ {field_name} est None"
            
            print("  ✅ Tous les champs présents")
            
        except Exception as e:
            print(f"  ❌ Erreur création dialog: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test 2: Vérification de l'ordre de tabulation
        print("🔸 Test 2: Navigation par tabulation...")
        try:
            # Vérifier que tab_order existe
            assert hasattr(dialog, 'tab_order'), "tab_order manquant"
            assert len(dialog.tab_order) == 9, f"tab_order devrait avoir 9 éléments, a {len(dialog.tab_order)}"
            
            # Vérifier l'ordre correct
            expected_order = [
                dialog.nom_field,
                dialog.prenom_field,
                dialog.entreprise_field,
                dialog.email_field,
                dialog.telephone_field,
                dialog.adresse_field,
                dialog.ville_field,
                dialog.code_postal_field,
                dialog.pays_field
            ]
            
            for i, expected_field in enumerate(expected_order):
                actual_field = dialog.tab_order[i]
                assert actual_field == expected_field, f"Ordre incorrect à l'index {i}"
            
            print("  ✅ Ordre de tabulation correct")
            
            # Vérifier les références de navigation
            for i, field in enumerate(dialog.tab_order):
                assert hasattr(field, '_next_field'), f"_next_field manquant pour le champ {i}"
                assert hasattr(field, '_prev_field'), f"_prev_field manquant pour le champ {i}"
                assert hasattr(field, '_field_index'), f"_field_index manquant pour le champ {i}"
                assert field._field_index == i, f"Index incorrect pour le champ {i}"
            
            print("  ✅ Références de navigation configurées")
            
        except Exception as e:
            print(f"  ❌ Erreur navigation tabulation: {e}")
            return False
        
        # Test 3: Vérification des valeurs pré-remplies
        print("🔸 Test 3: Pré-remplissage des champs...")
        try:
            assert dialog.nom_field.text == "Dupont"
            assert dialog.prenom_field.text == "Jean"
            assert dialog.entreprise_field.text == "SARL Dupont & Fils"
            assert dialog.email_field.text == "<EMAIL>"
            assert dialog.telephone_field.text == "01 23 45 67 89"
            assert dialog.ville_field.text == "Paris"
            assert dialog.code_postal_field.text == "75008"
            assert dialog.pays_field.text == "France"
            print("  ✅ Tous les champs pré-remplis correctement")
            
        except Exception as e:
            print(f"  ❌ Erreur pré-remplissage: {e}")
            return False
        
        # Test 4: Test avec nouveau client (champs vides)
        print("🔸 Test 4: Nouveau client (champs vides)...")
        try:
            new_dialog = ClientFormDialog()
            print("  ✅ Dialog nouveau client créé")
            
            # Vérifier que les champs sont vides (sauf pays)
            assert new_dialog.nom_field.text == ""
            assert new_dialog.prenom_field.text == ""
            assert new_dialog.entreprise_field.text == ""
            assert new_dialog.email_field.text == ""
            assert new_dialog.telephone_field.text == ""
            assert new_dialog.adresse_field.text == ""
            assert new_dialog.ville_field.text == ""
            assert new_dialog.code_postal_field.text == ""
            assert new_dialog.pays_field.text == "France"  # Valeur par défaut
            print("  ✅ Champs vides corrects (pays par défaut)")
            
        except Exception as e:
            print(f"  ❌ Erreur nouveau client: {e}")
            return False
        
        # Test 5: Vérification de la structure en colonnes
        print("🔸 Test 5: Structure en colonnes...")
        try:
            content = dialog.content_cls
            assert content is not None, "Content_cls manquant"
            
            # Vérifier que c'est un ScrollView
            from kivymd.uix.scrollview import MDScrollView
            assert isinstance(content, MDScrollView), f"Content n'est pas MDScrollView: {type(content)}"
            print("  ✅ ScrollView présent")
            
            # Vérifier la taille du dialog
            assert dialog.size_hint == [0.9, 0.8], f"Taille incorrecte: {dialog.size_hint}"
            print("  ✅ Taille du dialog correcte (90% x 80%)")
            
        except Exception as e:
            print(f"  ❌ Erreur structure: {e}")
            return False
        
        print("\n🎉 TOUS LES TESTS DU FORMULAIRE DEUX COLONNES RÉUSSIS!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Test du formulaire client - Deux colonnes + Tabulation")
    print("=" * 70)
    
    success = test_formulaire_deux_colonnes()
    
    if success:
        print("\n✅ LE FORMULAIRE DEUX COLONNES FONCTIONNE PARFAITEMENT!")
        print("🎯 Fonctionnalités validées:")
        print("   • Organisation en deux colonnes optimisée")
        print("   • Navigation par tabulation (Entrée pour passer au suivant)")
        print("   • Ordre logique: Nom→Prénom→Entreprise→Email→Téléphone→Adresse→Ville→Code→Pays")
        print("   • Pré-remplissage correct pour modification")
        print("   • Champs vides pour nouveau client")
        print("   • ScrollView fonctionnel")
        print("   • Taille adaptative (90% x 80%)")
        print("\n🎮 Utilisation:")
        print("   • Appuyer sur ENTRÉE pour passer au champ suivant")
        print("   • Cliquer directement sur un champ pour le sélectionner")
        print("   • Utiliser la molette pour faire défiler si nécessaire")
    else:
        print("\n❌ PROBLÈME AVEC LE FORMULAIRE DEUX COLONNES")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    print("\nAppuyez sur Entrée pour continuer...")
    input()