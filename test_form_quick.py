#!/usr/bin/env python3
"""
Test rapide du formulaire de catégorie
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from forms.category_form import CategoryFormDialog


class TestFormQuickApp(MDApp):
    """Test rapide du formulaire"""
    
    def build(self):
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        title = MDLabel(
            text="🧪 Test Rapide\nFormulaire Catégorie",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        test_btn = MDRaisedButton(
            text="📝 Ouvrir Formulaire",
            size_hint_y=None,
            height="60dp",
            on_release=self.open_form
        )
        
        self.result_label = MDLabel(
            text="Cliquez pour ouvrir le formulaire",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(test_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def open_form(self, *args):
        try:
            dialog = CategoryFormDialog(
                on_save_callback=self.on_save
            )
            dialog.open()
            
            self.result_label.text = "✅ Formulaire ouvert !\n\n" \
                                   "Vérifiez que vous voyez :\n" \
                                   "• Titre du formulaire\n" \
                                   "• Champ nom\n" \
                                   "• Champ description\n" \
                                   "• Boutons Annuler/Enregistrer"
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur: {str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def on_save(self, data):
        nom = data.get('nom', 'N/A')
        self.result_label.text = f"🎉 Catégorie sauvegardée !\n\nNom: {nom}"


def main():
    print("🧪 Test Rapide - Formulaire Catégorie")
    print("Objectif: Vérifier l'affichage du formulaire")
    
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestFormQuickApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()