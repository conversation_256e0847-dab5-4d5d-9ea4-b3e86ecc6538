#!/usr/bin/env python3
"""
Test pour vérifier que la scrollbar du formulaire client est désactivée
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_scrollbar_desactivee():
    """Test de désactivation de la scrollbar"""
    print("🧪 Test de désactivation de la scrollbar du formulaire client")
    print("=" * 65)
    
    try:
        # Importer les modules nécessaires
        from kivymd.app import MDApp
        from screens.clients_screen import ClientFormDialog
        from main import GesComApp
        
        print("✅ Modules importés")
        
        # Créer une app de test
        class TestApp(GesComApp):
            def build(self):
                return super().build()
        
        app = TestApp()
        print("✅ Application créée")
        
        # Données de test
        test_client_data = {
            'nom': 'Dupont',
            'prenom': 'Jean',
            'entreprise': 'SARL Test',
            'email': '<EMAIL>',
            'telephone': '0123456789',
            'adresse': '123 Rue Test',
            'ville': 'Paris',
            'code_postal': '75001',
            'pays': 'France'
        }
        
        print("✅ Données de test préparées")
        
        # Test 1: Création du dialog et vérification du ScrollView
        print("🔸 Test 1: Configuration du ScrollView...")
        try:
            dialog = ClientFormDialog(client_data=test_client_data)
            print("  ✅ Dialog créé avec succès")
            
            # Vérifier que le content_cls est un ScrollView
            scroll_view = dialog.content_cls
            assert scroll_view is not None, "ScrollView manquant"
            
            from kivymd.uix.scrollview import MDScrollView
            assert isinstance(scroll_view, MDScrollView), f"Content n'est pas MDScrollView: {type(scroll_view)}"
            print("  ✅ ScrollView présent")
            
            # Vérifier la configuration de la scrollbar
            assert hasattr(scroll_view, 'bar_width'), "Propriété bar_width manquante"
            assert scroll_view.bar_width == 0, f"bar_width devrait être 0, mais est {scroll_view.bar_width}"
            print("  ✅ bar_width = 0 (scrollbar invisible)")
            
            # Vérifier les autres propriétés de scroll
            assert scroll_view.do_scroll_x == False, "do_scroll_x devrait être False"
            assert scroll_view.do_scroll_y == True, "do_scroll_y devrait être True"
            print("  ✅ Configuration de scroll correcte (Y seulement)")
            
        except Exception as e:
            print(f"  ❌ Erreur configuration ScrollView: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test 2: Vérification que le défilement fonctionne toujours
        print("🔸 Test 2: Fonctionnalité de défilement...")
        try:
            # Vérifier que le ScrollView peut toujours défiler
            assert hasattr(scroll_view, 'scroll_y'), "Propriété scroll_y manquante"
            assert hasattr(scroll_view, 'scroll_x'), "Propriété scroll_x manquante"
            
            # Vérifier les limites de scroll
            initial_scroll_y = scroll_view.scroll_y
            print(f"  📏 Position initiale scroll_y: {initial_scroll_y}")
            
            # Le défilement devrait être possible même sans scrollbar visible
            assert 0 <= initial_scroll_y <= 1, f"scroll_y hors limites: {initial_scroll_y}"
            print("  ✅ Défilement fonctionnel (sans scrollbar visible)")
            
        except Exception as e:
            print(f"  ❌ Erreur fonctionnalité défilement: {e}")
            return False
        
        # Test 3: Vérification avec nouveau client
        print("🔸 Test 3: Nouveau client (scrollbar désactivée)...")
        try:
            new_dialog = ClientFormDialog()
            new_scroll_view = new_dialog.content_cls
            
            # Vérifier la même configuration pour nouveau client
            assert new_scroll_view.bar_width == 0, f"bar_width nouveau client: {new_scroll_view.bar_width}"
            assert new_scroll_view.do_scroll_y == True, "do_scroll_y devrait être True"
            print("  ✅ Scrollbar désactivée pour nouveau client aussi")
            
        except Exception as e:
            print(f"  ❌ Erreur nouveau client: {e}")
            return False
        
        # Test 4: Vérification de l'interface utilisateur
        print("🔸 Test 4: Interface utilisateur...")
        try:
            # Vérifier que tous les champs sont toujours accessibles
            required_fields = [
                'nom_field', 'prenom_field', 'entreprise_field',
                'email_field', 'telephone_field', 'adresse_field',
                'ville_field', 'code_postal_field', 'pays_field'
            ]
            
            for field_name in required_fields:
                assert hasattr(dialog, field_name), f"Champ {field_name} manquant"
                field = getattr(dialog, field_name)
                assert field is not None, f"Champ {field_name} est None"
            
            print("  ✅ Tous les champs accessibles")
            
            # Vérifier la taille du dialog
            assert dialog.size_hint == [0.9, 0.8], f"Taille dialog incorrecte: {dialog.size_hint}"
            print("  ✅ Taille du dialog correcte")
            
        except Exception as e:
            print(f"  ❌ Erreur interface utilisateur: {e}")
            return False
        
        print("\n🎉 TOUS LES TESTS DE SCROLLBAR DÉSACTIVÉE RÉUSSIS!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Test de désactivation de la scrollbar - GesComPro_LibTam")
    print("=" * 70)
    
    success = test_scrollbar_desactivee()
    
    if success:
        print("\n✅ LA SCROLLBAR EST BIEN DÉSACTIVÉE!")
        print("🎯 Configuration validée:")
        print("   • bar_width = 0 : Scrollbar invisible")
        print("   • do_scroll_y = True : Défilement vertical fonctionnel")
        print("   • do_scroll_x = False : Pas de défilement horizontal")
        print("   • Interface propre : Pas de barre visible")
        print("   • Fonctionnalité préservée : Défilement à la molette")
        print("\n🎮 Utilisation:")
        print("   • Molette de souris : Défilement vertical")
        print("   • Glisser-déposer : Défilement tactile")
        print("   • Navigation clavier : Focus automatique avec scroll")
        print("   • Pas de scrollbar visible : Interface épurée")
    else:
        print("\n❌ PROBLÈME AVEC LA DÉSACTIVATION DE LA SCROLLBAR")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    print("\nAppuyez sur Entrée pour continuer...")
    input()