#!/usr/bin/env python3
"""
Test du nouveau formulaire de catégorie basé sur la table
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from new_category_form import CategoryFormDialog


class TestNouveauFormulaireApp(MDApp):
    """Application de test pour le nouveau formulaire"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Nouveau Formulaire Catégorie"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construction de l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        # Titre
        title = MDLabel(
            text="🆕 Nouveau Formulaire Catégorie",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Instructions
        instructions = MDLabel(
            text="NOUVEAU FORMULAIRE BASÉ SUR LA TABLE CATEGORIES :\n\n"
                 "✅ Structure exacte de la table categories\n"
                 "✅ Champs: id, nom (UNIQUE), description, date_creation\n"
                 "✅ Validation en temps réel du nom\n"
                 "✅ Vérification d'unicité automatique\n"
                 "✅ Mode outlined pour meilleure visibilité\n"
                 "✅ Informations de base de données en modification\n\n"
                 "TESTEZ LES FONCTIONNALITÉS :\n"
                 "📝 Saisie du nom (obligatoire et unique)\n"
                 "📄 Description optionnelle multiline\n"
                 "🔍 Validation automatique\n"
                 "💾 Sauvegarde en base de données",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="280dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="140dp"
        )
        
        new_btn = MDRaisedButton(
            text="🆕 Nouveau Formulaire",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_new_form
        )
        
        edit_btn = MDRaisedButton(
            text="✏️ Modification avec Données",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_edit_form
        )
        
        buttons_layout.add_widget(new_btn)
        buttons_layout.add_widget(edit_btn)
        
        # Résultats
        self.result_label = MDLabel(
            text="Testez le nouveau formulaire basé sur la structure de la table !",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(instructions)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_new_form(self, *args):
        """Tester le nouveau formulaire de création"""
        self.result_label.text = "🆕 NOUVEAU FORMULAIRE OUVERT\n\n" \
                                "VÉRIFIEZ LA NOUVELLE STRUCTURE :\n" \
                                "📂 Titre 'Formulaire Catégorie'\n" \
                                "📝 Champ nom avec validation temps réel\n" \
                                "📄 Champ description multiline\n" \
                                "🔍 Mode 'outlined' pour visibilité\n" \
                                "💾 Boutons Annuler/Enregistrer\n\n" \
                                "Testez la validation en saisissant\n" \
                                "un nom existant ou trop court !"
        
        dialog = CategoryFormDialog(
            on_save_callback=self.on_save_callback
        )
        dialog.open()
        print("🆕 Nouveau formulaire basé sur la table ouvert")
    
    def test_edit_form(self, *args):
        """Tester le formulaire de modification"""
        self.result_label.text = "✏️ MODIFICATION AVEC DONNÉES\n\n" \
                                "DONNÉES DE TEST CHARGÉES :\n" \
                                "🆔 ID: 1 (PRIMARY KEY)\n" \
                                "📝 Nom: 'Électronique Nouvelle'\n" \
                                "📄 Description: 'Test nouveau formulaire'\n" \
                                "📅 Date: 2024-01-15\n" \
                                "📦 Produits: 5\n\n" \
                                "Vérifiez que toutes les informations\n" \
                                "de la base de données s'affichent !"
        
        # Données de test correspondant à la structure de la table
        test_data = {
            'id': 1,  # PRIMARY KEY AUTOINCREMENT
            'nom': 'Électronique Nouvelle',  # TEXT NOT NULL UNIQUE
            'description': 'Test du nouveau formulaire basé sur la structure exacte de la table categories',  # TEXT
            'date_creation': '2024-01-15T10:30:00',  # TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            'products_count': 5  # Relation avec table produits
        }
        
        dialog = CategoryFormDialog(
            category_data=test_data,
            on_save_callback=self.on_save_callback
        )
        dialog.open()
        print("✏️ Formulaire de modification avec données de test ouvert")
    
    def on_save_callback(self, category_data):
        """Callback de sauvegarde"""
        nom = category_data.get('nom', 'Sans nom')
        description = category_data.get('description', 'Aucune')
        category_id = category_data.get('id', 'Nouveau')
        
        self.result_label.text = f"🎉 NOUVEAU FORMULAIRE FONCTIONNE !\n\n" \
                                f"DONNÉES SAUVEGARDÉES :\n" \
                                f"🆔 ID: {category_id}\n" \
                                f"📝 Nom: {nom}\n" \
                                f"📄 Description: {description[:40]}{'...' if len(description) > 40 else ''}\n\n" \
                                f"✅ Structure de table respectée !\n" \
                                f"✅ Validation fonctionnelle !\n" \
                                f"✅ Sauvegarde réussie !\n" \
                                f"✅ Nouveau formulaire opérationnel !"
        
        print("🎉 SUCCÈS - Nouveau formulaire fonctionnel !")
        print(f"  - ID: {category_id}")
        print(f"  - Nom: {nom}")
        print(f"  - Description: {description}")
        print("✅ Le nouveau formulaire basé sur la table fonctionne parfaitement")


def main():
    """Fonction principale"""
    print("🆕 Test - Nouveau Formulaire Catégorie")
    print("=" * 60)
    print("NOUVEAU FORMULAIRE CRÉÉ :")
    print("✅ Basé sur la structure exacte de la table categories")
    print("✅ Champs correspondant aux colonnes de la table")
    print("✅ Validation en temps réel")
    print("✅ Mode outlined pour meilleure visibilité")
    print("✅ Gestion des contraintes de la table (UNIQUE, NOT NULL)")
    print()
    print("STRUCTURE DE LA TABLE CATEGORIES :")
    print("- id INTEGER PRIMARY KEY AUTOINCREMENT")
    print("- nom TEXT NOT NULL UNIQUE")
    print("- description TEXT")
    print("- date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    print()
    print("FONCTIONNALITÉS DU NOUVEAU FORMULAIRE :")
    print("1. Validation automatique du nom (obligatoire, unique)")
    print("2. Champ description optionnel multiline")
    print("3. Affichage des informations de base en modification")
    print("4. Couleurs et bordures optimisées pour la visibilité")
    print("5. Sauvegarde directe dans la table categories")
    print("=" * 60)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestNouveauFormulaireApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()