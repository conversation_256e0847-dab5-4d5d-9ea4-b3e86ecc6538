#!/usr/bin/env python3
"""
Test spécifique pour la modification de client
"""

import os
import sys
import tempfile

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_client_modification():
    """Test complet de la modification de client"""
    print("🧪 Test de la modification de client")
    print("=" * 50)
    
    try:
        # Importer les modules
        from database.db_manager import DatabaseManager
        from main import GesComApp
        
        # Créer une base de données temporaire
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
            test_db_path = tmp_file.name
        
        # Initialiser la base de données
        db_manager = DatabaseManager(test_db_path)
        if not db_manager.connect():
            print("❌ Erreur de connexion à la base de données")
            return False
        
        db_manager.initialize_database()
        print("✅ Base de données initialisée")
        
        # Ajouter un client de test
        client_id = db_manager.execute_insert("""
            INSERT INTO clients (nom, prenom, entreprise, email, telephone, adresse, ville, code_postal, pays)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, ('Dupont', 'Jean', 'SARL Dupont', '<EMAIL>', '0123456789', '123 Rue Test', 'Paris', '75001', 'France'))
        
        if not client_id:
            print("❌ Erreur lors de l'ajout du client test")
            return False
        
        print(f"✅ Client test ajouté (ID: {client_id})")
        
        # Récupérer le client
        clients = db_manager.execute_query("SELECT * FROM clients WHERE id = ?", (client_id,))
        if not clients:
            print("❌ Client non trouvé")
            return False
        
        client_data = clients[0]
        print(f"✅ Client récupéré: {dict(client_data)}")
        
        # Créer l'application pour les tests
        print("🔸 Création de l'application de test...")
        
        class TestApp(GesComApp):
            def __init__(self, **kwargs):
                super().__init__(**kwargs)
                self.db_manager = db_manager  # Utiliser notre DB de test
        
        app = TestApp()
        
        # Tester la création de l'écran clients
        print("🔸 Test de création de l'écran clients...")
        from screens.clients_screen import ClientsScreen
        clients_screen = ClientsScreen()
        print("✅ Écran clients créé")
        
        # Simuler l'initialisation de l'app
        app._running_app = app
        
        # Tester la méthode edit_client
        print("🔸 Test de la méthode edit_client...")
        try:
            clients_screen.edit_client(client_data)
            print("✅ Méthode edit_client exécutée sans erreur")
        except Exception as e:
            print(f"❌ Erreur dans edit_client: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n🎉 TOUS LES TESTS DE MODIFICATION RÉUSSIS!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Nettoyer
        if 'db_manager' in locals():
            db_manager.disconnect()
        try:
            os.unlink(test_db_path)
            print("🧹 Fichier de test supprimé")
        except:
            pass

if __name__ == "__main__":
    print("🚀 Test de la modification de client - GesComPro_LibTam")
    print("=" * 60)
    
    success = test_client_modification()
    
    if success:
        print("\n✅ LA MODIFICATION DE CLIENT FONCTIONNE CORRECTEMENT!")
        print("🚀 Vous pouvez maintenant modifier les clients dans l'application!")
    else:
        print("\n❌ PROBLÈME DANS LA MODIFICATION DE CLIENT")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    print("\nAppuyez sur Entrée pour continuer...")
    input()