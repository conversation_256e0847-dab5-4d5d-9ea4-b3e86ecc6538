"""
Test de la fonctionnalité de recherche client dans le formulaire de vente
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

# Configuration Kivy pour éviter les erreurs
os.environ['KIVY_GL_BACKEND'] = 'gl'

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.boxlayout import MDBoxLayout
from forms.sales_form import SalesFormDialog


class TestClientSearchApp(MDApp):
    """Application de test pour la recherche client"""
    
    def build(self):
        """Construire l'interface de test"""
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="20dp",
            pos_hint={'center_x': 0.5, 'center_y': 0.5},
            size_hint=(0.8, 0.6)
        )
        
        # Bouton pour ouvrir le formulaire de vente (nouveau)
        new_sale_btn = MDRaisedButton(
            text="🛒 Nouvelle Vente (Test Recherche Client)",
            size_hint_y=None,
            height="60dp",
            on_release=self.open_new_sale_form
        )
        
        # Bouton pour ouvrir le formulaire de vente (modification)
        edit_sale_btn = MDRaisedButton(
            text="✏️ Modifier Vente (Test Recherche Client)",
            size_hint_y=None,
            height="60dp",
            on_release=self.open_edit_sale_form
        )
        
        layout.add_widget(new_sale_btn)
        layout.add_widget(edit_sale_btn)
        
        screen.add_widget(layout)
        return screen
    
    def open_new_sale_form(self, *args):
        """Ouvrir le formulaire de nouvelle vente"""
        print("🧪 Test: Ouverture du formulaire de nouvelle vente")
        
        def on_save(sale_data):
            print(f"✅ Vente sauvegardée: {sale_data}")
        
        dialog = SalesFormDialog(
            sale_data=None,
            on_save_callback=on_save
        )
        dialog.open()
        
        # Test automatique de la fonctionnalité de recherche
        self.test_search_functionality(dialog)
    
    def open_edit_sale_form(self, *args):
        """Ouvrir le formulaire de modification de vente"""
        print("🧪 Test: Ouverture du formulaire de modification de vente")
        
        # Données de test pour une vente existante
        test_sale_data = {
            'id': 1,
            'numero_facture': 'FAC-2024-001',
            'client_id': 1,
            'date_vente': '2024-01-15T10:30:00',
            'montant_ht': 100.00,
            'montant_ttc': 120.00,
            'mode_paiement': '💰 Espèces',
            'notes': 'Vente de test'
        }
        
        def on_save(sale_data):
            print(f"✅ Vente modifiée: {sale_data}")
        
        dialog = SalesFormDialog(
            sale_data=test_sale_data,
            on_save_callback=on_save
        )
        dialog.open()
        
        # Test automatique de la fonctionnalité de recherche
        self.test_search_functionality(dialog)
    
    def test_search_functionality(self, dialog):
        """Tester la fonctionnalité de recherche"""
        print("\n🔍 Test de la fonctionnalité de recherche client:")
        
        # Vérifier que le champ de recherche existe
        if hasattr(dialog, 'client_search_field'):
            print("✅ Champ de recherche client créé")
            
            # Vérifier que la liste filtrée existe
            if hasattr(dialog, 'filtered_clients_list'):
                print("✅ Liste filtrée initialisée")
                print(f"   Nombre de clients total: {len(dialog.clients_list)}")
                print(f"   Nombre de clients filtrés: {len(dialog.filtered_clients_list)}")
                
                # Tester la méthode de filtrage
                print("\n🧪 Test du filtrage:")
                
                # Test 1: Recherche par nom
                print("   Test 1: Recherche 'Dupont'")
                dialog.on_client_search_text_change(None, "Dupont")
                dupont_results = len(dialog.filtered_clients_list)
                print(f"   Résultats: {dupont_results} client(s)")
                
                # Test 2: Recherche par prénom
                print("   Test 2: Recherche 'Jean'")
                dialog.on_client_search_text_change(None, "Jean")
                jean_results = len(dialog.filtered_clients_list)
                print(f"   Résultats: {jean_results} client(s)")
                
                # Test 3: Recherche par entreprise
                print("   Test 3: Recherche 'Entreprise'")
                dialog.on_client_search_text_change(None, "Entreprise")
                entreprise_results = len(dialog.filtered_clients_list)
                print(f"   Résultats: {entreprise_results} client(s)")
                
                # Test 4: Recherche vide (tous les clients)
                print("   Test 4: Recherche vide")
                dialog.on_client_search_text_change(None, "")
                all_results = len(dialog.filtered_clients_list)
                print(f"   Résultats: {all_results} client(s)")
                
                # Test 5: Recherche inexistante
                print("   Test 5: Recherche 'ClientInexistant'")
                dialog.on_client_search_text_change(None, "ClientInexistant")
                no_results = len(dialog.filtered_clients_list)
                print(f"   Résultats: {no_results} client(s)")
                
                # Vérifications
                if dupont_results > 0:
                    print("✅ Recherche par nom fonctionne")
                if jean_results > 0:
                    print("✅ Recherche par prénom fonctionne")
                if entreprise_results > 0:
                    print("✅ Recherche par entreprise fonctionne")
                if all_results == len(dialog.clients_list):
                    print("✅ Réinitialisation de la recherche fonctionne")
                if no_results == 0:
                    print("✅ Gestion des recherches sans résultat fonctionne")
                
                print("\n✅ Tests de la fonctionnalité de recherche terminés")
                
            else:
                print("❌ Liste filtrée non initialisée")
        else:
            print("❌ Champ de recherche client non trouvé")


def main():
    """Fonction principale de test"""
    print("🚀 Lancement du test de recherche client")
    print("=" * 50)
    
    try:
        app = TestClientSearchApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()