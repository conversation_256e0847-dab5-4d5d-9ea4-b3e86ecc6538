#!/usr/bin/env python3
"""
Vérifier la structure de la table categories
"""

from database.db_manager import DatabaseManager

def check_categories_table():
    """Vérifier la structure de la table categories"""
    db_manager = DatabaseManager()
    
    try:
        if not db_manager.connect():
            print("❌ Impossible de se connecter à la base de données")
            return
        
        # Vérifier la structure de la table categories
        print("🔍 Structure de la table categories :")
        structure = db_manager.execute_query("PRAGMA table_info(categories)")
        
        if structure:
            print("Colonnes trouvées :")
            for col in structure:
                print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        else:
            print("❌ Table categories non trouvée")
            return
        
        # Vérifier quelques catégories
        print("\n📋 Quelques catégories dans la table :")
        categories = db_manager.execute_query("SELECT * FROM categories LIMIT 5")
        
        if categories:
            for category in categories:
                print(f"  - ID: {category.get('id')}, Nom: {category.get('nom', 'N/A')}")
        else:
            print("❌ Aucune catégorie trouvée")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        db_manager.close()

if __name__ == '__main__':
    check_categories_table()