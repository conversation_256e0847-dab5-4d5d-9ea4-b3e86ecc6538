"""
Script pour corriger les problèmes de matplotlib avec Kivy
"""

import os
import sys
import subprocess
from pathlib import Path

def check_matplotlib_installation():
    """Vérifier l'installation de matplotlib"""
    print("🔍 VÉRIFICATION DE MATPLOTLIB")
    print("-" * 35)
    
    try:
        import matplotlib
        print(f"✅ Matplotlib {matplotlib.__version__} installé")
        
        # Vérifier le backend
        import matplotlib.pyplot as plt
        backend = plt.get_backend()
        print(f"📊 Backend actuel: {backend}")
        
        return True
    except ImportError:
        print("❌ Matplotlib non installé")
        return False


def check_kivy_garden():
    """Vérifier kivy-garden"""
    print("\n🌱 VÉRIFICATION DE KIVY-GARDEN")
    print("-" * 35)
    
    try:
        import kivy.garden
        print("✅ Kivy-garden installé")
        return True
    except ImportError:
        print("❌ Kivy-garden non installé")
        return False


def check_garden_matplotlib():
    """Vérifier garden.matplotlib"""
    print("\n📈 VÉRIFICATION DE GARDEN.MATPLOTLIB")
    print("-" * 40)
    
    try:
        from kivy.garden.matplotlib.backend_kivyagg import FigureCanvasKivyAgg
        print("✅ Garden.matplotlib installé et fonctionnel")
        return True
    except ImportError as e:
        print(f"❌ Garden.matplotlib non disponible: {e}")
        return False
    except Exception as e:
        print(f"⚠️ Erreur avec garden.matplotlib: {e}")
        return False


def install_dependencies():
    """Installer les dépendances manquantes"""
    print("\n🔧 INSTALLATION DES DÉPENDANCES")
    print("-" * 35)
    
    commands = [
        ("pip install matplotlib", "Installation de matplotlib"),
        ("pip install kivy-garden", "Installation de kivy-garden"),
        ("garden install matplotlib", "Installation de garden.matplotlib")
    ]
    
    for cmd, description in commands:
        try:
            print(f"🔄 {description}...")
            result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"✅ {description} réussie")
            else:
                print(f"⚠️ {description} - Avertissement:")
                if result.stderr:
                    print(f"   {result.stderr.strip()}")
                    
        except subprocess.TimeoutExpired:
            print(f"⏰ {description} - Timeout")
        except Exception as e:
            print(f"❌ {description} - Erreur: {e}")


def configure_matplotlib():
    """Configurer matplotlib pour Kivy"""
    print("\n⚙️ CONFIGURATION DE MATPLOTLIB")
    print("-" * 35)
    
    try:
        import matplotlib
        matplotlib.use('Agg')  # Backend non-interactif
        print("✅ Backend Agg configuré")
        
        # Créer un fichier de configuration matplotlib
        config_dir = Path.home() / '.matplotlib'
        config_dir.mkdir(exist_ok=True)
        
        config_file = config_dir / 'matplotlibrc'
        config_content = """
# Configuration matplotlib pour Kivy
backend: Agg
figure.facecolor: white
figure.edgecolor: white
savefig.facecolor: white
savefig.edgecolor: white
"""
        
        with open(config_file, 'w') as f:
            f.write(config_content.strip())
        
        print(f"✅ Configuration sauvegardée: {config_file}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur de configuration: {e}")
        return False


def test_matplotlib_kivy():
    """Tester matplotlib avec Kivy"""
    print("\n🧪 TEST DE MATPLOTLIB AVEC KIVY")
    print("-" * 35)
    
    try:
        # Test basique de matplotlib
        import matplotlib
        matplotlib.use('Agg')
        import matplotlib.pyplot as plt
        
        # Créer un graphique simple
        fig, ax = plt.subplots(figsize=(6, 4))
        ax.plot([1, 2, 3, 4], [1, 4, 2, 3])
        ax.set_title('Test Matplotlib')
        
        print("✅ Création de graphique matplotlib réussie")
        
        # Test avec FigureCanvasKivyAgg
        try:
            from kivy.garden.matplotlib.backend_kivyagg import FigureCanvasKivyAgg
            
            # Créer le widget sans l'afficher
            canvas = FigureCanvasKivyAgg(fig)
            print("✅ FigureCanvasKivyAgg créé avec succès")
            
            plt.close(fig)
            return True
            
        except Exception as canvas_error:
            print(f"⚠️ Problème avec FigureCanvasKivyAgg: {canvas_error}")
            plt.close(fig)
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False


def create_fallback_solution():
    """Créer une solution de fallback"""
    print("\n🔄 CRÉATION DE SOLUTION DE FALLBACK")
    print("-" * 40)
    
    fallback_code = '''
"""
Module de fallback pour les graphiques
"""

def create_simple_chart_widget(data, title="Graphique"):
    """Créer un widget graphique simple sans matplotlib"""
    from kivymd.uix.card import MDCard
    from kivymd.uix.boxlayout import MDBoxLayout
    from kivymd.uix.label import MDLabel
    from kivymd.uix.scrollview import MDScrollView
    
    card = MDCard(elevation=2, padding="16dp", size_hint_y=None, height="300dp")
    layout = MDBoxLayout(orientation='vertical', spacing="8dp")
    
    # Titre
    title_label = MDLabel(
        text=title,
        font_style="H6",
        theme_text_color="Primary",
        size_hint_y=None,
        height="32dp"
    )
    layout.add_widget(title_label)
    
    # Données en mode texte
    scroll = MDScrollView()
    data_layout = MDBoxLayout(orientation='vertical', spacing="4dp", adaptive_height=True)
    
    for item in data:
        item_label = MDLabel(
            text=str(item),
            size_hint_y=None,
            height="24dp"
        )
        data_layout.add_widget(item_label)
    
    scroll.add_widget(data_layout)
    layout.add_widget(scroll)
    
    card.add_widget(layout)
    return card
'''
    
    try:
        with open('utils/chart_fallback.py', 'w', encoding='utf-8') as f:
            f.write(fallback_code)
        print("✅ Solution de fallback créée: utils/chart_fallback.py")
        return True
    except Exception as e:
        print(f"❌ Erreur création fallback: {e}")
        return False


def main():
    """Fonction principale"""
    print("🔧 CORRECTION DES PROBLÈMES MATPLOTLIB")
    print("=" * 45)
    
    # Vérifications
    matplotlib_ok = check_matplotlib_installation()
    garden_ok = check_kivy_garden()
    garden_matplotlib_ok = check_garden_matplotlib()
    
    # Installation si nécessaire
    if not matplotlib_ok or not garden_ok or not garden_matplotlib_ok:
        print("\n🚀 INSTALLATION DES DÉPENDANCES MANQUANTES")
        install_dependencies()
        
        # Re-vérification
        print("\n🔍 RE-VÉRIFICATION APRÈS INSTALLATION")
        matplotlib_ok = check_matplotlib_installation()
        garden_matplotlib_ok = check_garden_matplotlib()
    
    # Configuration
    configure_matplotlib()
    
    # Test
    test_success = test_matplotlib_kivy()
    
    # Solution de fallback
    create_fallback_solution()
    
    # Résumé
    print("\n📋 RÉSUMÉ")
    print("=" * 15)
    
    if test_success:
        print("✅ Matplotlib fonctionne correctement avec Kivy")
        print("🎯 Les graphiques seront affichés normalement")
    else:
        print("⚠️ Problèmes persistants avec matplotlib")
        print("🔄 L'application utilisera le mode fallback (graphiques texte)")
        print("📝 Les fonctionnalités principales restent disponibles")
    
    print("\n🚀 RECOMMANDATIONS:")
    print("   • Utilisez 'python launch_optimized_db.py' pour lancer l'app")
    print("   • Les rapports fonctionneront même sans graphiques matplotlib")
    print("   • Les données sont toujours accessibles en mode texte")


if __name__ == "__main__":
    main()
