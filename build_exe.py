"""
Script pour créer l'exécutable Windows de GesComPro
"""

import os
import sys
import subprocess
from pathlib import Path

def install_pyinstaller():
    """Installer PyInstaller si nécessaire"""
    try:
        import PyInstaller
        print("PyInstaller est déjà installé")
    except ImportError:
        print("Installation de PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("PyInstaller installé avec succès")

def create_spec_file():
    """Créer le fichier .spec pour PyInstaller"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('data', 'data'),
        ('screens', 'screens'),
        ('database', 'database'),
        ('utils', 'utils'),
    ],
    hiddenimports=[
        'kivymd',
        'kivymd.app',
        'kivymd.uix.boxlayout',
        'kivymd.uix.screen',
        'kivymd.uix.button',
        'kivymd.uix.label',
        'kivymd.uix.textfield',
        'kivymd.uix.card',
        'kivymd.uix.dialog',
        'kivymd.uix.scrollview',
        'kivymd.uix.gridlayout',
        'kivymd.uix.navigationdrawer',
        'kivymd.uix.toolbar',
        'kivymd.uix.selectioncontrol',
        'kivymd.uix.menu',
        'kivymd.uix.tab',
        'kivymd.uix.list',
        'kivymd.uix.floatlayout',
        'matplotlib.backends.backend_agg',
        'matplotlib.figure',
        'matplotlib.pyplot',
        'matplotlib.dates',
        'sqlite3',
        'threading',
        'datetime',
        'json',
        'csv',
        'pathlib',
        'kivy.garden.matplotlib',
        'kivy.garden.matplotlib.backend_kivyagg'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GesComPro',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico'  # Optionnel : ajouter une icône
)
'''
    
    with open('gescompro.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content.strip())
    
    print("Fichier gescompro.spec créé")

def build_executable():
    """Construire l'exécutable"""
    print("Construction de l'exécutable...")
    
    # Commande PyInstaller
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name=GesComPro",
        "--add-data=screens;screens",
        "--add-data=database;database", 
        "--add-data=utils;utils",
        "--hidden-import=kivymd",
        "--hidden-import=kivymd.app",
        "--hidden-import=kivymd.uix.boxlayout",
        "--hidden-import=kivymd.uix.screen",
        "--hidden-import=kivymd.uix.button",
        "--hidden-import=kivymd.uix.label",
        "--hidden-import=kivymd.uix.textfield",
        "--hidden-import=kivymd.uix.card",
        "--hidden-import=kivymd.uix.dialog",
        "--hidden-import=kivymd.uix.scrollview",
        "--hidden-import=kivymd.uix.gridlayout",
        "--hidden-import=kivymd.uix.navigationdrawer",
        "--hidden-import=kivymd.uix.toolbar",
        "--hidden-import=kivymd.uix.selectioncontrol",
        "--hidden-import=kivymd.uix.menu",
        "--hidden-import=kivymd.uix.tab",
        "--hidden-import=kivymd.uix.list",
        "--hidden-import=kivymd.uix.floatlayout",
        "--hidden-import=matplotlib.backends.backend_agg",
        "--hidden-import=kivy.garden.matplotlib",
        "--hidden-import=kivy.garden.matplotlib.backend_kivyagg",
        "main.py"
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ Exécutable créé avec succès!")
        print("📁 Vous pouvez trouver GesComPro.exe dans le dossier 'dist'")
        
        # Vérifier si l'exécutable existe
        exe_path = Path("dist/GesComPro.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"📊 Taille de l'exécutable: {size_mb:.1f} MB")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de la construction: {e}")
        return False
    
    return True

def create_installer_script():
    """Créer un script d'installation simple"""
    installer_content = '''
@echo off
echo Installation de GesComPro...

REM Créer le dossier d'installation
if not exist "C:\\Program Files\\GesComPro" mkdir "C:\\Program Files\\GesComPro"

REM Copier l'exécutable
copy "GesComPro.exe" "C:\\Program Files\\GesComPro\\"

REM Créer un raccourci sur le bureau
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\GesComPro.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "C:\\Program Files\\GesComPro\\GesComPro.exe" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs
del CreateShortcut.vbs

echo Installation terminée!
echo Vous pouvez maintenant lancer GesComPro depuis le raccourci sur le bureau.
pause
'''
    
    with open('dist/installer.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content.strip())
    
    print("📦 Script d'installation créé: dist/installer.bat")

def main():
    """Fonction principale"""
    print("🚀 Construction de l'exécutable GesComPro")
    print("=" * 50)
    
    # Vérifier que nous sommes dans le bon répertoire
    if not Path("main.py").exists():
        print("❌ Erreur: main.py non trouvé. Assurez-vous d'être dans le bon répertoire.")
        return
    
    # Installer PyInstaller
    install_pyinstaller()
    
    # Construire l'exécutable
    if build_executable():
        # Créer le script d'installation
        create_installer_script()
        
        print("\n🎉 Construction terminée avec succès!")
        print("\n📋 Instructions:")
        print("1. L'exécutable se trouve dans le dossier 'dist'")
        print("2. Vous pouvez utiliser 'installer.bat' pour installer l'application")
        print("3. Ou simplement copier GesComPro.exe où vous voulez")
        
        print("\n⚠️  Note importante:")
        print("- La première exécution peut être lente (extraction des fichiers)")
        print("- Windows Defender peut signaler l'exécutable comme suspect (faux positif)")
        print("- Pour distribuer, vous pouvez créer un installateur avec NSIS ou Inno Setup")
    
    else:
        print("❌ Échec de la construction")

if __name__ == "__main__":
    main()
'''