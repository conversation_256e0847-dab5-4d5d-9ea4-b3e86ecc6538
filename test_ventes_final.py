#!/usr/bin/env python3
"""
Test final du système de ventes - Interface complète
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from database.db_manager import DatabaseManager

class TestVentesFinalApp(MDApp):
    def build(self):
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Initialiser la base de données
        self.db_manager = DatabaseManager()
        
        screen = MDScreen()
        layout = MDBoxLayout(
            orientation='vertical', 
            padding="20dp", 
            spacing="20dp",
            pos_hint={'center_x': 0.5, 'center_y': 0.5}
        )
        
        # Titre
        title = MDLabel(
            text="🛒 Système de Ventes - ACTIVÉ !",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        # Informations
        info_text = """
✅ Fonctionnalités Activées:
• Création de nouvelles ventes
• Sélection de clients
• Ajout de produits au panier
• Calcul automatique des totaux
• Gestion des stocks
• Affichage en Dirham (DH)
• Statuts de ventes (En cours, Payée, Annulée)
• Détails des ventes
• Recherche et filtrage
        """
        
        info_label = MDLabel(
            text=info_text.strip(),
            theme_text_color="Primary",
            size_hint_y=None,
            height="200dp",
            halign="center"
        )
        
        # Bouton pour ouvrir l'écran des ventes
        open_sales_btn = MDRaisedButton(
            text="🚀 Ouvrir Écran Ventes",
            size_hint=(None, None),
            size=("300dp", "60dp"),
            pos_hint={'center_x': 0.5},
            on_release=self.open_sales_screen
        )
        
        # Statistiques
        self.stats_label = MDLabel(
            text="Chargement des statistiques...",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        layout.add_widget(title)
        layout.add_widget(info_label)
        layout.add_widget(open_sales_btn)
        layout.add_widget(self.stats_label)
        
        screen.add_widget(layout)
        
        # Charger les statistiques
        self.load_stats()
        
        return screen
    
    def load_stats(self):
        """Charger les statistiques de ventes"""
        try:
            from database.db_manager import get_sales_statistics, get_all_sales, get_all_clients, get_all_products
            
            # Récupérer les données
            stats = get_sales_statistics(self.db_manager)
            sales = get_all_sales(self.db_manager)
            clients = get_all_clients(self.db_manager)
            products = get_all_products(self.db_manager)
            
            # Formater les statistiques
            nb_ventes = stats.get('nombre_ventes', 0)
            ca_total = stats.get('chiffre_affaires', 0)
            nb_clients = len(clients)
            nb_produits = len(products)
            
            from utils.helpers import format_currency
            
            stats_text = f"""
📊 Statistiques:
• Ventes: {nb_ventes}
• Chiffre d'affaires: {format_currency(ca_total)}
• Clients: {nb_clients}
• Produits: {nb_produits}
            """.strip()
            
            self.stats_label.text = stats_text
            
        except Exception as e:
            self.stats_label.text = f"❌ Erreur stats: {str(e)}"
            print(f"Erreur stats: {e}")
    
    def open_sales_screen(self, *args):
        """Ouvrir l'écran des ventes"""
        try:
            print("🚀 Ouverture de l'écran des ventes...")
            
            # Import de l'écran
            from screens.sales_screen import SalesScreen
            
            # Créer l'écran
            sales_screen = SalesScreen()
            
            # Changer vers cet écran
            self.root.clear_widgets()
            self.root.add_widget(sales_screen)
            
            print("✅ Écran ventes ouvert avec succès")
            
        except Exception as e:
            print(f"❌ Erreur ouverture écran: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    print("🚀 SYSTÈME DE VENTES - ACTIVÉ !")
    print("=" * 50)
    print("✅ Fonctionnalités disponibles:")
    print("   • Création de ventes")
    print("   • Gestion des clients")
    print("   • Gestion des produits")
    print("   • Calcul automatique des totaux")
    print("   • Gestion des stocks")
    print("   • Affichage en Dirham")
    print("   • Statuts de ventes")
    print("   • Recherche et filtrage")
    print("=" * 50)
    
    try:
        app = TestVentesFinalApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()