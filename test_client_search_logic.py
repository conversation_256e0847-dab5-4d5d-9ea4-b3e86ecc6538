"""
Test de la logique de recherche client (sans interface graphique)
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))


class MockSalesFormDialog:
    """Mock du formulaire de vente pour tester la logique de recherche"""
    
    def __init__(self):
        # Données de test pour les clients
        self.clients_list = [
            {'id': 1, 'nom': 'Du<PERSON>', 'prenom': '<PERSON>', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0123456789'},
            {'id': 2, 'nom': '<PERSON>', 'prenom': 'Marie', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0987654321'},
            {'id': 3, 'nom': '', 'prenom': '', 'entreprise': 'Entreprise Test SARL', 'email': '<EMAIL>', 'telephone': '0555123456'},
            {'id': 4, 'nom': 'Durand', 'prenom': '<PERSON>', 'entreprise': 'Du<PERSON> & Fils', 'email': '<EMAIL>', 'telephone': '0666777888'},
            {'id': 5, 'nom': 'Lefebvre', 'prenom': 'Sophie', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0111222333'}
        ]
        
        # Initialiser la liste filtrée
        self.filtered_clients_list = self.clients_list.copy()
    
    def on_client_search_text_change(self, instance, text):
        """Gérer les changements dans le champ de recherche client"""
        search_text = text.lower().strip()
        
        if not search_text:
            # Si le champ est vide, afficher tous les clients
            self.filtered_clients_list = self.clients_list.copy()
        else:
            # Filtrer les clients selon le texte de recherche
            self.filtered_clients_list = []
            for client in self.clients_list:
                # Rechercher dans nom, prénom, entreprise, email et téléphone
                searchable_fields = [
                    client.get('nom', '').lower(),
                    client.get('prenom', '').lower(),
                    client.get('entreprise', '').lower(),
                    client.get('email', '').lower(),
                    client.get('telephone', '').lower()
                ]
                
                # Vérifier si le texte de recherche est présent dans l'un des champs
                if any(search_text in field for field in searchable_fields):
                    self.filtered_clients_list.append(client)
        
        return len(self.filtered_clients_list)


def test_search_functionality():
    """Tester la fonctionnalité de recherche"""
    print("🔍 Test de la logique de recherche client")
    print("=" * 50)
    
    # Créer une instance mock du formulaire
    form = MockSalesFormDialog()
    
    print(f"📊 Nombre total de clients: {len(form.clients_list)}")
    print("\n📋 Liste des clients de test:")
    for client in form.clients_list:
        nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
        if not nom_complet:
            nom_complet = client.get('entreprise', f"Client {client.get('id', '')}")
        print(f"   - {nom_complet} ({client.get('email', 'N/A')})")
    
    # Tests de recherche
    test_cases = [
        ("Dupont", "Recherche par nom de famille"),
        ("Jean", "Recherche par prénom"),
        ("marie", "Recherche insensible à la casse"),
        ("Entreprise", "Recherche par entreprise"),
        ("gmail", "Recherche par email"),
        ("0123", "Recherche par téléphone"),
        ("durand", "Recherche dans nom et entreprise"),
        ("", "Recherche vide (tous les clients)"),
        ("ClientInexistant", "Recherche sans résultat"),
        ("sophie.lefebvre", "Recherche par email complet"),
        ("Test SARL", "Recherche par partie d'entreprise")
    ]
    
    print("\n🧪 Exécution des tests de recherche:")
    print("-" * 50)
    
    all_tests_passed = True
    
    for search_term, description in test_cases:
        result_count = form.on_client_search_text_change(None, search_term)
        
        print(f"Test: {description}")
        print(f"   Terme recherché: '{search_term}'")
        print(f"   Résultats trouvés: {result_count}")
        
        # Afficher les clients trouvés
        if result_count > 0:
            print("   Clients trouvés:")
            for client in form.filtered_clients_list:
                nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
                if not nom_complet:
                    nom_complet = client.get('entreprise', f"Client {client.get('id', '')}")
                print(f"     - {nom_complet}")
        else:
            print("   Aucun client trouvé")
        
        # Vérifications spécifiques
        if search_term == "Dupont" and result_count != 1:
            print("   ❌ ÉCHEC: Devrait trouver exactement 1 client")
            all_tests_passed = False
        elif search_term == "Jean" and result_count != 1:
            print("   ❌ ÉCHEC: Devrait trouver exactement 1 client")
            all_tests_passed = False
        elif search_term == "Entreprise" and result_count != 1:
            print("   ❌ ÉCHEC: Devrait trouver exactement 1 client")
            all_tests_passed = False
        elif search_term == "" and result_count != len(form.clients_list):
            print("   ❌ ÉCHEC: Devrait trouver tous les clients")
            all_tests_passed = False
        elif search_term == "ClientInexistant" and result_count != 0:
            print("   ❌ ÉCHEC: Ne devrait trouver aucun client")
            all_tests_passed = False
        elif search_term == "durand" and result_count != 1:
            print("   ❌ ÉCHEC: Devrait trouver exactement 1 client (Pierre Durand)")
            all_tests_passed = False
        else:
            print("   ✅ SUCCÈS")
        
        print()
    
    # Résumé des tests
    print("=" * 50)
    if all_tests_passed:
        print("🎉 TOUS LES TESTS SONT PASSÉS!")
        print("✅ La fonctionnalité de recherche client fonctionne correctement")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("⚠️ Vérifiez l'implémentation de la recherche")
    
    return all_tests_passed


def test_edge_cases():
    """Tester les cas limites"""
    print("\n🔬 Test des cas limites")
    print("-" * 30)
    
    form = MockSalesFormDialog()
    
    edge_cases = [
        ("   ", "Espaces uniquement"),
        ("DUPONT", "Tout en majuscules"),
        ("jean dupont", "Nom complet avec espace"),
        ("@", "Caractère spécial"),
        ("123", "Chiffres uniquement"),
        ("a", "Une seule lettre"),
        ("très long terme de recherche qui ne devrait rien donner", "Terme très long")
    ]
    
    for search_term, description in edge_cases:
        result_count = form.on_client_search_text_change(None, search_term)
        print(f"Test: {description}")
        print(f"   Terme: '{search_term}' -> {result_count} résultat(s)")
        
        if result_count > 0:
            for client in form.filtered_clients_list:
                nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
                if not nom_complet:
                    nom_complet = client.get('entreprise', f"Client {client.get('id', '')}")
                print(f"     - {nom_complet}")
        print()


def main():
    """Fonction principale de test"""
    print("🚀 Test de la fonctionnalité de recherche client")
    print("🎯 Test de la logique sans interface graphique")
    print()
    
    try:
        # Test principal
        success = test_search_functionality()
        
        # Tests des cas limites
        test_edge_cases()
        
        print("\n📋 RÉSUMÉ:")
        if success:
            print("✅ La logique de recherche client est implémentée correctement")
            print("🎯 Fonctionnalités testées:")
            print("   - Recherche par nom de famille")
            print("   - Recherche par prénom")
            print("   - Recherche par entreprise")
            print("   - Recherche par email")
            print("   - Recherche par téléphone")
            print("   - Recherche insensible à la casse")
            print("   - Gestion des recherches vides")
            print("   - Gestion des recherches sans résultat")
        else:
            print("❌ Des problèmes ont été détectés dans la logique de recherche")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()