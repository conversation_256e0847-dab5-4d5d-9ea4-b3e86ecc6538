#!/usr/bin/env python3
"""
Test de la correction du formulaire de vente
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

def test_sales_form_fix():
    """Test de la correction du formulaire de vente"""
    print("🔧 Test - Correction SalesFormDialog")
    print("=" * 40)
    
    try:
        # 1. Test de compilation
        print("1️⃣ Vérification de la compilation...")
        
        import py_compile
        py_compile.compile('forms/sales_form.py', doraise=True)
        print("   ✅ forms/sales_form.py compile sans erreur")
        
        # 2. Test d'import
        print("\n2️⃣ Test d'import...")
        
        from forms.sales_form import SalesFormDialog
        print("   ✅ Import SalesFormDialog réussi")
        
        # 3. Test de création d'instance
        print("\n3️⃣ Test de création d'instance...")
        
        # Données de test
        test_client_data = {
            'id': 16,
            'nom': '<PERSON><PERSON>',
            'prenom': '<PERSON>',
            'entreprise': 'Test SARL'
        }
        
        # Créer une instance (sans l'ouvrir)
        dialog = SalesFormDialog(
            sale_data={'client_id': 16, 'client_data': test_client_data},
            on_save_callback=lambda x: print("Callback test")
        )
        print("   ✅ Instance SalesFormDialog créée avec succès")
        
        # 4. Vérifier que la méthode existe
        print("\n4️⃣ Vérification des méthodes...")
        
        if hasattr(dialog, 'open_product_dropdown'):
            print("   ✅ Méthode 'open_product_dropdown' présente")
        else:
            print("   ❌ Méthode 'open_product_dropdown' MANQUANTE")
            return False
        
        if hasattr(dialog, 'open_product_selection'):
            print("   ⚠️ Ancienne méthode 'open_product_selection' encore présente")
        else:
            print("   ✅ Ancienne méthode 'open_product_selection' supprimée")
        
        # 5. Test de la correction
        print("\n5️⃣ Vérification de la correction...")
        
        with open('forms/sales_form.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'on_release=self.open_product_selection' in content:
            print("   ❌ Référence incorrecte 'open_product_selection' encore présente")
            return False
        else:
            print("   ✅ Référence incorrecte supprimée")
        
        if 'on_release=self.open_product_dropdown' in content:
            print("   ✅ Référence correcte 'open_product_dropdown' présente")
        else:
            print("   ❌ Référence correcte 'open_product_dropdown' MANQUANTE")
            return False
        
        print("\n" + "=" * 40)
        print("🎉 CORRECTION RÉUSSIE !")
        
        print("\n📋 Changement effectué:")
        print("   ❌ on_release=self.open_product_selection (SUPPRIMÉ)")
        print("   ✅ on_release=self.open_product_dropdown (AJOUTÉ)")
        
        print("\n💡 Explication:")
        print("   • La méthode s'appelle 'open_product_dropdown'")
        print("   • Le bouton faisait référence à 'open_product_selection'")
        print("   • Cette référence incorrecte causait l'erreur")
        
        print("\n🚀 Le formulaire de vente devrait maintenant s'ouvrir sans erreur !")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_sales_form_fix()
    
    if success:
        print("\n✅ CORRECTION VALIDÉE")
        print("Le bouton 'Nouvelle vente' devrait maintenant fonctionner.")
    else:
        print("\n❌ PROBLÈME DÉTECTÉ")
        print("Vérifiez les erreurs ci-dessus.")
    
    input("\nAppuyez sur Entrée pour quitter...")