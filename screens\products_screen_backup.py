"""
Écran de gestion des produits
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import MDTextField
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.chip import MDChip
from kivymd.uix.menu import MDDropdownMenu
from kivymd.app import MDApp
from kivy.clock import Clock
import threading
from utils.barcode_utils import BarcodeGenerator, BarcodeValidator, BarcodeSearch, validate_barcode_input


class ProductCard(MDCard):
    """Carte pour afficher un produit"""
    
    def __init__(self, product_data, on_edit_callback, on_delete_callback, **kwargs):
        super().__init__(**kwargs)
        self.product_data = product_data
        self.elevation = 2
        self.padding = "16dp"
        self.size_hint_y = None
        self.height = "160dp"
        self.spacing = "8dp"
        
        layout = MDBoxLayout(orientation='vertical', spacing="4dp")
        
        # En-tête avec nom et actions
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="32dp")
        
        nom_label = MDLabel(
            text=product_data.get('nom', 'Produit sans nom'),
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        
        # Boutons d'action
        actions_layout = MDBoxLayout(orientation='horizontal', size_hint_x=0.3, spacing="4dp")
        
        edit_btn = MDIconButton(
            icon="pencil",
            theme_icon_color="Primary",
            on_release=lambda x: on_edit_callback(product_data)
        )
        
        delete_btn = MDIconButton(
            icon="delete",
            theme_icon_color="Error",
            on_release=lambda x: on_delete_callback(product_data)
        )
        
        actions_layout.add_widget(edit_btn)
        actions_layout.add_widget(delete_btn)
        
        header_layout.add_widget(nom_label)
        header_layout.add_widget(actions_layout)
        
        # Informations du produit
        info_layout = MDBoxLayout(orientation='vertical', spacing="2dp")
        
        # Référence et code-barres
        ref_barcode_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="16dp")
        
        if product_data.get('reference'):
            ref_label = MDLabel(
                text=f"Réf: {product_data['reference']}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_x=0.5
            )
            ref_barcode_layout.add_widget(ref_label)
        
        if product_data.get('code_barre'):
            barcode_label = MDLabel(
                text=f"Code-barres: {product_data['code_barre']}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_x=0.5
            )
            ref_barcode_layout.add_widget(barcode_label)
        
        if ref_barcode_layout.children:
            info_layout.add_widget(ref_barcode_layout)
        
        if product_data.get('categorie_nom'):
            cat_label = MDLabel(
                text=f"Catégorie: {product_data['categorie_nom']}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="16dp"
            )
            info_layout.add_widget(cat_label)
        
        # Prix et stock
        prix_stock_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="20dp")
        
        prix_label = MDLabel(
            text=f"Prix: {product_data.get('prix_vente', 0):.2f} DH",
            font_style="Caption",
            theme_text_color="Primary",
            size_hint_x=0.5
        )
        
        stock = product_data.get('stock_actuel', 0)
        stock_min = product_data.get('stock_minimum', 0)
        stock_color = "Error" if stock <= stock_min else "Secondary"
        
        stock_label = MDLabel(
            text=f"Stock: {stock}",
            font_style="Caption",
            theme_text_color=stock_color,
            size_hint_x=0.5
        )
        
        prix_stock_layout.add_widget(prix_label)
        prix_stock_layout.add_widget(stock_label)
        
        info_layout.add_widget(prix_stock_layout)
        
        if product_data.get('description'):
            desc_label = MDLabel(
                text=product_data['description'][:50] + "..." if len(product_data['description']) > 50 else product_data['description'],
                font_style="Caption",
                theme_text_color="Secondary"
            )
            info_layout.add_widget(desc_label)
        
        layout.add_widget(header_layout)
        layout.add_widget(info_layout)
        self.add_widget(layout)


class ProductFormDialog(MDDialog):
    """Dialog pour ajouter/modifier un produit"""
    
    def __init__(self, product_data=None, categories=None, fournisseurs=None, on_save_callback=None, **kwargs):
        self.product_data = product_data or {}
        self.categories = categories or []
        self.fournisseurs = fournisseurs or []
        self.on_save_callback = on_save_callback
        
        # Créer le contenu du formulaire
        content_cls = self._create_form_content()
        
        # Boutons
        buttons = [
            MDFlatButton(
                text="ANNULER",
                on_release=self.dismiss
            ),
            MDRaisedButton(
                text="ENREGISTRER",
                on_release=self.save_product
            )
        ]
        
        title = "Modifier le produit" if product_data else "Nouveau produit"
        
        super().__init__(
            title=title,
            type="custom",
            content_cls=content_cls,
            buttons=buttons,
            size_hint=(0.9, None),
            height="700dp",
            **kwargs
        )
        
        # Configuration de la navigation par tabulation après l'initialisation
        self._setup_tab_navigation()
    
    def _create_form_content(self):
        """Créer le contenu du formulaire"""
        form_layout = MDBoxLayout(orientation='vertical', spacing="16dp", adaptive_height=True)
        
        self.nom_field = MDTextField(
            hint_text="Nom du produit *",
            text=self.product_data.get('nom', ''),
            required=True
        )
        
        self.description_field = MDTextField(
            hint_text="Description",
            text=self.product_data.get('description', ''),
            multiline=True,
            max_height="80dp"
        )
        
        self.reference_field = MDTextField(
            hint_text="Référence *",
            text=self.product_data.get('reference', ''),
            required=True
        )
        
        # Section code-barres avec boutons
        barcode_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="56dp", spacing="8dp")
        
        self.code_barre_field = MDTextField(
            hint_text="Code-barres",
            text=self.product_data.get('code_barre', ''),
            size_hint_x=0.7
        )
        self.code_barre_field.bind(text=self.validate_barcode)
        
        generate_barcode_btn = MDIconButton(
            icon="barcode-scan",
            theme_icon_color="Primary",
            on_release=self.generate_barcode,
            size_hint_x=0.15
        )
        
        validate_barcode_btn = MDIconButton(
            icon="check-circle",
            theme_icon_color="Primary",
            on_release=self.check_barcode,
            size_hint_x=0.15
        )
        
        barcode_layout.add_widget(self.code_barre_field)
        barcode_layout.add_widget(generate_barcode_btn)
        barcode_layout.add_widget(validate_barcode_btn)
        
        # Label de validation du code-barres
        self.barcode_validation_label = MDLabel(
            text="",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="16dp"
        )
        
        self.prix_achat_field = MDTextField(
            hint_text="Prix d'achat",
            text=str(self.product_data.get('prix_achat', 0)),
            input_filter="float"
        )
        
        self.prix_vente_field = MDTextField(
            hint_text="Prix de vente *",
            text=str(self.product_data.get('prix_vente', 0)),
            input_filter="float",
            required=True
        )
        
        self.stock_actuel_field = MDTextField(
            hint_text="Stock actuel",
            text=str(self.product_data.get('stock_actuel', 0)),
            input_filter="int"
        )
        
        self.stock_minimum_field = MDTextField(
            hint_text="Stock minimum",
            text=str(self.product_data.get('stock_minimum', 0)),
            input_filter="int"
        )
        
        self.tva_field = MDTextField(
            hint_text="TVA (%)",
            text=str(self.product_data.get('tva', 20.0)),
            input_filter="float"
        )
        
        # Checkbox pour actif/inactif
        checkbox_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="48dp")
        checkbox_label = MDLabel(text="Produit actif", size_hint_x=0.7)
        self.actif_checkbox = MDCheckbox(
            active=bool(self.product_data.get('actif', True)),
            size_hint_x=0.3
        )
        checkbox_layout.add_widget(checkbox_label)
        checkbox_layout.add_widget(self.actif_checkbox)
        
        # Ordre des champs pour la navigation par tabulation
        self.fields_order = [
            self.nom_field,
            self.description_field,
            self.reference_field,
            self.code_barre_field,
            self.prix_achat_field,
            self.prix_vente_field,
            self.stock_actuel_field,
            self.stock_minimum_field,
            self.tva_field
        ]
        
        # Ajout des champs au formulaire dans l'ordre de tabulation
        form_layout.add_widget(self.nom_field)
        form_layout.add_widget(self.description_field)
        form_layout.add_widget(self.reference_field)
        form_layout.add_widget(barcode_layout)
        form_layout.add_widget(self.barcode_validation_label)
        form_layout.add_widget(self.prix_achat_field)
        form_layout.add_widget(self.prix_vente_field)
        form_layout.add_widget(self.stock_actuel_field)
        form_layout.add_widget(self.stock_minimum_field)
        form_layout.add_widget(self.tva_field)
        form_layout.add_widget(checkbox_layout)
        
        return form_layout
    
    def _setup_tab_navigation(self):
        """Configure la navigation par tabulation entre les champs"""
        # Configuration de l'ordre de tabulation pour chaque champ
        for i, field in enumerate(self.fields_order):
            if hasattr(field, 'bind'):
                # Lier l'événement de tabulation
                field.bind(on_text_validate=self._on_tab_pressed)
                
                # Stocker l'index pour la navigation
                field.tab_index = i
        
        # Focus sur le premier champ
        if self.fields_order:
            self.fields_order[0].focus = True
    
    def _on_tab_pressed(self, current_field):
        """Gérer la navigation par tabulation"""
        try:
            current_index = getattr(current_field, 'tab_index', -1)
            
            if current_index >= 0 and current_index < len(self.fields_order) - 1:
                # Passer au champ suivant
                next_field = self.fields_order[current_index + 1]
                next_field.focus = True
            else:
                # Dernier champ, enlever le focus
                current_field.focus = False
        except Exception as e:
            print(f"Erreur navigation tabulation: {e}")
    
    def save_product(self, *args):
        """Enregistrer le produit"""
        # Validation basique
        if not self.nom_field.text.strip():
            self.nom_field.error = True
            self.nom_field.helper_text = "Le nom est obligatoire"
            return
        
        if not self.reference_field.text.strip():
            self.reference_field.error = True
            self.reference_field.helper_text = "La référence est obligatoire"
            return
        
        try:
            prix_vente = float(self.prix_vente_field.text or 0)
            if prix_vente <= 0:
                self.prix_vente_field.error = True
                self.prix_vente_field.helper_text = "Le prix de vente doit être supérieur à 0"
                return
        except ValueError:
            self.prix_vente_field.error = True
            self.prix_vente_field.helper_text = "Prix de vente invalide"
            return
        
        # Préparer les données
        product_data = {
            'nom': self.nom_field.text.strip(),
            'description': self.description_field.text.strip(),
            'reference': self.reference_field.text.strip(),
            'code_barre': self.code_barre_field.text.strip(),
            'prix_achat': float(self.prix_achat_field.text or 0),
            'prix_vente': float(self.prix_vente_field.text or 0),
            'stock_actuel': int(self.stock_actuel_field.text or 0),
            'stock_minimum': int(self.stock_minimum_field.text or 0),
            'tva': float(self.tva_field.text or 20.0),
            'actif': self.actif_checkbox.active
        }
        
        # Ajouter l'ID si c'est une modification
        if self.product_data.get('id'):
            product_data['id'] = self.product_data['id']
        
        # Appeler le callback
        if self.on_save_callback:
            self.on_save_callback(product_data)
        
        self.dismiss()
    
    def validate_barcode(self, instance, text):
        """Valider le code-barres en temps réel"""
        if not text.strip():
            self.barcode_validation_label.text = ""
            return
        
        validation = validate_barcode_input(text.strip())
        
        if validation['valid']:
            self.barcode_validation_label.text = f"✓ {validation['message']}"
            self.barcode_validation_label.theme_text_color = "Custom"
            self.barcode_validation_label.text_color = (0, 0.7, 0, 1)  # Vert
        else:
            self.barcode_validation_label.text = f"✗ {validation['message']}"
            self.barcode_validation_label.theme_text_color = "Custom"
            self.barcode_validation_label.text_color = (0.8, 0, 0, 1)  # Rouge
    
    def generate_barcode(self, *args):
        """Générer un code-barres automatiquement"""
        # Menu pour choisir le type de code-barres
        menu_items = [
            {
                "text": "EAN-13",
                "viewclass": "OneLineListItem",
                "on_release": lambda x="EAN13": self.generate_barcode_type(x)
            },
            {
                "text": "EAN-8",
                "viewclass": "OneLineListItem", 
                "on_release": lambda x="EAN8": self.generate_barcode_type(x)
            },
            {
                "text": "Code 128",
                "viewclass": "OneLineListItem",
                "on_release": lambda x="CODE128": self.generate_barcode_type(x)
            },
            {
                "text": "Personnalisé",
                "viewclass": "OneLineListItem",
                "on_release": lambda x="CUSTOM": self.generate_barcode_type(x)
            }
        ]
        
        self.barcode_menu = MDDropdownMenu(
            caller=self.code_barre_field,
            items=menu_items,
            max_height="200dp"
        )
        self.barcode_menu.open()
    
    def generate_barcode_type(self, barcode_type):
        """Générer un code-barres du type spécifié"""
        self.barcode_menu.dismiss()
        
        try:
            app = MDApp.get_running_app()
            db_manager = app.db_manager
            
            # Générer un code-barres unique
            barcode = BarcodeSearch.generate_unique_barcode(db_manager, barcode_type)
            self.code_barre_field.text = barcode
            
            # Valider le code généré
            self.validate_barcode(None, barcode)
            
        except Exception as e:
            print(f"Erreur lors de la génération du code-barres: {e}")
            self.barcode_validation_label.text = "Erreur lors de la génération"
            self.barcode_validation_label.theme_text_color = "Error"
    
    def check_barcode(self, *args):
        """Vérifier si le code-barres existe déjà"""
        barcode = self.code_barre_field.text.strip()
        if not barcode:
            return
        
        try:
            app = MDApp.get_running_app()
            db_manager = app.db_manager
            
            # Exclure le produit actuel si c'est une modification
            exclude_id = self.product_data.get('id') if self.product_data else None
            
            exists = BarcodeSearch.check_barcode_exists(db_manager, barcode, exclude_id)
            
            if exists:
                self.barcode_validation_label.text = "⚠ Ce code-barres existe déjà"
                self.barcode_validation_label.theme_text_color = "Custom"
                self.barcode_validation_label.text_color = (0.8, 0.4, 0, 1)  # Orange
            else:
                validation = validate_barcode_input(barcode)
                if validation['valid']:
                    self.barcode_validation_label.text = "✓ Code-barres disponible"
                    self.barcode_validation_label.theme_text_color = "Custom"
                    self.barcode_validation_label.text_color = (0, 0.7, 0, 1)  # Vert
                else:
                    self.validate_barcode(None, barcode)
        
        except Exception as e:
            print(f"Erreur lors de la vérification du code-barres: {e}")
            self.barcode_validation_label.text = "Erreur lors de la vérification"
            self.barcode_validation_label.theme_text_color = "Error"


class ProductsScreen(MDScreen):
    """Écran de gestion des produits"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.products_data = []
        self.filtered_products = []
        self.categories = []
        self.fournisseurs = []
        self.build_ui()
    
    def build_ui(self):
        """Construction de l'interface utilisateur"""
        main_layout = MDBoxLayout(orientation='vertical', padding="16dp", spacing="16dp")
        
        # En-tête avec titre et bouton d'ajout
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="48dp")
        
        title_label = MDLabel(
            text="Gestion des Produits",
            font_style="H5",
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        
        add_button = MDRaisedButton(
            text="Nouveau Produit",
            icon="plus",
            on_release=self.add_product,
            size_hint_x=0.3
        )
        
        header_layout.add_widget(title_label)
        header_layout.add_widget(add_button)
        
        # Barre de recherche et filtres
        search_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="56dp", spacing="8dp")
        
        self.search_field = MDTextField(
            hint_text="Rechercher un produit...",
            icon_left="magnify",
            size_hint_x=0.5,
            on_text=self.filter_products
        )
        
        # Bouton de recherche par code-barres
        barcode_search_btn = MDIconButton(
            icon="barcode-scan",
            theme_icon_color="Primary",
            on_release=self.search_by_barcode,
            size_hint_x=0.1
        )
        
        stock_bas_button = MDRaisedButton(
            text="Stock bas",
            icon="alert-circle",
            md_bg_color=(1, 0.3, 0.3, 1),  # Rouge
            size_hint_x=0.2,
            on_release=self.show_low_stock
        )
        
        # Bouton d'export
        export_btn = MDIconButton(
            icon="export",
            theme_icon_color="Secondary",
            on_release=self.export_products,
            size_hint_x=0.1
        )
        
        search_layout.add_widget(self.search_field)
        search_layout.add_widget(barcode_search_btn)
        search_layout.add_widget(stock_bas_button)
        search_layout.add_widget(export_btn)
        
        # ScrollView pour la liste des produits
        self.scroll = MDScrollView()
        self.products_layout = MDBoxLayout(orientation='vertical', spacing="8dp", adaptive_height=True)
        self.scroll.add_widget(self.products_layout)
        
        # Message quand aucun produit
        self.no_products_label = MDLabel(
            text="Aucun produit trouvé",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        main_layout.add_widget(header_layout)
        main_layout.add_widget(search_layout)
        main_layout.add_widget(self.scroll)
        
        self.add_widget(main_layout)
    
    def on_enter(self):
        """Actions à effectuer lors de l'entrée sur l'écran"""
        self.load_products()
        self.load_categories_and_suppliers()
    
    def load_products(self):
        """Charger la liste des produits"""
        def load_data():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                # Récupérer tous les produits avec leurs catégories
                self.products_data = db_manager.execute_query("""
                    SELECT p.*, c.nom as categorie_nom, f.nom as fournisseur_nom
                    FROM produits p
                    LEFT JOIN categories c ON p.categorie_id = c.id
                    LEFT JOIN fournisseurs f ON p.fournisseur_id = f.id
                    WHERE p.actif = 1
                    ORDER BY p.nom
                """)
                
                # Mettre à jour l'interface
                Clock.schedule_once(self.update_products_ui, 0)
                
            except Exception as e:
                print(f"Erreur lors du chargement des produits: {e}")
        
        threading.Thread(target=load_data, daemon=True).start()
    
    def load_categories_and_suppliers(self):
        """Charger les catégories et fournisseurs"""
        def load_data():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                # Récupérer les catégories
                self.categories = db_manager.execute_query("SELECT * FROM categories ORDER BY nom")
                
                # Récupérer les fournisseurs
                self.fournisseurs = db_manager.execute_query("SELECT * FROM fournisseurs WHERE actif = 1 ORDER BY nom")
                
            except Exception as e:
                print(f"Erreur lors du chargement des catégories/fournisseurs: {e}")
        
        threading.Thread(target=load_data, daemon=True).start()
    
    def update_products_ui(self, dt):
        """Mettre à jour l'interface des produits"""
        self.products_layout.clear_widgets()
        
        products_to_show = self.filtered_products if hasattr(self, 'filtered_products') and self.filtered_products else self.products_data
        
        if not products_to_show:
            self.products_layout.add_widget(self.no_products_label)
        else:
            for product in products_to_show:
                product_card = ProductCard(
                    product,
                    self.edit_product,
                    self.delete_product
                )
                self.products_layout.add_widget(product_card)
    
    def filter_products(self, instance, text):
        """Filtrer les produits selon le texte de recherche"""
        if not text.strip():
            self.filtered_products = self.products_data
        else:
            search_text = text.lower()
            self.filtered_products = []
            
            for product in self.products_data:
                # Recherche dans nom, référence, description, catégorie
                searchable_fields = [
                    product.get('nom', ''),
                    product.get('reference', ''),
                    product.get('description', ''),
                    product.get('categorie_nom', '')
                ]
                
                if any(search_text in field.lower() for field in searchable_fields if field):
                    self.filtered_products.append(product)
        
        self.update_products_ui(None)
    
    def show_low_stock(self, *args):
        """Afficher les produits en stock bas"""
        self.filtered_products = [
            product for product in self.products_data
            if product.get('stock_actuel', 0) <= product.get('stock_minimum', 0)
        ]
        self.update_products_ui(None)
    
    def add_product(self, *args):
        """Ajouter un nouveau produit"""
        dialog = ProductFormDialog(
            categories=self.categories,
            fournisseurs=self.fournisseurs,
            on_save_callback=self.save_product
        )
        dialog.open()
    
    def edit_product(self, product_data):
        """Modifier un produit existant"""
        dialog = ProductFormDialog(
            product_data=product_data,
            categories=self.categories,
            fournisseurs=self.fournisseurs,
            on_save_callback=self.save_product
        )
        dialog.open()
    
    def save_product(self, product_data):
        """Enregistrer un produit (nouveau ou modifié)"""
        def save_data():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                if product_data.get('id'):
                    # Modification
                    query = """
                        UPDATE produits SET 
                        nom = ?, description = ?, reference = ?, code_barre = ?,
                        prix_achat = ?, prix_vente = ?, stock_actuel = ?, stock_minimum = ?,
                        tva = ?, actif = ?
                        WHERE id = ?
                    """
                    params = (
                        product_data['nom'], product_data['description'], product_data['reference'],
                        product_data['code_barre'], product_data['prix_achat'], product_data['prix_vente'],
                        product_data['stock_actuel'], product_data['stock_minimum'], product_data['tva'],
                        product_data['actif'], product_data['id']
                    )
                else:
                    # Nouveau produit
                    query = """
                        INSERT INTO produits 
                        (nom, description, reference, code_barre, prix_achat, prix_vente, 
                         stock_actuel, stock_minimum, tva, actif)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    params = (
                        product_data['nom'], product_data['description'], product_data['reference'],
                        product_data['code_barre'], product_data['prix_achat'], product_data['prix_vente'],
                        product_data['stock_actuel'], product_data['stock_minimum'], product_data['tva'],
                        product_data['actif']
                    )
                
                success = db_manager.execute_update(query, params)
                
                if success:
                    # Recharger la liste
                    Clock.schedule_once(lambda dt: self.load_products(), 0)
                else:
                    print("Erreur lors de l'enregistrement du produit")
                
            except Exception as e:
                print(f"Erreur lors de l'enregistrement: {e}")
        
        threading.Thread(target=save_data, daemon=True).start()
    
    def delete_product(self, product_data):
        """Supprimer un produit (désactivation)"""
        def confirm_delete():
            def delete_data():
                try:
                    app = MDApp.get_running_app()
                    db_manager = app.db_manager
                    
                    # Désactiver le produit au lieu de le supprimer
                    success = db_manager.execute_update(
                        "UPDATE produits SET actif = 0 WHERE id = ?",
                        (product_data['id'],)
                    )
                    
                    if success:
                        # Recharger la liste
                        Clock.schedule_once(lambda dt: self.load_products(), 0)
                    else:
                        print("Erreur lors de la suppression du produit")
                
                except Exception as e:
                    print(f"Erreur lors de la suppression: {e}")
            
            threading.Thread(target=delete_data, daemon=True).start()
        
        # Dialog de confirmation
        confirm_dialog = MDDialog(
            title="Confirmer la suppression",
            text=f"Êtes-vous sûr de vouloir supprimer le produit '{product_data.get('nom', '')}' ?",
            buttons=[
                MDFlatButton(
                    text="ANNULER",
                    on_release=lambda x: confirm_dialog.dismiss()
                ),
                MDRaisedButton(
                    text="SUPPRIMER",
                    md_bg_color=(1, 0.3, 0.3, 1),
                    on_release=lambda x: (confirm_delete(), confirm_dialog.dismiss())
                )
            ]
        )
        confirm_dialog.open()
    
    def search_by_barcode(self, *args):
        """Rechercher un produit par code-barres"""
        # Dialog pour saisir le code-barres
        barcode_field = MDTextField(
            hint_text="Saisir ou scanner le code-barres",
            icon_left="barcode-scan"
        )
        
        def search_barcode():
            barcode = barcode_field.text.strip()
            if not barcode:
                return
            
            # Rechercher le produit
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                product = BarcodeSearch.search_product_by_barcode(db_manager, barcode)
                
                if product:
                    # Filtrer pour afficher seulement ce produit
                    self.filtered_products = [product]
                    self.update_products_ui(None)
                    search_dialog.dismiss()
                else:
                    # Aucun produit trouvé
                    barcode_field.error = True
                    barcode_field.helper_text = "Aucun produit trouvé avec ce code-barres"
            
            except Exception as e:
                print(f"Erreur lors de la recherche: {e}")
                barcode_field.error = True
                barcode_field.helper_text = "Erreur lors de la recherche"
        
        search_dialog = MDDialog(
            title="Recherche par code-barres",
            type="custom",
            content_cls=barcode_field,
            buttons=[
                MDFlatButton(
                    text="ANNULER",
                    on_release=lambda x: search_dialog.dismiss()
                ),
                MDRaisedButton(
                    text="RECHERCHER",
                    on_release=lambda x: search_barcode()
                )
            ]
        )
        search_dialog.open()
    
    def export_products(self, *args):
        """Exporter la liste des produits"""
        def export_data():
            try:
                from utils.helpers import export_to_csv, sanitize_filename
                from datetime import datetime
                import os
                
                app = MDApp.get_running_app()
                
                # Préparer les données pour l'export
                export_data = []
                products_to_export = self.filtered_products if hasattr(self, 'filtered_products') and self.filtered_products else self.products_data
                
                for product in products_to_export:
                    export_data.append({
                        'Nom': product.get('nom', ''),
                        'Référence': product.get('reference', ''),
                        'Code-barres': product.get('code_barre', ''),
                        'Description': product.get('description', ''),
                        'Catégorie': product.get('categorie_nom', ''),
                        'Prix d\'achat': product.get('prix_achat', 0),
                        'Prix de vente': product.get('prix_vente', 0),
                        'Stock actuel': product.get('stock_actuel', 0),
                        'Stock minimum': product.get('stock_minimum', 0),
                        'TVA (%)': product.get('tva', 0),
                        'Actif': 'Oui' if product.get('actif', 1) else 'Non'
                    })
                
                # Nom du fichier
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = sanitize_filename(f"produits_{timestamp}.csv")
                filepath = os.path.join("exports", filename)
                
                # Créer le dossier exports s'il n'existe pas
                os.makedirs("exports", exist_ok=True)
                
                # Exporter
                if export_to_csv(export_data, filepath):
                    Clock.schedule_once(lambda dt: self.show_export_success(filepath), 0)
                else:
                    Clock.schedule_once(lambda dt: self.show_export_error(), 0)
            
            except Exception as e:
                print(f"Erreur lors de l'export: {e}")
                Clock.schedule_once(lambda dt: self.show_export_error(), 0)
        
        threading.Thread(target=export_data, daemon=True).start()
    
    def show_export_success(self, filepath):
        """Afficher le message de succès d'export"""
        success_dialog = MDDialog(
            title="Export réussi",
            text=f"Les produits ont été exportés vers:\n{filepath}",
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: success_dialog.dismiss()
                )
            ]
        )
        success_dialog.open()
    
    def show_export_error(self):
        """Afficher le message d'erreur d'export"""
        error_dialog = MDDialog(
            title="Erreur d'export",
            text="Une erreur s'est produite lors de l'export des produits.",
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: error_dialog.dismiss()
                )
            ]
        )
        error_dialog.open()