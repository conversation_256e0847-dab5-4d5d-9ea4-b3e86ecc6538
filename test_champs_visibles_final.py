#!/usr/bin/env python3
"""
Test final pour vérifier que les champs du formulaire client sont visibles
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_champs_visibles_final():
    """Test final de visibilité des champs"""
    print("🧪 Test final de visibilité des champs du formulaire client")
    print("=" * 65)
    
    try:
        # Importer les modules nécessaires
        from kivymd.app import MDApp
        from screens.clients_screen import ClientFormDialog
        from main import GesComApp
        
        print("✅ Modules importés")
        
        # Créer une app de test
        class TestApp(GesComApp):
            def build(self):
                return super().build()
        
        app = TestApp()
        print("✅ Application créée")
        
        # Données de test
        test_client_data = {
            'nom': 'Dupont',
            'prenom': 'Jean',
            'entreprise': 'SARL Test',
            'email': '<EMAIL>',
            'telephone': '0123456789',
            'adresse': '123 Rue Test',
            'ville': 'Paris',
            'code_postal': '75001',
            'pays': 'France'
        }
        
        print("✅ Données de test préparées")
        
        # Test 1: Création du dialog
        print("🔸 Test 1: Création du dialog...")
        try:
            dialog = ClientFormDialog(client_data=test_client_data)
            print("  ✅ Dialog créé avec succès")
            
            # Vérifier que le dialog a un content_cls
            assert hasattr(dialog, 'content_cls')
            assert dialog.content_cls is not None
            print("  ✅ Content_cls présent")
            
            # Vérifier que c'est un ScrollView
            from kivymd.uix.scrollview import MDScrollView
            assert isinstance(dialog.content_cls, MDScrollView), f"Content n'est pas MDScrollView: {type(dialog.content_cls)}"
            print("  ✅ ScrollView présent")
            
            # Vérifier la configuration de la scrollbar
            scroll_view = dialog.content_cls
            print(f"  📏 bar_width: {scroll_view.bar_width}")
            assert scroll_view.bar_width == 0, f"bar_width devrait être 0, mais est {scroll_view.bar_width}"
            print("  ✅ Scrollbar masquée (bar_width = 0)")
            
        except Exception as e:
            print(f"  ❌ Erreur création dialog: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test 2: Vérification des champs
        print("🔸 Test 2: Vérification des champs...")
        try:
            # Liste des champs requis
            required_fields = [
                'nom_field', 'prenom_field', 'entreprise_field',
                'email_field', 'telephone_field', 'adresse_field',
                'ville_field', 'code_postal_field', 'pays_field'
            ]
            
            for field_name in required_fields:
                assert hasattr(dialog, field_name), f"Champ {field_name} manquant"
                field = getattr(dialog, field_name)
                assert field is not None, f"Champ {field_name} est None"
                print(f"  ✅ {field_name}: {field.text}")
            
            print("  ✅ Tous les champs présents et avec des valeurs")
            
        except Exception as e:
            print(f"  ❌ Erreur vérification champs: {e}")
            return False
        
        # Test 3: Vérification de la structure du ScrollView
        print("🔸 Test 3: Structure du ScrollView...")
        try:
            scroll_view = dialog.content_cls
            
            # Vérifier qu'il y a un enfant (le form_layout)
            children_count = len(scroll_view.children)
            print(f"  👶 Nombre d'enfants du ScrollView: {children_count}")
            assert children_count > 0, "ScrollView n'a pas d'enfants"
            
            # Vérifier que l'enfant est le form_layout
            form_layout = scroll_view.children[0]
            from kivymd.uix.boxlayout import MDBoxLayout
            assert isinstance(form_layout, MDBoxLayout), f"Enfant n'est pas MDBoxLayout: {type(form_layout)}"
            print("  ✅ form_layout présent dans ScrollView")
            
            # Vérifier que le form_layout a des enfants (les champs)
            form_children_count = len(form_layout.children)
            print(f"  👶 Nombre d'enfants du form_layout: {form_children_count}")
            assert form_children_count > 0, "form_layout n'a pas d'enfants"
            print("  ✅ Champs présents dans form_layout")
            
        except Exception as e:
            print(f"  ❌ Erreur structure ScrollView: {e}")
            return False
        
        # Test 4: Test avec nouveau client
        print("🔸 Test 4: Nouveau client...")
        try:
            new_dialog = ClientFormDialog()
            print("  ✅ Dialog nouveau client créé")
            
            # Vérifier que les champs sont vides mais présents
            assert new_dialog.nom_field.text == ""
            assert new_dialog.prenom_field.text == ""
            assert new_dialog.pays_field.text == "France"  # Valeur par défaut
            print("  ✅ Champs vides corrects")
            
            # Vérifier la même structure
            new_scroll_view = new_dialog.content_cls
            assert isinstance(new_scroll_view, MDScrollView)
            assert new_scroll_view.bar_width == 0
            print("  ✅ Structure identique pour nouveau client")
            
        except Exception as e:
            print(f"  ❌ Erreur dialog nouveau: {e}")
            return False
        
        # Test 5: Vérification de la navigation par tabulation
        print("🔸 Test 5: Navigation par tabulation...")
        try:
            # Vérifier que tab_order existe
            assert hasattr(dialog, 'tab_order'), "tab_order manquant"
            assert len(dialog.tab_order) == 9, f"tab_order devrait avoir 9 éléments, a {len(dialog.tab_order)}"
            print("  ✅ Navigation par tabulation configurée")
            
        except Exception as e:
            print(f"  ❌ Erreur navigation tabulation: {e}")
            return False
        
        print("\n🎉 TOUS LES TESTS DE VISIBILITÉ FINALE RÉUSSIS!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Test final de visibilité des champs - GesComPro_LibTam")
    print("=" * 70)
    
    success = test_champs_visibles_final()
    
    if success:
        print("\n✅ TOUS LES CHAMPS SONT MAINTENANT VISIBLES!")
        print("🎯 Configuration finale validée:")
        print("   • ScrollView: Présent et fonctionnel")
        print("   • Scrollbar: Masquée (bar_width = 0)")
        print("   • Champs: Tous visibles et accessibles")
        print("   • Organisation: Deux colonnes optimisées")
        print("   • Navigation: Tabulation par Entrée")
        print("   • Structure: form_layout dans ScrollView")
        print("\n🎮 Utilisation:")
        print("   • Molette souris: Défilement vertical")
        print("   • Entrée: Navigation entre champs")
        print("   • Clic direct: Sélection de champ")
        print("   • Interface épurée: Pas de scrollbar visible")
    else:
        print("\n❌ PROBLÈME AVEC LA VISIBILITÉ DES CHAMPS")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    print("\nAppuyez sur Entrée pour continuer...")
    input()