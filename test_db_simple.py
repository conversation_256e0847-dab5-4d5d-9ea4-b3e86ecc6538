"""
Test simple de la base de données
"""

import sys
import sqlite3
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

def test_database_direct():
    """Test direct de la base de données"""
    print("🗄️ Test direct de la base de données...")
    
    try:
        # Test avec base en mémoire
        conn = sqlite3.connect(":memory:")
        cursor = conn.cursor()
        
        # Créer table de test
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                prenom TEXT,
                email TEXT,
                telephone TEXT,
                actif BOOLEAN DEFAULT 1
            )
        ''')
        
        # Insérer données de test
        cursor.execute(
            "INSERT INTO clients (nom, prenom, email, telephone, actif) VALUES (?, ?, ?, ?, ?)",
            ("Test", "Client", "<EMAIL>", "0123456789", 1)
        )
        
        conn.commit()
        
        # Lire les données
        cursor.execute("SELECT * FROM clients WHERE nom = ?", ("Test",))
        result = cursor.fetchone()
        
        if result:
            print("✅ Test direct réussi")
            print(f"   Données: {result}")
            conn.close()
            return True
        else:
            print("❌ Aucune donnée trouvée")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_database_manager():
    """Test avec le DatabaseManager"""
    print("\n🗄️ Test avec DatabaseManager...")
    
    try:
        from database.db_manager import DatabaseManager
        
        # Créer une instance avec base en mémoire
        db_manager = DatabaseManager(":memory:")
        
        # Se connecter d'abord
        if not db_manager.connect():
            print("❌ Impossible de se connecter")
            return False
        
        # Initialiser (garde la connexion ouverte pour :memory:)
        if not db_manager.initialize_database():
            print("❌ Échec de l'initialisation")
            return False
        
        print("✅ Initialisation réussie")
        
        # Utiliser la même instance pour les tests
        # Test d'insertion
        query = "INSERT INTO clients (nom, prenom, email, telephone, actif) VALUES (?, ?, ?, ?, ?)"
        params = ("Test", "Client", "<EMAIL>", "0123456789", 1)
        
        if db_manager.execute_update(query, params):
            print("✅ Insertion réussie")
        else:
            print("❌ Échec de l'insertion")
            return False
        
        # Test de lecture
        clients = db_manager.execute_query("SELECT * FROM clients WHERE nom = ?", ("Test",))
        if clients and len(clients) > 0:
            print("✅ Lecture réussie")
            print(f"   Client trouvé: {dict(clients[0])}")
            return True
        else:
            print("❌ Aucun client trouvé")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("🧪 Tests de base de données simplifiés")
    print("=" * 50)
    
    tests = [
        ("Test direct SQLite", test_database_direct),
        ("Test DatabaseManager", test_database_manager)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 RÉSULTATS")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:<25} {status}")
    
    all_passed = all(result for _, result in results)
    if all_passed:
        print("\n🎉 Tous les tests sont réussis!")
    else:
        print("\n⚠️ Certains tests ont échoué.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    input("\nAppuyez sur Entrée pour continuer...")
    sys.exit(0 if success else 1)