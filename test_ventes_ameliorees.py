#!/usr/bin/env python3
"""
Test des améliorations du système de ventes
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from database.db_manager import DatabaseManager

class TestVentesAmelioreeApp(MDApp):
    def build(self):
        self.title = "🛒 Test Ventes Améliorées - GesComPro_LibTam"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Initialiser la base de données
        self.db_manager = DatabaseManager()
        
        screen = MDScreen()
        layout = MDBoxLayout(
            orientation='vertical', 
            padding="20dp", 
            spacing="20dp",
            pos_hint={'center_x': 0.5, 'center_y': 0.5}
        )
        
        # Titre
        title = MDLabel(
            text="🛒 Test des Améliorations Ventes",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        # Informations sur les améliorations
        info_text = """
✅ Améliorations Implémentées:

🔄 Gestion des Statuts:
• ✅ Marquer comme PAYÉE
• ❌ Marquer comme ANNULÉE  
• 🔄 Remettre EN COURS
• 📦 Restauration automatique du stock lors d'annulation

📋 Formulaire Nouvelle Vente:
• 👥 Liste déroulante pour sélectionner un client
• 💳 Liste déroulante pour le mode de paiement
  (Espèces, Carte bancaire, Chèque, Virement, etc.)

🎨 Interface Améliorée:
• Statuts avec icônes colorées
• Dialogs de confirmation
• Messages d'information clairs
        """
        
        info_label = MDLabel(
            text=info_text.strip(),
            theme_text_color="Primary",
            size_hint_y=None,
            height="300dp",
            halign="center"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="10dp",
            size_hint_y=None,
            height="200dp"
        )
        
        init_btn = MDRaisedButton(
            text="1. 🔧 Initialiser Données Test",
            size_hint=(None, None),
            size=("350dp", "50dp"),
            pos_hint={'center_x': 0.5},
            on_release=self.init_test_data
        )
        
        create_sale_btn = MDRaisedButton(
            text="2. 🛒 Créer Vente de Test",
            size_hint=(None, None),
            size=("350dp", "50dp"),
            pos_hint={'center_x': 0.5},
            on_release=self.create_test_sale
        )
        
        open_sales_btn = MDRaisedButton(
            text="3. 🚀 Tester Écran Ventes Amélioré",
            size_hint=(None, None),
            size=("350dp", "50dp"),
            pos_hint={'center_x': 0.5},
            on_release=self.open_sales_screen
        )
        
        buttons_layout.add_widget(init_btn)
        buttons_layout.add_widget(create_sale_btn)
        buttons_layout.add_widget(open_sales_btn)
        
        # Label pour les résultats
        self.result_label = MDLabel(
            text="Cliquez sur les boutons pour tester les améliorations",
            halign="center",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="60dp"
        )
        
        layout.add_widget(title)
        layout.add_widget(info_label)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def init_test_data(self, *args):
        """Initialiser des données de test"""
        try:
            print("🔧 Initialisation des données de test...")
            self.result_label.text = "Initialisation en cours..."
            
            # Import des fonctions
            from database.db_manager import add_client, add_product
            
            # Ajouter des clients de test
            clients_test = [
                {
                    'nom': 'Alami',
                    'prenom': 'Mohammed',
                    'email': '<EMAIL>',
                    'telephone': '0612345678',
                    'adresse': 'Casablanca, Maroc',
                    'actif': True
                },
                {
                    'nom': 'Bennani',
                    'prenom': 'Fatima',
                    'email': '<EMAIL>',
                    'telephone': '0687654321',
                    'adresse': 'Rabat, Maroc',
                    'actif': True
                },
                {
                    'entreprise': 'TechCorp Maroc',
                    'nom': 'Responsable',
                    'prenom': 'Achats',
                    'email': '<EMAIL>',
                    'telephone': '0522123456',
                    'adresse': 'Casablanca, Maroc',
                    'actif': True
                }
            ]
            
            for client in clients_test:
                try:
                    if hasattr(self.db_manager, 'connection') and self.db_manager.connection:
                        add_client(self.db_manager, client)
                        print(f"✅ Client ajouté: {client.get('prenom', '')} {client.get('nom', '')}")
                except Exception as e:
                    print(f"⚠️ Client déjà existant ou erreur: {e}")
            
            # Ajouter des produits de test
            produits_test = [
                {
                    'nom': 'Smartphone Pro Max',
                    'description': 'Smartphone haut de gamme',
                    'reference': 'PHONE-001',
                    'prix_achat': 800.0,
                    'prix_vente': 1200.0,
                    'stock_actuel': 25,
                    'stock_minimum': 5,
                    'tva': 20.0,
                    'actif': True
                },
                {
                    'nom': 'Casque Audio Bluetooth',
                    'description': 'Casque sans fil',
                    'reference': 'AUDIO-001',
                    'prix_achat': 80.0,
                    'prix_vente': 150.0,
                    'stock_actuel': 50,
                    'stock_minimum': 10,
                    'tva': 20.0,
                    'actif': True
                }
            ]
            
            for produit in produits_test:
                try:
                    if hasattr(self.db_manager, 'connection') and self.db_manager.connection:
                        add_product(self.db_manager, produit)
                        print(f"✅ Produit ajouté: {produit['nom']}")
                except Exception as e:
                    print(f"⚠️ Produit déjà existant ou erreur: {e}")
            
            self.result_label.text = "✅ Données de test initialisées!"
            print("✅ Initialisation terminée")
            
        except Exception as e:
            error_msg = f"❌ Erreur initialisation: {str(e)}"
            self.result_label.text = error_msg
            print(error_msg)
    
    def create_test_sale(self, *args):
        """Créer une vente de test"""
        try:
            print("🛒 Création d'une vente de test...")
            self.result_label.text = "Création vente de test..."
            
            # Import des fonctions
            from database.db_manager import get_all_clients, get_all_products, create_sale
            
            # Récupérer les données
            clients = get_all_clients(self.db_manager)
            products = get_all_products(self.db_manager)
            
            if clients and products:
                # Préparer une vente de test
                vente_data = {
                    'client_id': clients[0]['id'],
                    'mode_paiement': 'Carte bancaire',
                    'notes': 'Vente de test avec améliorations',
                    'statut': 'En cours'
                }
                
                sale_items = [
                    {
                        'product': products[0],
                        'quantite': 1,
                        'prix_unitaire': products[0]['prix_vente']
                    }
                ]
                
                vente_id = create_sale(self.db_manager, vente_data, sale_items)
                
                if vente_id:
                    self.result_label.text = f"✅ Vente de test créée (ID: {vente_id})"
                    print(f"✅ Vente de test créée avec succès (ID: {vente_id})")
                else:
                    self.result_label.text = "❌ Échec création vente de test"
                    print("❌ Échec création vente de test")
            else:
                self.result_label.text = "❌ Pas de clients ou produits disponibles"
                print("❌ Pas de clients ou produits disponibles")
            
        except Exception as e:
            error_msg = f"❌ Erreur création vente: {str(e)}"
            self.result_label.text = error_msg
            print(error_msg)
            import traceback
            traceback.print_exc()
    
    def open_sales_screen(self, *args):
        """Ouvrir l'écran des ventes amélioré"""
        try:
            print("🚀 Ouverture de l'écran des ventes amélioré...")
            self.result_label.text = "Chargement écran ventes..."
            
            # Import de l'écran
            from screens.sales_screen import SalesScreen
            
            # Créer l'écran
            sales_screen = SalesScreen()
            
            # Changer vers cet écran
            self.root.clear_widgets()
            self.root.add_widget(sales_screen)
            
            print("✅ Écran ventes amélioré chargé avec succès")
            
        except Exception as e:
            error_msg = f"❌ Erreur écran: {str(e)}"
            self.result_label.text = error_msg
            print(error_msg)
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    print("🚀 TEST DES AMÉLIORATIONS VENTES")
    print("=" * 50)
    print("✅ Nouvelles fonctionnalités:")
    print("   • Statuts avec boutons dédiés")
    print("   • Listes déroulantes clients/paiement")
    print("   • Restauration stock lors d'annulation")
    print("   • Interface améliorée avec icônes")
    print("=" * 50)
    
    try:
        app = TestVentesAmelioreeApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()