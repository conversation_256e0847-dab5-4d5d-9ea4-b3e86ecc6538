"""
Test d'intégration de la fonctionnalité de recherche client
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

from forms.sales_form import SalesFormDialog
from database.db_manager import DatabaseManager


def test_integration_with_database():
    """Tester l'intégration avec la base de données"""
    print("🔗 Test d'intégration avec la base de données")
    print("=" * 50)
    
    try:
        # Créer une instance du formulaire de vente
        print("📝 Création du formulaire de vente...")
        dialog = SalesFormDialog()
        
        # Vérifier que les composants de recherche sont présents
        print("🔍 Vérification des composants de recherche:")
        
        if hasattr(dialog, 'client_search_field'):
            print("✅ Champ de recherche client présent")
        else:
            print("❌ Champ de recherche client manquant")
            return False
        
        if hasattr(dialog, 'filtered_clients_list'):
            print("✅ Liste filtrée initialisée")
        else:
            print("❌ Liste filtrée non initialisée")
            return False
        
        if hasattr(dialog, 'clients_list'):
            print(f"✅ Liste des clients chargée ({len(dialog.clients_list)} clients)")
        else:
            print("❌ Liste des clients non chargée")
            return False
        
        # Tester la méthode de recherche
        print("\n🧪 Test de la méthode de recherche:")
        
        # Test avec les clients de la base ou les clients de test
        if dialog.clients_list:
            # Prendre le premier client pour le test
            first_client = dialog.clients_list[0]
            test_search_term = first_client.get('nom', '').lower()
            
            if test_search_term:
                print(f"   Recherche du terme: '{test_search_term}'")
                dialog.on_client_search_text_change(None, test_search_term)
                
                if len(dialog.filtered_clients_list) > 0:
                    print(f"   ✅ Résultats trouvés: {len(dialog.filtered_clients_list)}")
                    
                    # Vérifier que le client recherché est dans les résultats
                    found = any(client.get('nom', '').lower() == test_search_term 
                              for client in dialog.filtered_clients_list)
                    if found:
                        print("   ✅ Client recherché trouvé dans les résultats")
                    else:
                        print("   ❌ Client recherché non trouvé dans les résultats")
                        return False
                else:
                    print("   ❌ Aucun résultat trouvé")
                    return False
            else:
                print("   ⚠️ Impossible de tester - nom du premier client vide")
        
        # Test de réinitialisation
        print("\n🔄 Test de réinitialisation:")
        dialog.on_client_search_text_change(None, "")
        if len(dialog.filtered_clients_list) == len(dialog.clients_list):
            print("   ✅ Réinitialisation réussie")
        else:
            print("   ❌ Problème de réinitialisation")
            return False
        
        print("\n✅ Test d'intégration réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'intégration: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_database_connection():
    """Tester la connexion à la base de données"""
    print("\n🗄️ Test de connexion à la base de données")
    print("-" * 40)
    
    try:
        db_manager = DatabaseManager()
        
        if db_manager.connect():
            print("✅ Connexion à la base de données réussie")
            
            # Tester la requête des clients
            clients = db_manager.execute_query("""
                SELECT id, nom, prenom, entreprise, email, telephone
                FROM clients 
                ORDER BY nom, prenom
                LIMIT 5
            """)
            
            if clients:
                print(f"✅ {len(clients)} clients trouvés dans la base")
                print("📋 Aperçu des clients:")
                for client in clients[:3]:  # Afficher les 3 premiers
                    nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
                    if not nom_complet:
                        nom_complet = client.get('entreprise', f"Client {client.get('id', '')}")
                    print(f"   - {nom_complet}")
            else:
                print("⚠️ Aucun client trouvé dans la base (utilisation des clients de test)")
            
            db_manager.close()
            return True
            
        else:
            print("⚠️ Connexion à la base de données échouée (utilisation des clients de test)")
            return True  # Ce n'est pas une erreur critique
            
    except Exception as e:
        print(f"❌ Erreur de base de données: {e}")
        return False


def test_search_performance():
    """Tester les performances de la recherche"""
    print("\n⚡ Test de performance de la recherche")
    print("-" * 40)
    
    try:
        import time
        
        # Créer une instance du formulaire
        dialog = SalesFormDialog()
        
        # Mesurer le temps de recherche
        search_terms = ["test", "dupont", "martin", "entreprise", ""]
        
        for term in search_terms:
            start_time = time.time()
            dialog.on_client_search_text_change(None, term)
            end_time = time.time()
            
            duration = (end_time - start_time) * 1000  # en millisecondes
            print(f"   Recherche '{term}': {duration:.2f}ms ({len(dialog.filtered_clients_list)} résultats)")
        
        print("✅ Tests de performance terminés")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test de performance: {e}")
        return False


def main():
    """Fonction principale de test"""
    print("🚀 Test d'intégration de la recherche client")
    print("🎯 Vérification de l'intégration complète")
    print()
    
    all_tests_passed = True
    
    # Test de connexion à la base de données
    if not test_database_connection():
        all_tests_passed = False
    
    # Test d'intégration principal
    if not test_integration_with_database():
        all_tests_passed = False
    
    # Test de performance
    if not test_search_performance():
        all_tests_passed = False
    
    # Résumé final
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DES TESTS D'INTÉGRATION")
    print("=" * 60)
    
    if all_tests_passed:
        print("🎉 TOUS LES TESTS D'INTÉGRATION SONT PASSÉS!")
        print()
        print("✅ Fonctionnalités vérifiées:")
        print("   - Champ de recherche client ajouté au formulaire")
        print("   - Liste filtrée initialisée correctement")
        print("   - Méthode de recherche fonctionnelle")
        print("   - Intégration avec la base de données")
        print("   - Performance de recherche acceptable")
        print("   - Réinitialisation de la recherche")
        print()
        print("🎯 La fonctionnalité de recherche client est prête à l'utilisation!")
        
    else:
        print("❌ CERTAINS TESTS D'INTÉGRATION ONT ÉCHOUÉ")
        print("⚠️ Vérifiez l'implémentation avant utilisation")
    
    return all_tests_passed


if __name__ == "__main__":
    main()