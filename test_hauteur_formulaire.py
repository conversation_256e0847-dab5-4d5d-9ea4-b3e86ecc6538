#!/usr/bin/env python3
"""
Test pour vérifier l'affichage du formulaire avec la hauteur augmentée
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from screens.categories_screen import CategoryFormDialog


class TestHauteurFormulaireApp(MDApp):
    """Application de test pour la hauteur du formulaire"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Hauteur Formulaire Catégorie"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construction de l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        # Titre
        title = MDLabel(
            text="📏 Test - Hauteur Formulaire Augmentée",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Informations sur les dimensions
        info_label = MDLabel(
            text="Nouvelles dimensions du formulaire :\n"
                 "• Dialog total : 600dp (était 500dp)\n"
                 "• Container principal : 480dp (était 350dp)\n"
                 "• Container des champs : 420dp (était 280dp)\n\n"
                 "Testez l'affichage et l'espacement des éléments.",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="160dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="200dp"
        )
        
        # Test nouveau formulaire
        new_btn = MDRaisedButton(
            text="➕ Nouveau Formulaire (Hauteur 600dp)",
            size_hint_y=None,
            height="50dp",
            on_release=self.test_new_form
        )
        
        # Test modification avec données
        edit_btn = MDRaisedButton(
            text="✏️ Modification avec Données",
            size_hint_y=None,
            height="50dp",
            on_release=self.test_edit_form
        )
        
        # Test avec longue description
        long_desc_btn = MDRaisedButton(
            text="📝 Test avec Longue Description",
            size_hint_y=None,
            height="50dp",
            on_release=self.test_long_description
        )
        
        buttons_layout.add_widget(new_btn)
        buttons_layout.add_widget(edit_btn)
        buttons_layout.add_widget(long_desc_btn)
        
        # Résultats
        self.result_label = MDLabel(
            text="Cliquez sur les boutons pour tester l'affichage.\n"
                 "Vérifiez que tous les éléments sont bien visibles\n"
                 "et correctement espacés dans le formulaire.",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(info_label)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_new_form(self, *args):
        """Tester le nouveau formulaire"""
        self.result_label.text = "📏 Test: Nouveau formulaire avec hauteur 600dp\n" \
                                "Vérifiez l'espacement et la visibilité de tous les éléments."
        
        dialog = CategoryFormDialog(
            on_save_callback=self.on_save_callback
        )
        dialog.open()
        print("✅ Nouveau formulaire ouvert - Hauteur: 600dp")
    
    def test_edit_form(self, *args):
        """Tester le formulaire de modification"""
        self.result_label.text = "📏 Test: Formulaire de modification\n" \
                                "Avec informations supplémentaires affichées."
        
        # Données de test complètes
        test_data = {
            'id': 1,
            'nom': 'Électronique & Informatique',
            'description': 'Catégorie regroupant tous les produits électroniques, informatiques et high-tech.',
            'products_count': 15,
            'date_creation': '2024-01-15T10:30:00'
        }
        
        dialog = CategoryFormDialog(
            category_data=test_data,
            on_save_callback=self.on_save_callback
        )
        dialog.open()
        print("✅ Formulaire de modification ouvert avec données complètes")
    
    def test_long_description(self, *args):
        """Tester avec une longue description"""
        self.result_label.text = "📏 Test: Formulaire avec longue description\n" \
                                "Vérifiez que le champ multiline s'affiche correctement."
        
        # Données avec longue description
        test_data = {
            'id': 2,
            'nom': 'Alimentation & Boissons',
            'description': 'Cette catégorie comprend tous les produits alimentaires, '
                          'les boissons alcoolisées et non-alcoolisées, les produits frais, '
                          'les conserves, les surgelés, les épices et condiments, '
                          'ainsi que tous les articles de consommation courante '
                          'liés à l\'alimentation et à la restauration.',
            'products_count': 42,
            'date_creation': '2024-02-20T14:15:30'
        }
        
        dialog = CategoryFormDialog(
            category_data=test_data,
            on_save_callback=self.on_save_callback
        )
        dialog.open()
        print("✅ Formulaire avec longue description ouvert")
    
    def on_save_callback(self, category_data):
        """Callback de sauvegarde"""
        nom = category_data.get('nom', 'Sans nom')
        description = category_data.get('description', 'Aucune')
        
        self.result_label.text = f"✅ SUCCÈS - Formulaire fonctionnel !\n" \
                                f"Nom: {nom[:30]}{'...' if len(nom) > 30 else ''}\n" \
                                f"Description: {description[:50]}{'...' if len(description) > 50 else ''}\n" \
                                f"La hauteur augmentée améliore l'affichage !"
        
        print("🎉 SUCCÈS - Catégorie sauvegardée:")
        print(f"  - Nom: {nom}")
        print(f"  - Description: {description[:100]}{'...' if len(description) > 100 else ''}")
        print(f"  - ID: {category_data.get('id', 'Nouveau')}")


def main():
    """Fonction principale"""
    print("📏 Test - Hauteur Formulaire Catégorie Augmentée")
    print("=" * 60)
    print("NOUVELLES DIMENSIONS:")
    print("  • Dialog total : 600dp (+100dp)")
    print("  • Container principal : 480dp (+130dp)")
    print("  • Container des champs : 420dp (+140dp)")
    print("=" * 60)
    print("POINTS À VÉRIFIER:")
    print("1. Tous les éléments sont visibles")
    print("2. L'espacement est correct")
    print("3. Les champs ne sont pas tronqués")
    print("4. Les boutons sont bien positionnés")
    print("5. Le formulaire s'affiche entièrement à l'écran")
    print("=" * 60)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestHauteurFormulaireApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()