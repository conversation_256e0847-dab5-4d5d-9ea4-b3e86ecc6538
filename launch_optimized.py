#!/usr/bin/env python3
"""
Lanceur optimisé pour GesComPro_LibTam avec gestion des performances
"""

import os
import sys
import time

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.button import MDRaisedButton, MDIconButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.toolbar import MDTopAppBar
from database.db_manager import DatabaseManager

class OptimizedGesComApp(MDApp):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.db_manager = None
        self.screen_manager = None
        
    def build(self):
        self.title = "GesComPro_LibTam - Gestion Commerciale"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        print("🔧 Initialisation de l'application optimisée...")
        
        # Initialiser la base de données
        self.init_database()
        
        # Créer le gestionnaire d'écrans
        self.screen_manager = MDScreenManager()
        
        # Créer l'écran principal
        main_screen = self.create_main_screen()
        self.screen_manager.add_widget(main_screen)
        
        print("✅ Application optimisée initialisée")
        return self.screen_manager
    
    def init_database(self):
        """Initialiser la base de données de manière optimisée"""
        try:
            print("🔄 Connexion à la base de données...")
            self.db_manager = DatabaseManager()
            
            if not self.db_manager.connect():
                print("❌ Impossible de se connecter à la base de données")
                return False
            
            if not self.db_manager.initialize_database():
                print("❌ Impossible d'initialiser la base de données")
                return False
            
            print("✅ Base de données initialisée")
            return True
            
        except Exception as e:
            print(f"❌ Erreur base de données: {e}")
            return False
    
    def create_main_screen(self):
        """Créer l'écran principal optimisé"""
        screen = MDScreen(name='main')
        
        # Layout principal
        main_layout = MDBoxLayout(
            orientation='vertical',
            spacing="10dp"
        )
        
        # Barre d'outils
        toolbar = MDTopAppBar(
            title="🏪 GesComPro_LibTam",
            elevation=2
        )
        
        # Contenu principal
        content_layout = MDBoxLayout(
            orientation='vertical',
            padding="20dp",
            spacing="20dp"
        )
        
        # Titre de bienvenue
        welcome_card = MDCard(
            MDBoxLayout(
                MDLabel(
                    text="🎯 Tableau de Bord",
                    font_style="H4",
                    halign="center",
                    theme_text_color="Primary"
                ),
                MDLabel(
                    text="Système de Gestion Commerciale Professionnel",
                    font_style="Body1",
                    halign="center",
                    theme_text_color="Secondary"
                ),
                orientation='vertical',
                padding="20dp",
                spacing="10dp"
            ),
            size_hint_y=None,
            height="120dp",
            elevation=1
        )
        
        # Grille des fonctionnalités principales
        functions_grid = MDGridLayout(
            cols=2,
            spacing="16dp",
            size_hint_y=None,
            height="300dp",
            adaptive_height=True
        )
        
        # Boutons des fonctionnalités
        functions = [
            ("📦 Produits", "Gestion des produits et stock", self.open_products),
            ("👥 Clients", "Gestion de la clientèle", self.open_clients),
            ("🛒 Ventes", "Système de vente et facturation", self.open_sales),
            ("📊 Rapports", "Analyses et statistiques", self.open_reports)
        ]
        
        for title, description, callback in functions:
            card = self.create_function_card(title, description, callback)
            functions_grid.add_widget(card)
        
        # Statistiques rapides
        stats_card = self.create_stats_card()
        
        # Assembler le layout
        content_layout.add_widget(welcome_card)
        content_layout.add_widget(functions_grid)
        content_layout.add_widget(stats_card)
        
        main_layout.add_widget(toolbar)
        main_layout.add_widget(content_layout)
        
        screen.add_widget(main_layout)
        return screen
    
    def create_function_card(self, title, description, callback):
        """Créer une carte de fonctionnalité"""
        card = MDCard(
            MDBoxLayout(
                MDLabel(
                    text=title,
                    font_style="H6",
                    halign="center",
                    theme_text_color="Primary"
                ),
                MDLabel(
                    text=description,
                    font_style="Caption",
                    halign="center",
                    theme_text_color="Secondary"
                ),
                MDRaisedButton(
                    text="Ouvrir",
                    size_hint_y=None,
                    height="36dp",
                    on_release=callback
                ),
                orientation='vertical',
                padding="16dp",
                spacing="8dp"
            ),
            size_hint_y=None,
            height="140dp",
            elevation=2,
            on_release=callback
        )
        return card
    
    def create_stats_card(self):
        """Créer la carte des statistiques"""
        stats_layout = MDBoxLayout(
            orientation='horizontal',
            spacing="20dp"
        )
        
        # Statistiques simulées (à remplacer par de vraies données)
        stats = [
            ("📦", "Produits", "0"),
            ("👥", "Clients", "0"),
            ("🛒", "Ventes", "0 DH"),
            ("⚠️", "Alertes", "0")
        ]
        
        for icon, label, value in stats:
            stat_box = MDBoxLayout(
                MDLabel(
                    text=icon,
                    font_style="H4",
                    halign="center"
                ),
                MDLabel(
                    text=label,
                    font_style="Caption",
                    halign="center",
                    theme_text_color="Secondary"
                ),
                MDLabel(
                    text=value,
                    font_style="H6",
                    halign="center",
                    theme_text_color="Primary"
                ),
                orientation='vertical',
                spacing="4dp"
            )
            stats_layout.add_widget(stat_box)
        
        stats_card = MDCard(
            stats_layout,
            size_hint_y=None,
            height="100dp",
            elevation=1,
            padding="16dp"
        )
        
        return stats_card
    
    def open_products(self, *args):
        """Ouvrir l'écran des produits de manière optimisée"""
        try:
            print("🔄 Chargement de l'écran produits...")
            
            # Vérifier si l'écran existe déjà
            if not self.screen_manager.has_screen('products'):
                from screens.products_screen import ProductsScreen
                products_screen = ProductsScreen(name='products')
                self.screen_manager.add_widget(products_screen)
                print("✅ Écran produits créé")
            
            self.screen_manager.current = 'products'
            print("✅ Écran produits ouvert")
            
        except Exception as e:
            print(f"❌ Erreur ouverture produits: {e}")
            import traceback
            traceback.print_exc()
    
    def open_clients(self, *args):
        """Ouvrir l'écran des clients"""
        try:
            print("🔄 Chargement de l'écran clients...")
            
            if not self.screen_manager.has_screen('clients'):
                from screens.clients_screen import ClientsScreen
                clients_screen = ClientsScreen(name='clients')
                self.screen_manager.add_widget(clients_screen)
                print("✅ Écran clients créé")
            
            self.screen_manager.current = 'clients'
            print("✅ Écran clients ouvert")
            
        except Exception as e:
            print(f"❌ Erreur ouverture clients: {e}")
            # Créer un écran temporaire
            self.create_temp_screen('clients', '👥 Clients', 'Fonctionnalité en cours de développement')
    
    def open_sales(self, *args):
        """Ouvrir l'écran des ventes"""
        try:
            print("🔄 Chargement de l'écran ventes...")
            
            if not self.screen_manager.has_screen('sales'):
                from screens.sales_screen import SalesScreen
                sales_screen = SalesScreen(name='sales')
                self.screen_manager.add_widget(sales_screen)
                print("✅ Écran ventes créé")
            
            self.screen_manager.current = 'sales'
            print("✅ Écran ventes ouvert")
            
        except Exception as e:
            print(f"❌ Erreur ouverture ventes: {e}")
            self.create_temp_screen('sales', '🛒 Ventes', 'Fonctionnalité en cours de développement')
    
    def open_reports(self, *args):
        """Ouvrir l'écran des rapports"""
        try:
            print("🔄 Chargement de l'écran rapports...")
            
            if not self.screen_manager.has_screen('reports'):
                from screens.reports_screen import ReportsScreen
                reports_screen = ReportsScreen(name='reports')
                self.screen_manager.add_widget(reports_screen)
                print("✅ Écran rapports créé")
            
            self.screen_manager.current = 'reports'
            print("✅ Écran rapports ouvert")
            
        except Exception as e:
            print(f"❌ Erreur ouverture rapports: {e}")
            self.create_temp_screen('reports', '📊 Rapports', 'Fonctionnalité en cours de développement')
    
    def create_temp_screen(self, screen_name, title, message):
        """Créer un écran temporaire pour les fonctionnalités non disponibles"""
        if self.screen_manager.has_screen(screen_name):
            self.screen_manager.current = screen_name
            return
        
        screen = MDScreen(name=screen_name)
        
        layout = MDBoxLayout(
            orientation='vertical',
            padding="20dp",
            spacing="20dp"
        )
        
        # Barre d'outils avec bouton retour
        toolbar = MDTopAppBar(
            title=title,
            elevation=2
            # left_action_items supprimés temporairement pour éviter l'avertissement width_mult
        )
        
        # Message
        message_label = MDLabel(
            text=message,
            font_style="H5",
            halign="center",
            theme_text_color="Secondary"
        )
        
        # Bouton retour
        back_button = MDRaisedButton(
            text="🔙 Retour au tableau de bord",
            size_hint=(None, None),
            size=("300dp", "48dp"),
            pos_hint={'center_x': 0.5},
            on_release=self.go_back
        )
        
        layout.add_widget(toolbar)
        layout.add_widget(message_label)
        layout.add_widget(back_button)
        
        screen.add_widget(layout)
        self.screen_manager.add_widget(screen)
        self.screen_manager.current = screen_name
    
    def go_back(self, *args):
        """Retourner au tableau de bord"""
        self.screen_manager.current = 'main'
    
    def on_stop(self):
        """Nettoyer les ressources à la fermeture"""
        if self.db_manager and self.db_manager.connection:
            self.db_manager.disconnect()
            print("🔒 Base de données fermée proprement")

def main():
    """Fonction principale optimisée"""
    print("🚀 LANCEMENT DE GESCOMPRO_LIBTAM OPTIMISÉ")
    print("=" * 55)
    print("🏪 Système de Gestion Commerciale")
    print("⚡ Version optimisée pour de meilleures performances")
    print("🛒 Système de ventes opérationnel")
    print("🪙 Devise: Dirham Marocain (DH)")
    print("=" * 55)
    
    start_time = time.time()
    
    try:
        app = OptimizedGesComApp()
        
        # Mesurer le temps de démarrage
        init_time = time.time() - start_time
        print(f"⚡ Application initialisée en {init_time:.2f}s")
        
        app.run()
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()
        input("\nAppuyez sur Entrée pour quitter...")

if __name__ == "__main__":
    main()