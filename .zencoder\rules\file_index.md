# GesComPro_LibTam - File Index

## Core Application Files

### Main Entry Points
- **`main.py`** - Main application class (GesComApp) with MDApp, navigation drawer, screen manager
- **`launch.py`** - Alternative launch script
- **`launch_final.py`** - Final optimized launch script
- **`launch_optimized.py`** - Performance-optimized launcher
- **`launch_safe.py`** - Safe mode launcher
- **`launch_simple.py`** - Simplified launcher
- **`launch_stable.py`** - Stable version launcher
- **`launch_ventes.py`** - Sales-focused launcher

### Configuration
- **`config.py`** - Global application configuration
- **`config/currency_config.py`** - Currency settings (Dirham DH)
- **`requirements.txt`** - Python dependencies list

### Build and Deployment
- **`build_exe.py`** - PyInstaller script for Windows executable
- **`install.py`** - Installation script
- **`GesComPro.bat`** - Windows batch launcher
- **`GesComPro_LibTam.bat`** - Alternative batch launcher
- **`Test.bat`** - Test runner batch file

## Database Layer

### Core Database Management
- **`database/db_manager.py`** - Main DatabaseManager class with singleton pattern
  - Connection management
  - CRUD operations for all entities
  - Database initialization and optimization
  - Sample data creation

### Database Files
- **`data/gescom.db`** - SQLite database file (auto-created)

## User Interface Screens

### Main Application Screens
- **`screens/dashboard_screen.py`** - DashboardScreen class
  - Statistics overview
  - Quick actions
  - Sales summary
  - Stock alerts

- **`screens/clients_screen.py`** - ClientsScreen class
  - Client list and management
  - ClientFormDialog for add/edit
  - Search and filtering
  - Client details view

- **`screens/products_screen.py`** - ProductsScreen class
  - Product catalog management
  - ProductFormDialog for add/edit
  - Stock management
  - Category integration
  - Barcode support

- **`screens/categories_screen.py`** - CategoriesScreen class
  - Category hierarchy management
  - CRUD operations
  - Product-category relationships

- **`screens/sales_screen.py`** - SalesScreen class
  - Sales list and management
  - SaleFormDialog for creating sales
  - ProductSelectionDialog for product picker
  - SaleViewDialog for viewing sales
  - Invoice printing functionality
  - Custom dropdown components

- **`screens/reports_screen.py`** - ReportsScreen class
  - Sales analytics
  - Chart generation
  - Period-based reports
  - Export functionality

- **`screens/settings_screen.py`** - SettingsScreen class
  - Application configuration
  - CompanyInfoDialog for company settings
  - BackupDialog for data backup
  - Theme management

### Screen Variants and Backups
- **`screens/products_screen_backup.py`** - Backup version of products screen
- **`screens/products_screen_fixed.py`** - Fixed version of products screen

## Forms and Dialogs

### Sales Forms
- **`forms/sales_form.py`** - SalesFormDialog class
  - Main sales creation dialog
  - Client selection
  - Product selection with quantities
  - Payment method selection
  - Total calculations

- **`forms/sales_form_fixed.py`** - SalesFormDialogFixed class
  - Fixed version of sales form
  - Enhanced validation
  - Improved UI layout

- **`sales_form_improved.py`** - ImprovedSaleFormDialog class
  - Enhanced sales form with better UX

### Category Forms
- **`forms/category_form.py`** - CategoryFormDialog class
  - Category creation and editing
  - Validation and error handling

## Models

### Data Models
- **`models/category_model.py`** - Category data model
  - Category entity definition
  - Business logic for categories

## Utilities

### Helper Functions
- **`utils/helpers.py`** - General utility functions
  - Currency formatting
  - Email validation
  - Date/time utilities
  - String manipulation

- **`utils/pdf_generator.py`** - PDFGenerator class
  - Invoice PDF generation
  - Report PDF creation
  - Document formatting

- **`utils/barcode_utils.py`** - Barcode utilities
  - BarcodeGenerator class
  - BarcodeValidator class
  - Barcode format support

- **`utils/chart_fallback.py`** - Chart generation utilities
  - Matplotlib integration
  - Chart rendering fallbacks

## Test Files

### Application Tests
- **`test_app.py`** - Main application test suite
- **`test_app_sales_form.py`** - Sales form integration tests
- **`test_main_app_sales.py`** - Main app sales functionality tests

### Database Tests
- **`test_db_simple.py`** - Basic database tests
- **`test_performance_db.py`** - Database performance tests
- **`test_correction_db.py`** - Database correction tests

### Sales Tests
- **`test_sales_form_complete.py`** - Complete sales form tests
- **`test_sales_form_display.py`** - Sales form display tests
- **`test_sales_form_improved.py`** - Improved sales form tests
- **`test_sales_form_simple.py`** - Simple sales form tests
- **`test_sales_form_with_products.py`** - Sales form with products tests
- **`test_sales_integration_final.py`** - Final sales integration tests
- **`test_sales_screen_complete.py`** - Complete sales screen tests
- **`test_ventes_ameliorees.py`** - Enhanced sales tests
- **`test_ventes_complet.py`** - Complete sales tests
- **`test_ventes_final.py`** - Final sales tests

### Client Tests
- **`test_clients.py`** - Client management tests
- **`test_client_dropdown.py`** - Client dropdown tests
- **`test_client_edit.py`** - Client editing tests
- **`test_client_form.py`** - Client form tests
- **`test_client_modification.py`** - Client modification tests
- **`test_client_ui.py`** - Client UI tests

### Category Tests
- **`test_categories_crud.py`** - Category CRUD tests
- **`test_categories_final.py`** - Final category tests
- **`test_category_form_display.py`** - Category form display tests

### Form Tests
- **`test_formulaire_actuel.py`** - Current form tests
- **`test_formulaire_corrige.py`** - Corrected form tests
- **`test_formulaire_deux_colonnes.py`** - Two-column form tests
- **`test_formulaire_liste.py`** - Form list tests
- **`test_formulaire_simple.py`** - Simple form tests
- **`test_nouveau_formulaire.py`** - New form tests
- **`test_form_quick.py`** - Quick form tests
- **`test_form_size.py`** - Form size tests
- **`test_hauteur_formulaire.py`** - Form height tests

### Integration Tests
- **`test_integration_finale.py`** - Final integration tests
- **`test_liaison_complete.py`** - Complete linking tests
- **`test_final.py`** - Final comprehensive tests
- **`test_final_formulaire.py`** - Final form tests
- **`test_final_resolution.py`** - Final resolution tests

### UI Tests
- **`test_champs_visibles_final.py`** - Visible fields tests
- **`test_scrollbar_desactivee.py`** - Scrollbar deactivation tests
- **`test_simple_dialog.py`** - Simple dialog tests
- **`test_diagnostic_formulaire.py`** - Form diagnostic tests

### Specialized Tests
- **`test_delete_both_statuses.py`** - Status deletion tests
- **`test_delete_sale_button.py`** - Sale deletion button tests
- **`test_fixed_sales_form.py`** - Fixed sales form tests
- **`test_new_sales_form.py`** - New sales form tests

## Debug and Analysis Files

### Debug Scripts
- **`debug_sales_dialog.py`** - Sales dialog debugging
- **`debug_sales_import.py`** - Sales import debugging
- **`debug_sales_screen_warning.py`** - Sales screen warning debugging
- **`debug_width_mult_warning.py`** - Width multiplier warning debugging

### Analysis Scripts
- **`analyse_base_donnees.py`** - Database analysis
- **`check_categories_table.py`** - Categories table checker
- **`check_clients_table.py`** - Clients table checker
- **`comparaison_performances.py`** - Performance comparison
- **`diagnostic_performance.py`** - Performance diagnostics

### Optimization Scripts
- **`optimiser_db_automatique.py`** - Automatic database optimization
- **`optimiser_sqlite.py`** - SQLite optimization
- **`fix_matplotlib.py`** - Matplotlib fixes
- **`suppress_width_mult_completely.py`** - Width multiplier warning suppression

## Data Files

### Test Data
- **`afficher_donnees_test.py`** - Test data display
- **`ajouter_donnees_test.py`** - Test data addition
- **`ajouter_ventes_test.py`** - Test sales data addition

### Export Files
- **`produits_*.csv`** - Product export files (multiple timestamps)

## Demo and Example Files

### Demo Applications
- **`demo_categories_complete.py`** - Complete categories demo
- **`demo_client_form.py`** - Client form demo
- **`demo_liaison_finale.py`** - Final linking demo

## Documentation Files

### Implementation Guides
- **`AMELIORATIONS_VENTES.md`** - Sales improvements guide
- **`AMELIORATION_FORMULAIRE_CLIENT.md`** - Client form improvement guide
- **`AMELIORATION_FORMULAIRE_DEUX_COLONNES.md`** - Two-column form guide
- **`CATEGORIES_CRUD_SUMMARY.md`** - Categories CRUD summary
- **`CATEGORIES_SYSTEM_COMPLETE.md`** - Complete categories system guide
- **`CRUD_CATEGORIES_GUIDE.md`** - Categories CRUD guide
- **`FORMULAIRE_PRODUIT_GUIDE.md`** - Product form guide
- **`FORMULAIRE_PRODUIT_RESUME.md`** - Product form summary
- **`GUIDE_*.md`** - Various feature guides
- **`IMPLEMENTATION_COMPLETE.md`** - Complete implementation guide
- **`LIAISON_CATEGORIES_PRODUITS_GUIDE.md`** - Category-product linking guide
- **`LISTE_CATEGORIES_GUIDE.md`** - Categories list guide
- **`PRODUCTS_INTEGRATION_FINAL.md`** - Final products integration guide
- **`VENTES_ACTIVEES_GUIDE.md`** - Sales activation guide
- **`VENTES_RESUME.md`** - Sales summary

### Bug Fixes and Corrections
- **`CORRECTIONS_APPLIQUEES.md`** - Applied corrections
- **`CORRECTIONS_MATPLOTLIB_RESUME.md`** - Matplotlib corrections summary
- **`CORRECTION_*.md`** - Various correction documents
- **`CLIENT_DROPDOWN_FIX.md`** - Client dropdown fix
- **`DELETE_EXTENDED_STATUSES.md`** - Extended statuses deletion
- **`DELETE_SALE_BUTTON_IMPLEMENTATION.md`** - Sale button deletion implementation
- **`FORMS_DISPLAY_FIX.md`** - Forms display fix
- **`FORM_SIZE_FIX.md`** - Form size fix
- **`SALES_DIALOG_FINAL_FIX.md`** - Sales dialog final fix
- **`SALES_FORM_*.md`** - Sales form fixes
- **`WIDTH_MULT_FIX_COMPLETE.md`** - Width multiplier fix

### Optimization and Performance
- **`OPTIMISATIONS_CATEGORIES.md`** - Categories optimizations
- **`OPTIMISATIONS_DB_RESUME.md`** - Database optimizations summary
- **`RAPPORT_OPTIMISATION_CATEGORIES.md`** - Categories optimization report
- **`RESUME_OPTIMISATION_CATEGORIES.md`** - Categories optimization summary
- **`RESUME_REFACTORING_CATEGORIES.md`** - Categories refactoring summary

### General Documentation
- **`README.md`** - Main project documentation
- **`RESUME_APPLICATION.md`** - Application summary
- **`RESUME_SALES_IMPROVEMENTS.md`** - Sales improvements summary
- **`CHANGEMENTS_BRANDING.md`** - Branding changes
- **`NOUVEAU_NOM_APPLICATION.md`** - New application name
- **`DONNEES_TEST_GUIDE.md`** - Test data guide
- **`GESTION_CLIENTS_ACTIVEE.md`** - Client management activation
- **`VALIDATION_HAUTEUR_FORMULAIRE.md`** - Form height validation

## Directory Structure

### Data Directories
- **`data/`** - Application data and database
- **`backups/`** - Database backups
- **`exports/`** - Exported files
- **`exports/pdf/`** - PDF exports

### Python Package Directories
- **`__pycache__/`** - Python bytecode cache
- **`screens/__pycache__/`** - Screen modules cache
- **`database/__pycache__/`** - Database modules cache
- **`forms/__pycache__/`** - Forms modules cache
- **`models/__pycache__/`** - Models modules cache
- **`utils/__pycache__/`** - Utils modules cache
- **`config/__pycache__/`** - Config modules cache

### Virtual Environment
- **`.venv/`** - Python virtual environment
  - **`.venv/Scripts/`** - Virtual environment executables
  - **`.venv/Lib/`** - Virtual environment libraries

### Configuration
- **`.zencoder/`** - Zencoder configuration
- **`.zencoder/rules/`** - Zencoder rules directory

## File Patterns and Naming Conventions

### Test Files
- Pattern: `test_*.py`
- Purpose: Unit tests, integration tests, UI tests
- Location: Root directory

### Screen Files
- Pattern: `*_screen.py`
- Purpose: UI screen implementations
- Location: `screens/` directory

### Form Files
- Pattern: `*_form.py`
- Purpose: Dialog and form implementations
- Location: `forms/` directory

### Documentation Files
- Pattern: `*.md`
- Purpose: Implementation guides, fixes, summaries
- Location: Root directory

### Launch Scripts
- Pattern: `launch*.py`
- Purpose: Application launchers with different configurations
- Location: Root directory

### Debug Scripts
- Pattern: `debug_*.py`
- Purpose: Debugging and analysis tools
- Location: Root directory

### Demo Scripts
- Pattern: `demo_*.py`
- Purpose: Demonstration and example applications
- Location: Root directory

This comprehensive file index provides a complete overview of all files in the GesComPro_LibTam repository, their purposes, and organizational structure.