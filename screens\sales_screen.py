"""
Écran de gestion des ventes
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import MDTextField
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.list import MDList, OneLineAvatarIconListItem, IconLeftWidget, IconRightWidget, OneLineListItem
from kivymd.uix.selectioncontrol import MDCheckbox

from kivymd.app import MDApp
from kivy.clock import Clock
from datetime import datetime
import threading
import os
import tempfile
import webbrowser
import subprocess
import win32print
import win32api
import win32gui
import win32con
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.lib import colors


class CustomDropdown(MDRaisedButton):
    """Bouton personnalisé qui simule une liste déroulante"""
    
    def __init__(self, items=None, default_text="Sélectionner...", on_select=None, **kwargs):
        super().__init__(**kwargs)
        self.items = items or []
        self.default_text = default_text
        self.on_select_callback = on_select
        self.selected_value = None
        
        # Configuration du bouton
        self.text = default_text
        self.size_hint_y = None
        self.height = "48dp"
        
        # Créer le menu
        self.setup_menu()
    
    def setup_menu(self):
        """Configurer le menu déroulant"""
        menu_items = []
        for item in self.items:
            if isinstance(item, dict):
                text = item.get('text', str(item))
                value = item.get('value', item)
            else:
                text = str(item)
                value = item
            
            menu_items.append({
                "text": text,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=value, t=text: self.select_item(x, t)
            })
        
        self.menu = MDDropdownMenu(
            caller=self,
            items=menu_items,
            max_height="200dp",
            width_mult=4
        )
        
        self.bind(on_release=lambda x: self.menu.open())
    
    def select_item(self, value, text):
        """Sélectionner un élément"""
        self.selected_value = value
        self.text = text
        self.menu.dismiss()
        
        if self.on_select_callback:
            self.on_select_callback(value)
    
    def set_items(self, items):
        """Mettre à jour les éléments de la liste"""
        self.items = items
        self.setup_menu()
    
    def set_selection(self, value, text=None):
        """Définir la sélection par programmation"""
        self.selected_value = value
        if text:
            self.text = text
        else:
            # Chercher le texte correspondant à la valeur
            for item in self.items:
                if isinstance(item, dict):
                    if item.get('value') == value:
                        self.text = item.get('text', str(value))
                        break
                elif item == value:
                    self.text = str(value)
                    break


class SaleCard(MDCard):
    """Carte pour afficher une vente"""
    
    def __init__(self, sale_data, on_edit_callback, on_delete_callback, on_change_status_callback=None, on_view_callback=None, on_print_callback=None, **kwargs):
        super().__init__(**kwargs)
        self.sale_data = sale_data
        self.elevation = 2
        self.padding = "6dp"
        self.size_hint_y = None
        self.height = "140dp"
        self.spacing = "8dp"
        
        layout = MDBoxLayout(orientation='vertical', spacing="2dp")
        
        # En-tête avec numéro de facture et actions
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="32dp")
        
        # Numéro de facture
        numero_facture = sale_data.get('numero_facture', 'N/A')
        
        facture_label = MDLabel(
            text=f"Facture: {numero_facture}",
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        
        # Boutons d'action
        actions_layout = MDBoxLayout(orientation='horizontal', size_hint_x=0.3, spacing="4dp")
        
        # Bouton de consultation
        if on_view_callback:
            view_btn = MDIconButton(
                icon="eye",
                theme_icon_color="Secondary",
                on_release=lambda x: on_view_callback(sale_data)
            )
            actions_layout.add_widget(view_btn)
        
        # Bouton d'impression
        if on_print_callback:
            print_btn = MDIconButton(
                icon="printer",
                theme_icon_color="Secondary",
                on_release=lambda x: on_print_callback(sale_data)
            )
            actions_layout.add_widget(print_btn)
        
        edit_btn = MDIconButton(
            icon="pencil",
            theme_icon_color="Primary",
            on_release=lambda x: on_edit_callback(sale_data)
        )
        
        # Bouton de changement d'état
        if on_change_status_callback:
            status_btn = MDIconButton(
                icon="swap-horizontal",
                theme_icon_color="Secondary",
                on_release=lambda x: on_change_status_callback(sale_data)
            )
            actions_layout.add_widget(status_btn)
        
        actions_layout.add_widget(edit_btn)
        
        # Bouton de suppression seulement pour les ventes "En cours" et "Annulées"
        statut = sale_data.get('statut', '')
        if statut in ['En cours', 'Annulée']:
            delete_btn = MDIconButton(
                icon="delete",
                theme_icon_color="Error",
                on_release=lambda x: on_delete_callback(sale_data)
            )
            actions_layout.add_widget(delete_btn)
        
        header_layout.add_widget(facture_label)
        header_layout.add_widget(actions_layout)
        
        # Informations de la vente
        info_layout = MDBoxLayout(orientation='vertical', spacing="2dp")
        
        # Client
        if sale_data.get('client_nom'):
            client_label = MDLabel(
                text=f"Client: {sale_data['client_nom']}",
                font_style="Caption",
                theme_text_color="Secondary"
            )
            info_layout.add_widget(client_label)
        
        # Date de vente
        if sale_data.get('date_vente'):
            date_vente = sale_data['date_vente']
            if isinstance(date_vente, str):
                try:
                    date_obj = datetime.fromisoformat(date_vente.replace('Z', '+00:00'))
                    date_formatted = date_obj.strftime('%d/%m/%Y %H:%M')
                except:
                    date_formatted = str(date_vente)
            else:
                date_formatted = str(date_vente)
            
            date_label = MDLabel(
                text=f"Date: {date_formatted}",
                font_style="Caption",
                theme_text_color="Secondary"
            )
            info_layout.add_widget(date_label)
        
        # Montants
        montant_layout = MDBoxLayout(orientation='horizontal', spacing="10dp", size_hint_y=None, height="20dp")
        
        montant_ht = sale_data.get('montant_ht', 0)
        montant_ttc = sale_data.get('montant_ttc', 0)
        
        if montant_ht:
            ht_label = MDLabel(
                text=f"HT: {montant_ht:.2f} DH",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_x=0.5
            )
            montant_layout.add_widget(ht_label)
        
        if montant_ttc:
            ttc_label = MDLabel(
                text=f"TTC: {montant_ttc:.2f} DH",
                font_style="Caption",
                theme_text_color="Primary",
                size_hint_x=0.5
            )
            montant_layout.add_widget(ttc_label)
        
        if montant_ht or montant_ttc:
            info_layout.add_widget(montant_layout)
        
        # Statut
        if sale_data.get('statut'):
            statut_label = MDLabel(
                text=f"Statut: {sale_data['statut']}",
                font_style="Caption",
                theme_text_color="Secondary"
            )
            info_layout.add_widget(statut_label)
        
        layout.add_widget(header_layout)
        layout.add_widget(info_layout)
        self.add_widget(layout)


class ProductLineCard(MDCard):
    """Carte pour afficher une ligne de produit dans la vente"""
    
    def __init__(self, product_data, quantity=1, on_remove_callback=None, on_quantity_change=None, **kwargs):
        super().__init__(**kwargs)
        self.product_data = product_data
        self.quantity = quantity
        self.on_remove_callback = on_remove_callback
        self.on_quantity_change = on_quantity_change
        
        self.elevation = 1
        self.padding = "8dp"
        self.size_hint_y = None
        self.height = "80dp"
        self.spacing = "4dp"
        
        layout = MDBoxLayout(orientation='horizontal', spacing="8dp")
        
        # Informations du produit
        product_info = MDBoxLayout(orientation='vertical', size_hint_x=0.5)
        
        product_name = MDLabel(
            text=product_data.get('nom', 'Produit inconnu'),
            font_style="Subtitle2",
            theme_text_color="Primary"
        )
        
        prix_unitaire = product_data.get('prix_vente', 0)
        product_price = MDLabel(
            text=f"Prix: {prix_unitaire:.2f} DH",
            font_style="Caption",
            theme_text_color="Secondary"
        )
        
        product_info.add_widget(product_name)
        product_info.add_widget(product_price)
        
        # Quantité
        quantity_layout = MDBoxLayout(orientation='horizontal', size_hint_x=0.3, spacing="4dp")
        
        minus_btn = MDIconButton(
            icon="minus",
            theme_icon_color="Primary",
            on_release=self.decrease_quantity
        )
        
        self.quantity_field = MDTextField(
            text=str(quantity),
            input_filter="int",
            size_hint_x=None,
            width="60dp",
            on_text=self.on_quantity_text_change
        )
        
        plus_btn = MDIconButton(
            icon="plus",
            theme_icon_color="Primary",
            on_release=self.increase_quantity
        )
        
        quantity_layout.add_widget(minus_btn)
        quantity_layout.add_widget(self.quantity_field)
        quantity_layout.add_widget(plus_btn)
        
        # Total et suppression
        actions_layout = MDBoxLayout(orientation='vertical', size_hint_x=0.2)
        
        self.total_label = MDLabel(
            text=f"{prix_unitaire * quantity:.2f} DH",
            font_style="Subtitle2",
            theme_text_color="Primary",
            halign="center"
        )
        
        remove_btn = MDIconButton(
            icon="delete",
            theme_icon_color="Error",
            on_release=self.remove_product
        )
        
        actions_layout.add_widget(self.total_label)
        actions_layout.add_widget(remove_btn)
        
        layout.add_widget(product_info)
        layout.add_widget(quantity_layout)
        layout.add_widget(actions_layout)
        
        self.add_widget(layout)
    
    def increase_quantity(self, *args):
        """Augmenter la quantité"""
        self.quantity += 1
        self.quantity_field.text = str(self.quantity)
        self.update_total()
        if self.on_quantity_change:
            self.on_quantity_change()
    
    def decrease_quantity(self, *args):
        """Diminuer la quantité"""
        if self.quantity > 1:
            self.quantity -= 1
            self.quantity_field.text = str(self.quantity)
            self.update_total()
            if self.on_quantity_change:
                self.on_quantity_change()
    
    def on_quantity_text_change(self, instance, text):
        """Changement de quantité par saisie"""
        try:
            new_quantity = int(text) if text else 1
            if new_quantity < 1:
                new_quantity = 1
                self.quantity_field.text = "1"
            self.quantity = new_quantity
            self.update_total()
            if self.on_quantity_change:
                self.on_quantity_change()
        except ValueError:
            self.quantity_field.text = str(self.quantity)
    
    def update_total(self):
        """Mettre à jour le total de la ligne"""
        prix_unitaire = self.product_data.get('prix_vente', 0)
        total = prix_unitaire * self.quantity
        self.total_label.text = f"{total:.2f} DH"
    
    def remove_product(self, *args):
        """Supprimer le produit de la vente"""
        if self.on_remove_callback:
            self.on_remove_callback(self)
    
    def get_line_data(self):
        """Obtenir les données de la ligne"""
        prix_unitaire = self.product_data.get('prix_vente', 0)
        return {
            'produit_id': self.product_data.get('id'),
            'nom': self.product_data.get('nom'),
            'quantite': self.quantity,
            'prix_unitaire': prix_unitaire,
            'montant_ligne': prix_unitaire * self.quantity
        }


class ProductSelectionDialog(MDDialog):
    """Dialog pour sélectionner un produit à ajouter"""
    
    def __init__(self, products, on_select_callback=None, **kwargs):
        self.products = products
        self.on_select_callback = on_select_callback
        
        # Création de la liste des produits
        products_layout = MDBoxLayout(orientation='vertical', spacing="8dp", adaptive_height=True)
        
        # Barre de recherche
        self.search_field = MDTextField(
            hint_text="Rechercher un produit...",
            icon_left="magnify",
            size_hint_y=None,
            height="56dp",
            on_text=self.filter_products
        )
        
        # Liste des produits
        self.products_scroll = MDScrollView(size_hint_y=None, height="300dp")
        self.products_list = MDBoxLayout(orientation='vertical', spacing="4dp", adaptive_height=True)
        self.products_scroll.add_widget(self.products_list)
        
        products_layout.add_widget(self.search_field)
        products_layout.add_widget(self.products_scroll)
        
        # Initialiser la liste
        self.filtered_products = self.products
        self.update_products_list()
        
        buttons = [
            MDFlatButton(
                text="ANNULER",
                on_release=self.dismiss
            )
        ]
        
        super().__init__(
            title="Sélectionner un produit",
            type="custom",
            content_cls=products_layout,
            buttons=buttons,
            size_hint=(0.8, None),
            height="450dp",
            **kwargs
        )
    
    def filter_products(self, instance, text):
        """Filtrer les produits selon le texte de recherche"""
        if not text.strip():
            self.filtered_products = self.products
        else:
            search_text = text.lower()
            self.filtered_products = []
            
            for product in self.products:
                searchable_fields = [
                    product.get('nom', ''),
                    product.get('reference', ''),
                    product.get('description', '')
                ]
                
                if any(search_text in field.lower() for field in searchable_fields if field):
                    self.filtered_products.append(product)
        
        self.update_products_list()
    
    def update_products_list(self):
        """Mettre à jour la liste des produits"""
        self.products_list.clear_widgets()
        
        for product in self.filtered_products:
            if product.get('stock_actuel', 0) > 0:  # Seulement les produits en stock
                product_item = OneLineListItem(
                    text=f"{product.get('nom', '')} - {product.get('prix_vente', 0):.2f} DH (Stock: {product.get('stock_actuel', 0)})",
                    on_release=lambda x, p=product: self.select_product(p)
                )
                self.products_list.add_widget(product_item)
    
    def select_product(self, product):
        """Sélectionner un produit"""
        if self.on_select_callback:
            self.on_select_callback(product)
        self.dismiss()


class SaleViewDialog(MDDialog):
    """Dialog pour consulter une vente en mode lecture seule"""
    
    def __init__(self, sale_data, **kwargs):
        self.sale_data = sale_data
        
        # Charger les données nécessaires
        self.load_clients()
        self.load_sale_details()
        
        # Création du contenu principal
        main_layout = MDBoxLayout(orientation='vertical', spacing="8dp", adaptive_height=True)
        
        # Section informations générales
        info_section = self.create_info_section()
        main_layout.add_widget(info_section)
        
        # Section produits
        products_section = self.create_products_section()
        main_layout.add_widget(products_section)
        
        # Section totaux
        totals_section = self.create_totals_section()
        main_layout.add_widget(totals_section)
        
        # Bouton de fermeture
        buttons = [
            MDRaisedButton(
                text="FERMER",
                on_release=self.dismiss
            )
        ]
        
        super().__init__(
            title=f"Consultation - {sale_data.get('numero_facture', 'N/A')}",
            type="custom",
            content_cls=main_layout,
            buttons=buttons,
            size_hint=(0.9, None),
            height="600dp",
            **kwargs
        )
    
    def load_clients(self):
        """Charger la liste des clients"""
        try:
            app = MDApp.get_running_app()
            db_manager = app.db_manager
            
            self.clients_data = db_manager.execute_query("""
                SELECT id, nom, prenom, entreprise 
                FROM clients 
                WHERE actif = 1 
                ORDER BY nom, prenom
            """)
        except Exception as e:
            print(f"Erreur lors du chargement des clients: {e}")
            self.clients_data = []
    
    def load_sale_details(self):
        """Charger les détails de vente"""
        try:
            app = MDApp.get_running_app()
            db_manager = app.db_manager
            
            # Récupérer les détails de vente
            self.details_data = db_manager.execute_query("""
                SELECT dv.*, p.nom as produit_nom, p.prix_vente, p.tva as taux_tva
                FROM details_vente dv
                JOIN produits p ON dv.produit_id = p.id
                WHERE dv.vente_id = ?
                ORDER BY dv.id
            """, (self.sale_data['id'],))
            
        except Exception as e:
            print(f"❌ Erreur lors du chargement des détails de vente: {e}")
            self.details_data = []
    
    def create_info_section(self):
        """Créer la section des informations générales (lecture seule)"""
        section = MDBoxLayout(orientation='vertical', spacing="8dp", size_hint_y=None, height="180dp")
        
        # Titre de section
        section_title = MDLabel(
            text="Informations générales",
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        section.add_widget(section_title)
        
        # Numéro de facture
        numero_label = MDLabel(
            text=f"Numéro de facture: {self.sale_data.get('numero_facture', 'N/A')}",
            font_style="Body1",
            theme_text_color="Primary"
        )
        section.add_widget(numero_label)
        
        # Client
        client_name = self.get_client_display_name()
        client_label = MDLabel(
            text=f"Client: {client_name}",
            font_style="Body1",
            theme_text_color="Primary"
        )
        section.add_widget(client_label)
        
        # Date de vente
        date_label = MDLabel(
            text=f"Date de vente: {self.sale_data.get('date_vente', 'N/A')}",
            font_style="Body1",
            theme_text_color="Primary"
        )
        section.add_widget(date_label)
        
        # Mode de paiement et statut sur la même ligne
        payment_status_layout = MDBoxLayout(orientation='horizontal', spacing="16dp", size_hint_y=None, height="32dp")
        
        mode_paiement_label = MDLabel(
            text=f"Mode de paiement: {self.sale_data.get('mode_paiement', 'N/A')}",
            font_style="Body1",
            theme_text_color="Primary",
            size_hint_x=0.5
        )
        
        statut_label = MDLabel(
            text=f"Statut: {self.sale_data.get('statut', 'N/A')}",
            font_style="Body1",
            theme_text_color="Primary",
            size_hint_x=0.5
        )
        
        payment_status_layout.add_widget(mode_paiement_label)
        payment_status_layout.add_widget(statut_label)
        section.add_widget(payment_status_layout)
        
        return section
    
    def create_products_section(self):
        """Créer la section des produits (lecture seule)"""
        section = MDBoxLayout(orientation='vertical', spacing="8dp", size_hint_y=None, height="200dp")
        
        # Titre de section
        products_title = MDLabel(
            text="Produits commandés",
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        section.add_widget(products_title)
        
        # Liste des produits
        products_scroll = MDScrollView()
        products_layout = MDBoxLayout(orientation='vertical', spacing="4dp", adaptive_height=True)
        
        if self.details_data:
            for detail in self.details_data:
                product_card = self.create_product_card(detail)
                products_layout.add_widget(product_card)
        else:
            no_products_label = MDLabel(
                text="Aucun produit dans cette vente",
                theme_text_color="Secondary",
                halign="center",
                size_hint_y=None,
                height="40dp"
            )
            products_layout.add_widget(no_products_label)
        
        products_scroll.add_widget(products_layout)
        section.add_widget(products_scroll)
        
        return section
    
    def create_product_card(self, detail):
        """Créer une carte produit en mode lecture seule"""
        card = MDCard(
            elevation=1,
            padding="8dp",
            size_hint_y=None,
            height="60dp",
            spacing="4dp"
        )
        
        layout = MDBoxLayout(orientation='horizontal', spacing="8dp")
        
        # Nom du produit
        product_name = MDLabel(
            text=detail.get('produit_nom', 'Produit inconnu'),
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_x=0.4
        )
        
        # Quantité
        quantity_label = MDLabel(
            text=f"Qté: {detail.get('quantite', 0)}",
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_x=0.2
        )
        
        # Prix unitaire
        price_label = MDLabel(
            text=f"Prix: {detail.get('prix_unitaire', 0):.2f} DH",
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_x=0.2
        )
        
        # Total ligne
        total_ligne = detail.get('quantite', 0) * detail.get('prix_unitaire', 0)
        total_label = MDLabel(
            text=f"Total: {total_ligne:.2f} DH",
            font_style="Body2",
            theme_text_color="Primary",
            size_hint_x=0.2
        )
        
        layout.add_widget(product_name)
        layout.add_widget(quantity_label)
        layout.add_widget(price_label)
        layout.add_widget(total_label)
        
        card.add_widget(layout)
        return card
    
    def create_totals_section(self):
        """Créer la section des totaux (lecture seule)"""
        section = MDBoxLayout(orientation='vertical', spacing="4dp", size_hint_y=None, height="100dp")
        
        # Titre de section
        totals_title = MDLabel(
            text="Totaux",
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        section.add_widget(totals_title)
        
        # Montants
        montant_ht = self.sale_data.get('montant_ht', 0)
        montant_tva = self.sale_data.get('montant_tva', 0)
        montant_ttc = self.sale_data.get('montant_ttc', 0)
        
        ht_label = MDLabel(
            text=f"Montant HT: {montant_ht:.2f} DH",
            font_style="Body1",
            theme_text_color="Secondary"
        )
        
        tva_label = MDLabel(
            text=f"TVA: {montant_tva:.2f} DH",
            font_style="Body1",
            theme_text_color="Secondary"
        )
        
        ttc_label = MDLabel(
            text=f"Montant TTC: {montant_ttc:.2f} DH",
            font_style="Subtitle1",
            theme_text_color="Primary"
        )
        
        section.add_widget(ht_label)
        section.add_widget(tva_label)
        section.add_widget(ttc_label)
        
        return section
    
    def get_client_display_name(self):
        """Obtenir le nom d'affichage du client"""
        client_id = self.sale_data.get('client_id')
        if not client_id:
            return 'Client inconnu'
        
        for client in self.clients_data:
            if client.get('id') == client_id:
                nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
                if not nom_complet:
                    nom_complet = client.get('entreprise', f'Client {client_id}')
                return nom_complet
        
        return f'Client {client_id}'


class SaleFormDialog(MDDialog):
    """Dialog pour ajouter/modifier une vente avec gestion des produits"""
    
    def __init__(self, sale_data=None, on_save_callback=None, **kwargs):
        # Vérifier que l'app est initialisée
        app = MDApp.get_running_app()
        if not app:
            raise RuntimeError("L'application MDApp doit être initialisée avant de créer ce dialog")
        
        self.sale_data = sale_data or {}
        self.on_save_callback = on_save_callback
        self.clients_data = []
        self.products_data = []
        self.product_lines = []  # Liste des lignes de produits
        self.selected_client = None
        
        # Charger les données
        self.load_clients()
        self.load_products()
        
        # Générer un numéro de facture automatique si nouveau
        if not self.sale_data.get('numero_facture'):
            self.sale_data['numero_facture'] = self.generate_invoice_number()
        
        # Création du formulaire principal
        main_layout = MDBoxLayout(orientation='vertical', spacing="8dp", adaptive_height=True)
        
        # Section informations générales
        info_section = self.create_info_section()
        main_layout.add_widget(info_section)
        
        # Section produits
        products_section = self.create_products_section()
        main_layout.add_widget(products_section)
        
        # Section totaux
        totals_section = self.create_totals_section()
        main_layout.add_widget(totals_section)
        

        
        # Boutons
        buttons = [
            MDFlatButton(
                text="ANNULER",
                on_release=self.dismiss
            ),
            MDRaisedButton(
                text="ENREGISTRER",
                on_release=self.save_sale
            )
        ]
        
        title = "Modifier la vente" if sale_data else "Nouvelle vente"
        
        super().__init__(
            title=title,
            type="custom",
            content_cls=main_layout,
            buttons=buttons,
            size_hint=(0.95, None),
            height="700dp",
            **kwargs
        )
        
        # Charger les détails de vente si c'est une modification
        if self.sale_data.get('id'):
            Clock.schedule_once(lambda dt: self.load_sale_details_and_update_ui(), 0.1)
    
    def generate_invoice_number(self):
        """Générer un numéro de facture automatique"""
        from datetime import datetime
        now = datetime.now()
        return f"FAC-{now.strftime('%Y%m%d')}-{now.strftime('%H%M%S')}"
    
    def create_info_section(self):
        """Créer la section des informations générales"""
        section = MDBoxLayout(orientation='vertical', spacing="8dp", size_hint_y=None, height="200dp")
        
        # Titre de section
        section_title = MDLabel(
            text="Informations générales",
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        section.add_widget(section_title)
        
        # Fonction helper pour convertir les valeurs en string sécurisé
        def safe_str(value):
            return str(value) if value is not None else ''
        
        # Numéro de facture
        self.numero_facture_field = MDTextField(
            hint_text="Numéro de facture *",
            text=safe_str(self.sale_data.get('numero_facture', '')),
            required=True,
            readonly=True  # Généré automatiquement
        )
        section.add_widget(self.numero_facture_field)
        
        # Client - Liste déroulante
        client_items = []
        for client in self.clients_data:
            nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
            if not nom_complet:
                nom_complet = client.get('entreprise', f"Client {client.get('id', '')}")
            
            client_items.append({
                'text': nom_complet,
                'value': client
            })
        
        self.client_dropdown = CustomDropdown(
            items=client_items,
            default_text="Sélectionner un client *",
            on_select=self.on_client_selected
        )
        
        # Si c'est une modification, sélectionner le client existant
        if self.sale_data.get('client_id'):
            client_name = self.get_client_display_name()
            if client_name:
                self.client_dropdown.text = client_name
        
        section.add_widget(self.client_dropdown)
        
        # Date de vente
        date_vente = self.sale_data.get('date_vente', '')
        if date_vente and isinstance(date_vente, str):
            try:
                date_obj = datetime.fromisoformat(date_vente.replace('Z', '+00:00'))
                date_formatted = date_obj.strftime('%Y-%m-%d %H:%M')
            except:
                date_formatted = str(date_vente)
        else:
            date_formatted = datetime.now().strftime('%Y-%m-%d %H:%M')
        
        self.date_vente_field = MDTextField(
            hint_text="Date de vente (YYYY-MM-DD HH:MM)",
            text=date_formatted,
            helper_text="Format: 2024-01-15 14:30",
            helper_text_mode="on_focus"
        )
        section.add_widget(self.date_vente_field)
        
        # Mode de paiement et statut sur la même ligne
        payment_status_layout = MDBoxLayout(orientation='horizontal', spacing="8dp", size_hint_y=None, height="56dp")
        
        # Mode de paiement
        paiement_items = [
            {'text': 'Espèces', 'value': 'Espèces'},
            {'text': 'Carte bancaire', 'value': 'Carte bancaire'},
            {'text': 'Chèque', 'value': 'Chèque'},
            {'text': 'Virement bancaire', 'value': 'Virement bancaire'},
            {'text': 'Électronique', 'value': 'Électronique'},
            {'text': 'Crédit', 'value': 'Crédit'}
        ]
        
        self.mode_paiement_dropdown = CustomDropdown(
            items=paiement_items,
            default_text="Mode de paiement",
            on_select=self.on_paiement_selected,
            size_hint_x=0.5
        )
        
        # Définir la valeur par défaut ou existante
        mode_paiement = safe_str(self.sale_data.get('mode_paiement', 'Espèces'))
        self.mode_paiement_dropdown.set_selection(mode_paiement, mode_paiement)
        
        # Statut
        statut_items = [
            {'text': 'En cours', 'value': 'En cours'},
            {'text': 'Payée', 'value': 'Payée'},
            {'text': 'Annulée', 'value': 'Annulée'}
        ]
        
        self.statut_dropdown = CustomDropdown(
            items=statut_items,
            default_text="Statut",
            on_select=self.on_statut_selected,
            size_hint_x=0.5
        )
        
        # Définir la valeur par défaut ou existante
        statut = safe_str(self.sale_data.get('statut', 'En cours'))
        self.statut_dropdown.set_selection(statut, statut)
        
        payment_status_layout.add_widget(self.mode_paiement_dropdown)
        payment_status_layout.add_widget(self.statut_dropdown)
        section.add_widget(payment_status_layout)
        
        return section
    
    def create_products_section(self):
        """Créer la section des produits"""
        section = MDBoxLayout(orientation='vertical', spacing="8dp", size_hint_y=None, height="300dp")
        
        # En-tête avec titre et bouton d'ajout
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="40dp")
        
        products_title = MDLabel(
            text="Produits",
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        
        add_product_btn = MDRaisedButton(
            text="Ajouter produit",
            icon="plus",
            on_release=self.add_product,
            size_hint_x=0.3,
            size_hint_y=None,
            height="36dp"
        )
        
        header_layout.add_widget(products_title)
        header_layout.add_widget(add_product_btn)
        section.add_widget(header_layout)
        
        # Liste des produits
        self.products_scroll = MDScrollView()
        self.products_layout = MDBoxLayout(orientation='vertical', spacing="4dp", adaptive_height=True)
        self.products_scroll.add_widget(self.products_layout)
        section.add_widget(self.products_scroll)
        
        return section
    
    def create_totals_section(self):
        """Créer la section des totaux"""
        section = MDBoxLayout(orientation='vertical', spacing="8dp", size_hint_y=None, height="120dp")
        
        # Titre de section
        section_title = MDLabel(
            text="Totaux",
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        section.add_widget(section_title)
        
        # Totaux sur une ligne
        totals_layout = MDBoxLayout(orientation='horizontal', spacing="8dp", size_hint_y=None, height="56dp")
        
        self.montant_ht_field = MDTextField(
            hint_text="Montant HT",
            text="0.00",
            readonly=True,
            size_hint_x=0.33
        )
        
        self.montant_tva_field = MDTextField(
            hint_text="Montant TVA",
            text="0.00",
            readonly=True,
            size_hint_x=0.33
        )
        
        self.montant_ttc_field = MDTextField(
            hint_text="Montant TTC",
            text="0.00",
            readonly=True,
            size_hint_x=0.34
        )
        
        totals_layout.add_widget(self.montant_ht_field)
        totals_layout.add_widget(self.montant_tva_field)
        totals_layout.add_widget(self.montant_ttc_field)
        section.add_widget(totals_layout)
        
        # Notes
        self.notes_field = MDTextField(
            hint_text="Notes (optionnel)",
            text=self.sale_data.get('notes', ''),
            multiline=True,
            max_height="60dp"
        )
        section.add_widget(self.notes_field)
        
        return section
    
    def load_clients(self):
        """Charger la liste des clients"""
        try:
            app = MDApp.get_running_app()
            db_manager = app.db_manager
            
            self.clients_data = db_manager.execute_query("""
                SELECT id, nom, prenom, entreprise 
                FROM clients 
                WHERE actif = 1 
                ORDER BY nom, prenom
            """)
        except Exception as e:
            print(f"Erreur lors du chargement des clients: {e}")
            self.clients_data = []
    
    def load_products(self):
        """Charger la liste des produits"""
        try:
            app = MDApp.get_running_app()
            db_manager = app.db_manager
            
            self.products_data = db_manager.execute_query("""
                SELECT id, nom, description, reference, prix_vente, stock_actuel, tva
                FROM produits 
                WHERE actif = 1 AND stock_actuel > 0
                ORDER BY nom
            """)
        except Exception as e:
            print(f"Erreur lors du chargement des produits: {e}")
            self.products_data = []
    
    def load_sale_details(self):
        """Charger les détails de vente existants"""
        try:
            app = MDApp.get_running_app()
            db_manager = app.db_manager
            
            # Récupérer les détails de vente
            details = db_manager.execute_query("""
                SELECT dv.*, p.nom as produit_nom, p.prix_vente, p.tva as taux_tva
                FROM details_vente dv
                JOIN produits p ON dv.produit_id = p.id
                WHERE dv.vente_id = ?
                ORDER BY dv.id
            """, (self.sale_data['id'],))
            
            # Créer les lignes de produits
            for detail in details:
                # Créer les données du produit pour ProductLineCard
                product_data = {
                    'id': detail['produit_id'],
                    'nom': detail['produit_nom'],
                    'prix_vente': detail['prix_unitaire'],  # Utiliser le prix au moment de la vente
                    'tva': detail['taux_tva']
                }
                
                # Créer la ligne de produit avec la quantité existante
                line = ProductLineCard(
                    product_data=product_data,
                    quantity=detail['quantite'],
                    on_remove=self.remove_product_line,
                    on_quantity_change=self.update_totals
                )
                
                self.product_lines.append(line)
            
            print(f"✅ {len(details)} produits chargés pour la vente {self.sale_data.get('numero_facture', '')}")
            
        except Exception as e:
            print(f"❌ Erreur lors du chargement des détails de vente: {e}")
            import traceback
            traceback.print_exc()
    
    def load_sale_details_and_update_ui(self):
        """Charger les détails de vente et mettre à jour l'interface"""
        self.load_sale_details()
        self.update_products_display()
        self.update_totals()
    
    def update_products_display(self):
        """Mettre à jour l'affichage des produits"""
        # Vider le layout des produits
        self.products_layout.clear_widgets()
        
        # Ajouter chaque ligne de produit
        for line in self.product_lines:
            self.products_layout.add_widget(line)
        
        # Afficher un message si aucun produit
        if not self.product_lines:
            no_products_label = MDLabel(
                text="Aucun produit ajouté",
                theme_text_color="Secondary",
                halign="center",
                size_hint_y=None,
                height="40dp"
            )
            self.products_layout.add_widget(no_products_label)
    
    def get_client_display_name(self):
        """Obtenir le nom d'affichage du client sélectionné"""
        client_id = self.sale_data.get('client_id')
        if not client_id:
            return ''
        
        for client in self.clients_data:
            if client.get('id') == client_id:
                # Sauvegarder le client sélectionné pour la modification
                self.selected_client = client
                nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
                if not nom_complet:
                    nom_complet = client.get('entreprise', f'Client {client_id}')
                return nom_complet
        
        return f'Client {client_id}'
    
    def on_client_selected(self, client):
        """Callback quand un client est sélectionné"""
        self.selected_client = client
    
    def on_paiement_selected(self, mode_paiement):
        """Callback quand un mode de paiement est sélectionné"""
        pass  # La valeur est automatiquement stockée dans le dropdown
    
    def on_statut_selected(self, statut):
        """Callback quand un statut est sélectionné"""
        pass  # La valeur est automatiquement stockée dans le dropdown
    
    def add_product(self, *args):
        """Ajouter un produit à la vente"""
        if not self.products_data:
            error_dialog = MDDialog(
                title="Aucun produit disponible",
                text="Aucun produit en stock n'est disponible pour la vente.",
                buttons=[
                    MDFlatButton(
                        text="OK",
                        on_release=lambda x: error_dialog.dismiss()
                    )
                ]
            )
            error_dialog.open()
            return
        
        # Ouvrir le dialog de sélection de produit
        product_dialog = ProductSelectionDialog(
            products=self.products_data,
            on_select_callback=self.on_product_selected
        )
        product_dialog.open()
    
    def on_product_selected(self, product):
        """Callback quand un produit est sélectionné"""
        # Vérifier si le produit n'est pas déjà dans la liste
        for line in self.product_lines:
            if line.product_data.get('id') == product.get('id'):
                # Produit déjà présent, augmenter la quantité
                line.increase_quantity()
                return
        
        # Ajouter une nouvelle ligne de produit
        product_line = ProductLineCard(
            product_data=product,
            quantity=1,
            on_remove_callback=self.remove_product_line,
            on_quantity_change=self.update_totals
        )
        
        self.product_lines.append(product_line)
        self.products_layout.add_widget(product_line)
        self.update_totals()
    
    def remove_product_line(self, product_line):
        """Supprimer une ligne de produit"""
        if product_line in self.product_lines:
            self.product_lines.remove(product_line)
            self.products_layout.remove_widget(product_line)
            self.update_totals()
    
    def update_totals(self):
        """Mettre à jour les totaux de la vente"""
        total_ht = 0.0
        total_tva = 0.0
        
        for line in self.product_lines:
            line_data = line.get_line_data()
            montant_ligne = line_data['montant_ligne']
            total_ht += montant_ligne
            
            # Calculer la TVA (supposons 20% par défaut)
            tva_rate = line.product_data.get('tva', 20.0) / 100
            tva_ligne = montant_ligne * tva_rate
            total_tva += tva_ligne
        
        total_ttc = total_ht + total_tva
        
        # Mettre à jour les champs
        self.montant_ht_field.text = f"{total_ht:.2f}"
        self.montant_tva_field.text = f"{total_tva:.2f}"
        self.montant_ttc_field.text = f"{total_ttc:.2f}"
    
    def save_sale(self, *args):
        """Enregistrer la vente"""
        # Validation basique
        if not self.numero_facture_field.text.strip():
            self.numero_facture_field.error = True
            self.numero_facture_field.helper_text = "Le numéro de facture est obligatoire"
            return
        
        if not hasattr(self, 'selected_client') or not self.selected_client:
            # Afficher un message d'erreur pour le client
            error_dialog = MDDialog(
                title="Client requis",
                text="Veuillez sélectionner un client avant d'enregistrer la vente.",
                buttons=[
                    MDFlatButton(
                        text="OK",
                        on_release=lambda x: error_dialog.dismiss()
                    )
                ]
            )
            error_dialog.open()
            return
        
        if not self.product_lines:
            error_dialog = MDDialog(
                title="Aucun produit",
                text="Veuillez ajouter au moins un produit à la vente.",
                buttons=[
                    MDFlatButton(
                        text="OK",
                        on_release=lambda x: error_dialog.dismiss()
                    )
                ]
            )
            error_dialog.open()
            return
        
        # Validation des montants
        try:
            montant_ht = float(self.montant_ht_field.text) if self.montant_ht_field.text.strip() else 0.0
            montant_tva = float(self.montant_tva_field.text) if self.montant_tva_field.text.strip() else 0.0
            montant_ttc = float(self.montant_ttc_field.text) if self.montant_ttc_field.text.strip() else 0.0
        except ValueError:
            # Erreur dans la conversion des montants
            return
        
        # Préparer les données de la vente
        sale_data = {
            'numero_facture': self.numero_facture_field.text.strip(),
            'client_id': self.selected_client['id'],
            'date_vente': self.date_vente_field.text.strip(),
            'montant_ht': montant_ht,
            'montant_tva': montant_tva,
            'montant_ttc': montant_ttc,
            'statut': self.statut_dropdown.selected_value or 'En cours',
            'mode_paiement': self.mode_paiement_dropdown.selected_value or 'Espèces',
            'notes': self.notes_field.text.strip(),
            'product_lines': [line.get_line_data() for line in self.product_lines]
        }
        
        # Ajouter l'ID si c'est une modification
        if self.sale_data.get('id'):
            sale_data['id'] = self.sale_data['id']
        
        # Appeler le callback
        if self.on_save_callback:
            self.on_save_callback(sale_data)
        
        self.dismiss()


class SalesScreen(MDScreen):
    """Écran de gestion des ventes"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.sales_data = []
        self.filtered_sales = []
        self.build_ui()
    
    def build_ui(self):
        """Construction de l'interface utilisateur"""
        main_layout = MDBoxLayout(orientation='vertical', padding="16dp", spacing="16dp")
        
        # En-tête avec titre et bouton d'ajout
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="48dp")
        
        title_label = MDLabel(
            text="Gestion des Ventes",
            font_style="H5",
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        
        add_button = MDRaisedButton(
            text="Nouvelle Vente",
            icon="plus",
            on_release=self.add_sale,
            size_hint_x=0.3
        )
        
        header_layout.add_widget(title_label)
        header_layout.add_widget(add_button)
        
        # Barre de recherche
        self.search_field = MDTextField(
            hint_text="Rechercher une vente...",
            icon_left="magnify",
            size_hint_y=None,
            height="56dp",
            on_text=self.filter_sales
        )
        
        # ScrollView pour la liste des ventes
        self.scroll = MDScrollView()
        self.sales_layout = MDBoxLayout(orientation='vertical', spacing="8dp", adaptive_height=True)
        self.scroll.add_widget(self.sales_layout)
        
        # Message quand aucune vente
        self.no_sales_label = MDLabel(
            text="Aucune vente trouvée",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        main_layout.add_widget(header_layout)
        main_layout.add_widget(self.search_field)
        main_layout.add_widget(self.scroll)
        
        self.add_widget(main_layout)
    
    def on_enter(self):
        """Actions à effectuer lors de l'entrée sur l'écran"""
        self.load_sales()
    
    def load_sales(self):
        """Charger la liste des ventes"""
        def load_data():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                # Récupérer toutes les ventes avec les informations du client
                self.sales_data = db_manager.execute_query("""
                    SELECT v.*, 
                           COALESCE(c.prenom || ' ' || c.nom, c.entreprise, 'Client inconnu') as client_nom
                    FROM ventes v
                    LEFT JOIN clients c ON v.client_id = c.id
                    ORDER BY v.date_vente DESC
                """)
                
                # Mettre à jour l'interface
                Clock.schedule_once(self.update_sales_ui, 0)
                
            except Exception as e:
                print(f"Erreur lors du chargement des ventes: {e}")
        
        threading.Thread(target=load_data, daemon=True).start()
    
    def update_sales_ui(self, dt):
        """Mettre à jour l'interface des ventes"""
        self.sales_layout.clear_widgets()
        
        sales_to_show = self.filtered_sales if hasattr(self, 'filtered_sales') and self.filtered_sales else self.sales_data
        
        if not sales_to_show:
            self.sales_layout.add_widget(self.no_sales_label)
        else:
            for sale in sales_to_show:
                sale_card = SaleCard(
                    sale,
                    self.edit_sale,
                    self.delete_sale,
                    self.change_sale_status,
                    self.view_sale,
                    self.print_sale
                )
                self.sales_layout.add_widget(sale_card)
    
    def filter_sales(self, instance, text):
        """Filtrer les ventes selon le texte de recherche"""
        if not text.strip():
            self.filtered_sales = self.sales_data
        else:
            search_text = text.lower()
            self.filtered_sales = []
            
            for sale in self.sales_data:
                # Recherche dans numéro de facture, client, statut
                searchable_fields = [
                    sale.get('numero_facture', ''),
                    sale.get('client_nom', ''),
                    sale.get('statut', ''),
                    sale.get('mode_paiement', '')
                ]
                
                if any(search_text in field.lower() for field in searchable_fields if field):
                    self.filtered_sales.append(sale)
        
        self.update_sales_ui(None)
    
    def add_sale(self, *args):
        """Ajouter une nouvelle vente"""
        try:
            # Vérifier que l'app est bien initialisée
            app = MDApp.get_running_app()
            if not app:
                print("❌ Application non initialisée")
                return
            
            dialog = SaleFormDialog(on_save_callback=self.save_sale)
            dialog.open()
            
        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du dialog d'ajout: {e}")
            import traceback
            traceback.print_exc()
    
    def edit_sale(self, sale_data):
        """Modifier une vente existante"""
        try:
            # Vérifier que l'app est bien initialisée
            app = MDApp.get_running_app()
            if not app:
                print("❌ Application non initialisée")
                return
            
            # Convertir sale_data en dictionnaire si nécessaire
            if hasattr(sale_data, 'keys'):
                sale_dict = dict(sale_data)
            else:
                sale_dict = sale_data
            
            dialog = SaleFormDialog(sale_data=sale_dict, on_save_callback=self.save_sale)
            dialog.open()
            
        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du dialog de modification: {e}")
            import traceback
            traceback.print_exc()
    
    def save_sale(self, sale_data):
        """Enregistrer une vente (nouvelle ou modifiée) avec détails"""
        def save_data():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                # Extraire les lignes de produits
                product_lines = sale_data.pop('product_lines', [])
                
                if sale_data.get('id'):
                    # Modification
                    query = """
                        UPDATE ventes SET 
                        numero_facture = ?, client_id = ?, date_vente = ?, 
                        montant_ht = ?, montant_tva = ?, montant_ttc = ?,
                        statut = ?, mode_paiement = ?, notes = ?
                        WHERE id = ?
                    """
                    params = (
                        sale_data['numero_facture'], sale_data['client_id'], sale_data['date_vente'],
                        sale_data['montant_ht'], sale_data['montant_tva'], sale_data['montant_ttc'],
                        sale_data['statut'], sale_data['mode_paiement'], sale_data['notes'],
                        sale_data['id']
                    )
                    
                    success = db_manager.execute_update(query, params)
                    vente_id = sale_data['id']
                    
                    # Supprimer les anciens détails
                    if success:
                        db_manager.execute_update("DELETE FROM details_vente WHERE vente_id = ?", (vente_id,))
                else:
                    # Nouvelle vente
                    query = """
                        INSERT INTO ventes 
                        (numero_facture, client_id, date_vente, montant_ht, montant_tva, montant_ttc, statut, mode_paiement, notes)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    params = (
                        sale_data['numero_facture'], sale_data['client_id'], sale_data['date_vente'],
                        sale_data['montant_ht'], sale_data['montant_tva'], sale_data['montant_ttc'],
                        sale_data['statut'], sale_data['mode_paiement'], sale_data['notes']
                    )
                    
                    success = db_manager.execute_update(query, params)
                    
                    # Récupérer l'ID de la vente créée
                    if success:
                        result = db_manager.execute_query("SELECT last_insert_rowid() as id")
                        vente_id = result[0]['id'] if result else None
                    else:
                        vente_id = None
                
                # Ajouter les détails de vente
                if success and vente_id and product_lines:
                    for line in product_lines:
                        detail_query = """
                            INSERT INTO details_vente 
                            (vente_id, produit_id, quantite, prix_unitaire, montant_ligne)
                            VALUES (?, ?, ?, ?, ?)
                        """
                        detail_params = (
                            vente_id,
                            line['produit_id'],
                            line['quantite'],
                            line['prix_unitaire'],
                            line['montant_ligne']
                        )
                        db_manager.execute_update(detail_query, detail_params)
                
                if success:
                    # Recharger la liste
                    Clock.schedule_once(lambda dt: self.load_sales(), 0)
                    print(f"✅ Vente {sale_data['numero_facture']} enregistrée avec succès")
                else:
                    print("Erreur lors de l'enregistrement de la vente")
                
            except Exception as e:
                print(f"Erreur lors de l'enregistrement: {e}")
                import traceback
                traceback.print_exc()
        
        threading.Thread(target=save_data, daemon=True).start()
    
    def view_sale(self, sale_data):
        """Consulter une vente en mode lecture seule"""
        try:
            # Convertir les données de vente en dictionnaire si nécessaire
            if hasattr(sale_data, '__getitem__'):
                sale_dict = dict(sale_data)
            else:
                sale_dict = sale_data
            
            # Ouvrir le dialog de consultation
            dialog = SaleViewDialog(sale_data=sale_dict)
            dialog.open()
            
        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du dialog de consultation: {e}")
            import traceback
            traceback.print_exc()
    
    def print_sale(self, sale_data):
        """Imprimer une vente (état ou facture)"""
        try:
            # Vérifier les imprimantes disponibles
            printers = self.get_available_printers()
            if not printers:
                error_dialog = MDDialog(
                    title="Erreur d'impression",
                    text="Aucune imprimante trouvée sur ce système.",
                    buttons=[
                        MDFlatButton(
                            text="OK",
                            on_release=lambda x: error_dialog.dismiss()
                        )
                    ]
                )
                error_dialog.open()
                return
            
            # Créer un dialog pour choisir le type d'impression
            print_buttons = [
                MDRaisedButton(
                    text="📄 État de vente",
                    md_bg_color=(0.2, 0.5, 0.8, 1),
                    on_release=lambda x: self.show_printer_selection(sale_data, 'state', print_dialog)
                ),
                MDRaisedButton(
                    text="🧾 Facture",
                    md_bg_color=(0.2, 0.7, 0.2, 1),
                    on_release=lambda x: self.show_printer_selection(sale_data, 'invoice', print_dialog)
                ),
                MDFlatButton(
                    text="ANNULER",
                    on_release=lambda x: print_dialog.dismiss()
                )
            ]
            
            numero_facture = sale_data.get('numero_facture', 'N/A')
            print_dialog = MDDialog(
                title="Impression",
                text=f"Vente: {numero_facture}\n\nQue souhaitez-vous imprimer ?",
                buttons=print_buttons
            )
            print_dialog.open()
            
        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du dialog d'impression: {e}")
            import traceback
            traceback.print_exc()
    
    def get_available_printers(self):
        """Obtenir la liste des imprimantes disponibles"""
        try:
            printers = []
            printer_enum = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS)
            for printer in printer_enum:
                printers.append(printer[2])  # Nom de l'imprimante
            return printers
        except Exception as e:
            print(f"❌ Erreur lors de l'énumération des imprimantes: {e}")
            return []
    
    def show_printer_selection(self, sale_data, doc_type, parent_dialog):
        """Afficher la sélection d'imprimante"""
        parent_dialog.dismiss()
        
        try:
            printers = self.get_available_printers()
            default_printer = win32print.GetDefaultPrinter()
            
            # Créer les boutons pour chaque imprimante
            printer_buttons = []
            
            for printer in printers:
                is_default = printer == default_printer
                button_text = f"🖨️ {printer}"
                if is_default:
                    button_text += " (Par défaut)"
                
                btn = MDRaisedButton(
                    text=button_text,
                    md_bg_color=(0.2, 0.7, 0.2, 1) if is_default else (0.5, 0.5, 0.5, 1),
                    on_release=lambda x, p=printer: self.print_to_printer(sale_data, doc_type, p, printer_dialog)
                )
                printer_buttons.append(btn)
            
            # Bouton d'annulation
            printer_buttons.append(
                MDFlatButton(
                    text="ANNULER",
                    on_release=lambda x: printer_dialog.dismiss()
                )
            )
            
            numero_facture = sale_data.get('numero_facture', 'N/A')
            doc_name = "État de vente" if doc_type == 'state' else "Facture"
            
            printer_dialog = MDDialog(
                title="Sélection d'imprimante",
                text=f"{doc_name}: {numero_facture}\n\nChoisissez une imprimante:",
                buttons=printer_buttons
            )
            printer_dialog.open()
            
        except Exception as e:
            print(f"❌ Erreur lors de la sélection d'imprimante: {e}")
            import traceback
            traceback.print_exc()
    
    def print_to_printer(self, sale_data, doc_type, printer_name, dialog):
        """Imprimer vers l'imprimante sélectionnée"""
        dialog.dismiss()
        
        def generate_and_print():
            try:
                # Charger les données complètes de la vente
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                # Récupérer les détails de vente
                details = db_manager.execute_query("""
                    SELECT dv.*, p.nom as produit_nom, p.reference, p.prix_vente, p.tva as taux_tva
                    FROM details_vente dv
                    JOIN produits p ON dv.produit_id = p.id
                    WHERE dv.vente_id = ?
                    ORDER BY dv.id
                """, (sale_data['id'],))
                
                # Récupérer les informations du client
                client_info = db_manager.execute_query("""
                    SELECT * FROM clients WHERE id = ?
                """, (sale_data['client_id'],))
                
                client = client_info[0] if client_info else {}
                
                # Générer le PDF
                if doc_type == 'state':
                    pdf_path = self.generate_sale_state_pdf(sale_data, details, client)
                    doc_name = "État de vente"
                else:
                    pdf_path = self.generate_invoice_pdf(sale_data, details, client)
                    doc_name = "Facture"
                
                # Imprimer le PDF
                self.print_pdf_to_printer(pdf_path, printer_name)
                
                print(f"✅ {doc_name} imprimé sur {printer_name} pour {sale_data.get('numero_facture', '')}")
                
                # Nettoyer le fichier temporaire
                try:
                    os.remove(pdf_path)
                except:
                    pass
                
            except Exception as e:
                print(f"❌ Erreur lors de l'impression: {e}")
                import traceback
                traceback.print_exc()
        
        threading.Thread(target=generate_and_print, daemon=True).start()
    
    def print_pdf_to_printer(self, pdf_path, printer_name):
        """Imprimer un fichier PDF vers une imprimante spécifique"""
        try:
            # Méthode 1: Essayer avec Adobe Reader ou un lecteur PDF par défaut
            try:
                win32api.ShellExecute(
                    0,
                    "printto",
                    pdf_path,
                    f'"{printer_name}"',
                    ".",
                    0
                )
                return
            except:
                pass
            
            # Méthode 2: Utiliser SumatraPDF si disponible
            try:
                sumatra_path = self.find_sumatra_pdf()
                if sumatra_path:
                    import subprocess
                    subprocess.run([
                        sumatra_path,
                        "-print-to",
                        printer_name,
                        "-silent",
                        pdf_path
                    ], check=True)
                    return
            except:
                pass
            
            # Méthode 3: Ouvrir le PDF et laisser l'utilisateur imprimer manuellement
            print(f"⚠️ Impression automatique impossible. Ouverture du PDF pour impression manuelle...")
            win32api.ShellExecute(0, "open", pdf_path, "", ".", 1)
            
        except Exception as e:
            print(f"❌ Erreur lors de l'impression PDF: {e}")
            raise
    
    def find_sumatra_pdf(self):
        """Trouver SumatraPDF sur le système"""
        possible_paths = [
            r"C:\Program Files\SumatraPDF\SumatraPDF.exe",
            r"C:\Program Files (x86)\SumatraPDF\SumatraPDF.exe",
            r"C:\Users\<USER>\AppData\Local\SumatraPDF\SumatraPDF.exe".format(os.environ.get('USERNAME', ''))
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None
    
    def generate_sale_state_pdf(self, sale_data, details, client):
        """Générer un PDF pour l'état de vente"""
        try:
            # Créer un fichier temporaire
            temp_dir = tempfile.gettempdir()
            pdf_path = os.path.join(temp_dir, f"etat_vente_{sale_data.get('numero_facture', 'N_A')}.pdf")
            
            # Créer le document PDF
            doc = SimpleDocTemplate(pdf_path, pagesize=A4)
            story = []
            
            # Styles
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # Centré
            )
            
            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12
            )
            
            # Titre
            story.append(Paragraph("ÉTAT DE VENTE", title_style))
            story.append(Paragraph("GESCOM - Système de Gestion Commerciale", styles['Normal']))
            story.append(Spacer(1, 20))
            
            # Informations de la vente
            story.append(Paragraph("Informations de la vente", heading_style))
            vente_info = [
                f"<b>Numéro:</b> {sale_data.get('numero_facture', 'N/A')}",
                f"<b>Date:</b> {sale_data.get('date_vente', 'N/A')}",
                f"<b>Statut:</b> {sale_data.get('statut', 'N/A')}",
                f"<b>Mode de paiement:</b> {sale_data.get('mode_paiement', 'N/A')}"
            ]
            for info in vente_info:
                story.append(Paragraph(info, styles['Normal']))
            story.append(Spacer(1, 15))
            
            # Informations client
            client_name = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
            if not client_name:
                client_name = client.get('entreprise', 'Client inconnu')
            
            story.append(Paragraph("Informations client", heading_style))
            client_info = [f"<b>Nom:</b> {client_name}"]
            if client.get('adresse'):
                client_info.append(f"<b>Adresse:</b> {client.get('adresse')}")
            if client.get('telephone'):
                client_info.append(f"<b>Téléphone:</b> {client.get('telephone')}")
            if client.get('email'):
                client_info.append(f"<b>Email:</b> {client.get('email')}")
            
            for info in client_info:
                story.append(Paragraph(info, styles['Normal']))
            story.append(Spacer(1, 15))
            
            # Tableau des produits
            story.append(Paragraph("Détail des produits", heading_style))
            
            # Données du tableau
            table_data = [['Produit', 'Référence', 'Quantité', 'Prix unitaire', 'Total']]
            
            for detail in details:
                total_ligne = detail.get('quantite', 0) * detail.get('prix_unitaire', 0)
                table_data.append([
                    detail.get('produit_nom', ''),
                    detail.get('reference', ''),
                    str(detail.get('quantite', 0)),
                    f"{detail.get('prix_unitaire', 0):.2f} DH",
                    f"{total_ligne:.2f} DH"
                ])
            
            # Créer le tableau
            table = Table(table_data, colWidths=[4*cm, 3*cm, 2*cm, 3*cm, 3*cm])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(table)
            story.append(Spacer(1, 20))
            
            # Totaux
            totals_data = [
                f"<b>Montant HT:</b> {sale_data.get('montant_ht', 0):.2f} DH",
                f"<b>TVA:</b> {sale_data.get('montant_tva', 0):.2f} DH",
                f"<b>Montant TTC:</b> {sale_data.get('montant_ttc', 0):.2f} DH"
            ]
            
            for total in totals_data:
                story.append(Paragraph(total, styles['Normal']))
            
            story.append(Spacer(1, 30))
            story.append(Paragraph(f"Document généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}", styles['Normal']))
            
            # Construire le PDF
            doc.build(story)
            
            return pdf_path
            
        except Exception as e:
            print(f"❌ Erreur lors de la génération du PDF état: {e}")
            raise
    
    def generate_invoice_pdf(self, sale_data, details, client):
        """Générer un PDF pour la facture"""
        try:
            # Créer un fichier temporaire
            temp_dir = tempfile.gettempdir()
            pdf_path = os.path.join(temp_dir, f"facture_{sale_data.get('numero_facture', 'N_A')}.pdf")
            
            # Créer le document PDF
            doc = SimpleDocTemplate(pdf_path, pagesize=A4)
            story = []
            
            # Styles
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=20,
                spaceAfter=30,
                alignment=2  # Droite
            )
            
            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12
            )
            
            # En-tête avec informations entreprise et facture
            header_data = [
                ['GESCOM\nSystème de Gestion Commerciale\nAdresse de l\'entreprise\nTéléphone: +212 XXX XXX XXX', 
                 f'FACTURE\n\nN°: {sale_data.get("numero_facture", "N/A")}\nDate: {sale_data.get("date_vente", "N/A")}']
            ]
            
            header_table = Table(header_data, colWidths=[8*cm, 7*cm])
            header_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (0, 0), 'LEFT'),
                ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
                ('FONTNAME', (1, 0), (1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (1, 0), (1, 0), 14),
                ('VALIGN', (0, 0), (-1, -1), 'TOP')
            ]))
            
            story.append(header_table)
            story.append(Spacer(1, 30))
            
            # Informations client
            client_name = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
            if not client_name:
                client_name = client.get('entreprise', 'Client inconnu')
            
            client_text = f"<b>Facturé à:</b><br/><b>{client_name}</b>"
            if client.get('adresse'):
                client_text += f"<br/>{client.get('adresse')}"
            if client.get('telephone'):
                client_text += f"<br/>Tél: {client.get('telephone')}"
            if client.get('email'):
                client_text += f"<br/>Email: {client.get('email')}"
            
            story.append(Paragraph(client_text, styles['Normal']))
            story.append(Spacer(1, 20))
            
            # Tableau des produits
            table_data = [['Désignation', 'Référence', 'Qté', 'Prix unitaire', 'TVA', 'Total HT']]
            
            for detail in details:
                total_ligne = detail.get('quantite', 0) * detail.get('prix_unitaire', 0)
                table_data.append([
                    detail.get('produit_nom', ''),
                    detail.get('reference', ''),
                    str(detail.get('quantite', 0)),
                    f"{detail.get('prix_unitaire', 0):.2f} DH",
                    f"{detail.get('taux_tva', 0):.1f}%",
                    f"{total_ligne:.2f} DH"
                ])
            
            # Créer le tableau
            table = Table(table_data, colWidths=[4*cm, 2.5*cm, 1.5*cm, 2.5*cm, 1.5*cm, 2.5*cm])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(table)
            story.append(Spacer(1, 20))
            
            # Totaux
            totals_data = [
                ['', f"Montant HT: {sale_data.get('montant_ht', 0):.2f} DH"],
                ['', f"TVA: {sale_data.get('montant_tva', 0):.2f} DH"],
                ['', f"<b>Montant TTC: {sale_data.get('montant_ttc', 0):.2f} DH</b>"]
            ]
            
            totals_table = Table(totals_data, colWidths=[10*cm, 5*cm])
            totals_table.setStyle(TableStyle([
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
                ('FONTNAME', (1, 2), (1, 2), 'Helvetica-Bold'),
                ('FONTSIZE', (1, 2), (1, 2), 12)
            ]))
            
            story.append(totals_table)
            story.append(Spacer(1, 20))
            
            # Informations de paiement
            payment_info = f"<b>Mode de paiement:</b> {sale_data.get('mode_paiement', 'N/A')}<br/><b>Statut:</b> {sale_data.get('statut', 'N/A')}"
            story.append(Paragraph(payment_info, styles['Normal']))
            
            story.append(Spacer(1, 30))
            story.append(Paragraph("Merci pour votre confiance !", styles['Normal']))
            story.append(Paragraph(f"Document généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}", styles['Normal']))
            
            # Construire le PDF
            doc.build(story)
            
            return pdf_path
            
        except Exception as e:
            print(f"❌ Erreur lors de la génération du PDF facture: {e}")
            raise
    
    # Anciennes méthodes HTML supprimées - remplacées par l'impression PDF directe
    
    def change_sale_status(self, sale_data):
        """Changer le statut d'une vente"""
        current_status = sale_data.get('statut', 'En cours')
        
        # Définir les transitions possibles
        status_transitions = {
            'En cours': ['Payée', 'Annulée'],
            'Payée': ['Annulée'],  # Une vente payée peut être annulée (remboursement)
            'Annulée': ['En cours']  # Une vente annulée peut être remise en cours
        }
        
        possible_statuses = status_transitions.get(current_status, [])
        
        if not possible_statuses:
            # Aucune transition possible
            info_dialog = MDDialog(
                title="Changement d'état",
                text=f"Aucun changement d'état possible pour une vente '{current_status}'.",
                buttons=[
                    MDFlatButton(
                        text="OK",
                        on_release=lambda x: info_dialog.dismiss()
                    )
                ]
            )
            info_dialog.open()
            return
        
        # Créer les boutons pour chaque statut possible
        status_buttons = []
        
        for status in possible_statuses:
            # Définir la couleur selon le statut
            if status == 'Payée':
                color = (0.2, 0.7, 0.2, 1)  # Vert
                icon_text = "✓"
            elif status == 'Annulée':
                color = (0.8, 0.2, 0.2, 1)  # Rouge
                icon_text = "✗"
            else:  # En cours
                color = (0.2, 0.5, 0.8, 1)  # Bleu
                icon_text = "⏳"
            
            btn = MDRaisedButton(
                text=f"{icon_text} {status}",
                md_bg_color=color,
                on_release=self.create_status_callback(sale_data, status)
            )
            status_buttons.append(btn)
        
        # Ajouter le bouton d'annulation
        cancel_btn = MDFlatButton(
            text="ANNULER"
        )
        status_buttons.append(cancel_btn)
        
        # Créer le dialog avec les boutons
        numero_facture = sale_data.get('numero_facture', 'N/A')
        status_dialog = MDDialog(
            title="Changer le statut",
            text=f"Vente: {numero_facture}\nStatut actuel: {current_status}\n\nChoisissez le nouveau statut:",
            buttons=status_buttons
        )
        
        # Assigner le callback d'annulation après création du dialog
        cancel_btn.bind(on_release=lambda x: status_dialog.dismiss())
        
        # Stocker la référence du dialog
        self.current_status_dialog = status_dialog
        status_dialog.open()
    
    def create_status_callback(self, sale_data, status):
        """Créer un callback pour le changement de statut"""
        def callback(x):
            # Fermer le dialog de sélection de statut
            if hasattr(self, 'current_status_dialog') and self.current_status_dialog:
                self.current_status_dialog.dismiss()
            # Ouvrir le dialog de confirmation
            self.confirm_status_change(sale_data, status)
        return callback
    
    def confirm_status_change(self, sale_data, new_status):
        """Confirmer le changement de statut"""
        
        def update_status():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                # Mettre à jour le statut dans la base de données
                success = db_manager.execute_update(
                    "UPDATE ventes SET statut = ? WHERE id = ?",
                    (new_status, sale_data['id'])
                )
                
                if success:
                    # Recharger la liste des ventes
                    Clock.schedule_once(lambda dt: self.load_sales(), 0)
                    print(f"✅ Statut de la vente {sale_data.get('numero_facture', '')} changé vers '{new_status}'")
                else:
                    print("❌ Erreur lors du changement de statut")
                
            except Exception as e:
                print(f"❌ Erreur lors du changement de statut: {e}")
        
        # Confirmation finale
        numero_facture = sale_data.get('numero_facture', 'N/A')
        current_status = sale_data.get('statut', '')
        
        confirm_dialog = MDDialog(
            title="Confirmer le changement",
            text=f"Vente: {numero_facture}\n\n{current_status} → {new_status}\n\nConfirmer ce changement ?",
            buttons=[
                MDFlatButton(
                    text="ANNULER",
                    on_release=lambda x: confirm_dialog.dismiss()
                ),
                MDRaisedButton(
                    text="CONFIRMER",
                    md_bg_color=(0.2, 0.7, 0.2, 1),
                    on_release=lambda x: (update_status(), confirm_dialog.dismiss())
                )
            ]
        )
        confirm_dialog.open()
    
    def delete_sale(self, sale_data):
        """Supprimer une vente (seulement les ventes En cours et Annulées)"""
        # Vérification du statut avant suppression
        statut = sale_data.get('statut', '')
        if statut not in ['En cours', 'Annulée']:
            # Afficher un message d'erreur pour les ventes payées
            error_dialog = MDDialog(
                title="Suppression impossible",
                text=f"Seules les ventes avec le statut 'En cours' ou 'Annulée' peuvent être supprimées.\n\nStatut actuel: {statut}\n\nLes ventes payées ne peuvent pas être supprimées pour des raisons de traçabilité comptable.",
                buttons=[
                    MDFlatButton(
                        text="OK",
                        on_release=lambda x: error_dialog.dismiss()
                    )
                ]
            )
            error_dialog.open()
            return
        
        def confirm_delete():
            def delete_data():
                try:
                    app = MDApp.get_running_app()
                    db_manager = app.db_manager
                    
                    # Vérifier une dernière fois le statut avant suppression
                    current_sale = db_manager.execute_query(
                        "SELECT statut FROM ventes WHERE id = ?",
                        (sale_data['id'],)
                    )
                    
                    if not current_sale or current_sale[0].get('statut') not in ['En cours', 'Annulée']:
                        print("❌ Tentative de suppression d'une vente payée bloquée")
                        return
                    
                    # Supprimer d'abord les détails de vente
                    db_manager.execute_update("DELETE FROM details_vente WHERE vente_id = ?", (sale_data['id'],))
                    
                    # Supprimer la vente (seulement si En cours ou Annulée)
                    success = db_manager.execute_update(
                        "DELETE FROM ventes WHERE id = ? AND statut IN ('En cours', 'Annulée')",
                        (sale_data['id'],)
                    )
                    
                    if success:
                        # Recharger la liste
                        Clock.schedule_once(lambda dt: self.load_sales(), 0)
                        print(f"✅ Vente {sale_data.get('numero_facture', '')} supprimée avec succès")
                    else:
                        print("Erreur lors de la suppression de la vente")
                
                except Exception as e:
                    print(f"Erreur lors de la suppression: {e}")
            
            threading.Thread(target=delete_data, daemon=True).start()
        
        # Dialog de confirmation
        numero_facture = sale_data.get('numero_facture', 'N/A')
        
        confirm_dialog = MDDialog(
            title="Confirmer la suppression",
            text=f"Êtes-vous sûr de vouloir supprimer la vente '{numero_facture}' ?\n\nStatut: {statut}\n\nCette action est irréversible.",
            buttons=[
                MDFlatButton(
                    text="ANNULER",
                    on_release=lambda x: confirm_dialog.dismiss()
                ),
                MDRaisedButton(
                    text="SUPPRIMER",
                    md_bg_color=(1, 0.3, 0.3, 1),
                    on_release=lambda x: (confirm_delete(), confirm_dialog.dismiss())
                )
            ]
        )
        confirm_dialog.open()