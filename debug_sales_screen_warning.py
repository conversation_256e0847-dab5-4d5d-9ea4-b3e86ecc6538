#!/usr/bin/env python3
"""
Script de débogage spécifique pour l'écran des ventes
"""

import os
import sys
import warnings
import traceback

# Capturer tous les avertissements avec traceback
def warning_handler(message, category, filename, lineno, file=None, line=None):
    if "width_mult" in str(message):
        print(f"\n🔍 AVERTISSEMENT width_mult DÉTECTÉ:")
        print(f"📄 Fichier: {filename}")
        print(f"📍 Ligne: {lineno}")
        print(f"⚠️ Message: {message}")
        print(f"📂 Catégorie: {category}")
        print("\n📋 STACK TRACE:")
        traceback.print_stack()
        print("\n" + "="*60 + "\n")

# Installer le gestionnaire d'avertissements
warnings.showwarning = warning_handler

# Configurer le logger de Kivy
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

# Configuration pour Windows
if sys.platform == 'win32':
    os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'

print("🔍 DÉBOGAGE ÉCRAN DES VENTES")
print("Objectif: Identifier si l'avertissement vient de l'écran des ventes")
print("="*60)

try:
    from kivymd.app import MDApp
    from kivymd.uix.screen import MDScreen
    from kivymd.uix.boxlayout import MDBoxLayout
    from kivymd.uix.button import MDRaisedButton
    from kivymd.uix.label import MDLabel
    
    print("✅ Imports KivyMD réussis")
    
    # Import de l'écran des ventes
    print("📦 Import de l'écran des ventes...")
    from screens.sales_screen import SalesScreen
    
    print("✅ Import de SalesScreen réussi")
    
    class DebugSalesApp(MDApp):
        def build(self):
            print("🏗️ Construction de l'interface de débogage...")
            
            screen = MDScreen()
            layout = MDBoxLayout(
                orientation='vertical',
                spacing="20dp",
                padding="20dp"
            )
            
            title = MDLabel(
                text="🔍 Débogage Écran Ventes\nRecherche width_mult...",
                font_style="H5",
                theme_text_color="Primary",
                halign="center",
                size_hint_y=None,
                height="100dp"
            )
            
            test_btn = MDRaisedButton(
                text="🛒 Créer Écran des Ventes",
                size_hint_y=None,
                height="60dp",
                on_release=self.test_sales_screen
            )
            
            form_btn = MDRaisedButton(
                text="📝 Ouvrir Formulaire de Vente",
                size_hint_y=None,
                height="60dp",
                on_release=self.test_sales_form
            )
            
            self.result_label = MDLabel(
                text="En attente de test...",
                font_style="Body1",
                theme_text_color="Secondary",
                halign="center"
            )
            
            layout.add_widget(title)
            layout.add_widget(test_btn)
            layout.add_widget(form_btn)
            layout.add_widget(self.result_label)
            
            screen.add_widget(layout)
            return screen
        
        def test_sales_screen(self, *args):
            print("\n🧪 TEST: Création de l'écran des ventes")
            try:
                # Créer l'écran des ventes
                sales_screen = SalesScreen()
                
                self.result_label.text = "✅ Écran des ventes créé\n" \
                                       "Vérifiez la console pour width_mult"
                
                print("✅ Écran des ventes créé avec succès")
                
            except Exception as e:
                error_msg = f"❌ Erreur écran ventes: {str(e)}"
                self.result_label.text = error_msg
                print(error_msg)
                traceback.print_exc()
        
        def test_sales_form(self, *args):
            print("\n🧪 TEST: Ouverture du formulaire de vente")
            try:
                from forms.sales_form import SalesFormDialog
                
                def on_save(data):
                    print(f"💾 Données sauvegardées: {data}")
                
                # Créer le formulaire
                dialog = SalesFormDialog(on_save_callback=on_save)
                dialog.open()
                
                self.result_label.text = "✅ Formulaire de vente ouvert\n" \
                                       "Vérifiez la console pour width_mult"
                
                print("✅ Formulaire de vente ouvert avec succès")
                
            except Exception as e:
                error_msg = f"❌ Erreur formulaire: {str(e)}"
                self.result_label.text = error_msg
                print(error_msg)
                traceback.print_exc()
    
    print("🚀 Lancement de l'application de débogage...")
    app = DebugSalesApp()
    app.run()
    
except Exception as e:
    print(f"❌ Erreur critique: {e}")
    traceback.print_exc()