"""
Script d'installation automatique pour GesComPro
"""

import sys
import subprocess
import os
from pathlib import Path

def check_python_version():
    """Vérifier la version de Python"""
    print("🐍 Vérification de la version Python...")
    
    if sys.version_info < (3, 8):
        print(f"❌ Python 3.8+ requis. Version actuelle: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_dependencies():
    """Installer les dépendances"""
    print("\n📦 Installation des dépendances...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ Fichier requirements.txt non trouvé")
        return False
    
    try:
        # Mettre à jour pip
        print("🔄 Mise à jour de pip...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Installer les dépendances
        print("📥 Installation des dépendances...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)])
        
        print("✅ Dépendances installées avec succès")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de l'installation: {e}")
        return False

def setup_directories():
    """Créer les répertoires nécessaires"""
    print("\n📁 Création des répertoires...")
    
    base_dir = Path(__file__).parent
    directories = ["data", "backups", "exports", "exports/pdf"]
    
    for dir_name in directories:
        dir_path = base_dir / dir_name
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ {dir_name}")
    
    return True

def initialize_database():
    """Initialiser la base de données"""
    print("\n🗄️ Initialisation de la base de données...")
    
    try:
        # Ajouter le répertoire au path
        sys.path.insert(0, str(Path(__file__).parent))
        
        from database.db_manager import DatabaseManager
        from config import DATABASE_PATH
        
        db_manager = DatabaseManager(str(DATABASE_PATH))
        
        if db_manager.initialize_database():
            print("✅ Base de données initialisée")
            
            # Créer des données d'exemple
            if db_manager.create_sample_data():
                print("✅ Données d'exemple créées")
            else:
                print("⚠️ Impossible de créer les données d'exemple")
            
            return True
        else:
            print("❌ Erreur lors de l'initialisation de la base de données")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_installation():
    """Tester l'installation"""
    print("\n🧪 Test de l'installation...")
    
    try:
        # Test des imports principaux
        import kivy
        import kivymd
        import matplotlib
        import reportlab
        
        print("✅ Tous les modules importés avec succès")
        
        # Test de la base de données
        sys.path.insert(0, str(Path(__file__).parent))
        from database.db_manager import DatabaseManager
        from config import DATABASE_PATH
        
        db_manager = DatabaseManager(str(DATABASE_PATH))
        if db_manager.connect():
            print("✅ Connexion à la base de données réussie")
            db_manager.disconnect()
        else:
            print("❌ Impossible de se connecter à la base de données")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Module manquant: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

def create_shortcuts():
    """Créer des raccourcis de lancement"""
    print("\n🔗 Création des raccourcis...")
    
    base_dir = Path(__file__).parent
    
    # Script de lancement simple
    launcher_content = f'''@echo off
cd /d "{base_dir}"
python launch.py
pause
'''
    
    launcher_path = base_dir / "GesComPro.bat"
    with open(launcher_path, 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ Raccourci GesComPro.bat créé")
    
    # Script de test
    test_content = f'''@echo off
cd /d "{base_dir}"
python test_app.py
pause
'''
    
    test_path = base_dir / "Test.bat"
    with open(test_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("✅ Raccourci Test.bat créé")
    
    return True

def main():
    """Fonction principale d'installation"""
    print("🚀 Installation de GesComPro")
    print("=" * 50)
    
    steps = [
        ("Vérification de Python", check_python_version),
        ("Installation des dépendances", install_dependencies),
        ("Configuration des répertoires", setup_directories),
        ("Initialisation de la base de données", initialize_database),
        ("Test de l'installation", test_installation),
        ("Création des raccourcis", create_shortcuts)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"\n❌ Échec de l'étape: {step_name}")
            print("Installation interrompue.")
            input("\nAppuyez sur Entrée pour quitter...")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 Installation terminée avec succès!")
    print("\n📋 Pour démarrer l'application:")
    print("1. Double-cliquez sur GesComPro.bat")
    print("2. Ou exécutez: python launch.py")
    print("\n🧪 Pour tester l'installation:")
    print("1. Double-cliquez sur Test.bat")
    print("2. Ou exécutez: python test_app.py")
    
    print(f"\n📁 Répertoire d'installation: {Path(__file__).parent}")
    
    # Demander si l'utilisateur veut lancer l'application
    response = input("\nVoulez-vous lancer l'application maintenant? (o/N): ")
    if response.lower() in ['o', 'oui', 'y', 'yes']:
        try:
            from launch import main as launch_main
            launch_main()
        except Exception as e:
            print(f"Erreur lors du lancement: {e}")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️ Installation interrompue par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
        input("\nAppuyez sur Entrée pour quitter...")
        sys.exit(1)