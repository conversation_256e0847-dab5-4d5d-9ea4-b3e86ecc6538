#!/usr/bin/env python3
"""
Test du formulaire client amélioré
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_client_form():
    """Test du formulaire client"""
    print("🧪 Test du formulaire client amélioré")
    print("=" * 50)
    
    try:
        # Importer les modules nécessaires
        from kivymd.app import MDApp
        from screens.clients_screen import ClientFormDialog
        from main import GesComApp
        
        print("✅ Modules importés")
        
        # Créer une app de test
        class TestApp(GesComApp):
            def build(self):
                return super().build()
        
        app = TestApp()
        
        # Données de test pour modification
        test_client_data = {
            'id': 1,
            'nom': 'Dupont',
            'prenom': 'Jean',
            'entreprise': 'SARL Dupont & Fils',
            'email': '<EMAIL>',
            'telephone': '01 23 45 67 89',
            'adresse': '123 Avenue des Champs-Élysées\nAppartement 4B',
            'ville': 'Paris',
            'code_postal': '75008',
            'pays': 'France'
        }
        
        print("✅ Données de test préparées")
        
        # Test 1: Création du dialog pour nouveau client
        print("🔸 Test 1: Dialog nouveau client...")
        try:
            new_dialog = ClientFormDialog()
            print("  ✅ Dialog nouveau client créé")
            
            # Vérifier que les champs sont vides
            assert new_dialog.nom_field.text == ""
            assert new_dialog.prenom_field.text == ""
            assert new_dialog.pays_field.text == "France"  # Valeur par défaut
            print("  ✅ Champs vides corrects")
            
        except Exception as e:
            print(f"  ❌ Erreur dialog nouveau client: {e}")
            return False
        
        # Test 2: Création du dialog pour modification
        print("🔸 Test 2: Dialog modification client...")
        try:
            edit_dialog = ClientFormDialog(client_data=test_client_data)
            print("  ✅ Dialog modification créé")
            
            # Vérifier que les champs sont pré-remplis
            assert edit_dialog.nom_field.text == "Dupont"
            assert edit_dialog.prenom_field.text == "Jean"
            assert edit_dialog.entreprise_field.text == "SARL Dupont & Fils"
            assert edit_dialog.email_field.text == "<EMAIL>"
            assert edit_dialog.telephone_field.text == "01 23 45 67 89"
            assert edit_dialog.ville_field.text == "Paris"
            assert edit_dialog.code_postal_field.text == "75008"
            assert edit_dialog.pays_field.text == "France"
            print("  ✅ Champs pré-remplis corrects")
            
        except Exception as e:
            print(f"  ❌ Erreur dialog modification: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test 3: Gestion des valeurs None
        print("🔸 Test 3: Gestion des valeurs None...")
        try:
            none_data = {
                'nom': 'Test',
                'prenom': None,
                'entreprise': None,
                'email': None,
                'telephone': None,
                'adresse': None,
                'ville': None,
                'code_postal': None,
                'pays': None
            }
            
            none_dialog = ClientFormDialog(client_data=none_data)
            print("  ✅ Dialog avec valeurs None créé")
            
            # Vérifier que les None sont convertis en chaînes vides
            assert none_dialog.nom_field.text == "Test"
            assert none_dialog.prenom_field.text == ""
            assert none_dialog.entreprise_field.text == ""
            assert none_dialog.email_field.text == ""
            print("  ✅ Valeurs None gérées correctement")
            
        except Exception as e:
            print(f"  ❌ Erreur gestion None: {e}")
            return False
        
        # Test 4: Structure du formulaire
        print("🔸 Test 4: Structure du formulaire...")
        try:
            dialog = ClientFormDialog(client_data=test_client_data)
            
            # Vérifier que le ScrollView est utilisé
            assert hasattr(dialog, 'content_cls')
            print("  ✅ ScrollView présent")
            
            # Vérifier que tous les champs existent
            required_fields = [
                'nom_field', 'prenom_field', 'entreprise_field',
                'email_field', 'telephone_field', 'adresse_field',
                'ville_field', 'code_postal_field', 'pays_field'
            ]
            
            for field_name in required_fields:
                assert hasattr(dialog, field_name)
            print("  ✅ Tous les champs présents")
            
        except Exception as e:
            print(f"  ❌ Erreur structure: {e}")
            return False
        
        print("\n🎉 TOUS LES TESTS DU FORMULAIRE RÉUSSIS!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Test du formulaire client - GesComPro_LibTam")
    print("=" * 60)
    
    success = test_client_form()
    
    if success:
        print("\n✅ LE FORMULAIRE CLIENT EST BIEN ORGANISÉ!")
        print("🎯 Améliorations apportées:")
        print("   • Sections organisées (Personnel, Contact, Adresse)")
        print("   • Champs Nom/Prénom sur la même ligne")
        print("   • Champs Ville/Code postal sur la même ligne")
        print("   • ScrollView pour éviter les champs cachés")
        print("   • Taille adaptative du dialog")
        print("   • Gestion sécurisée des valeurs None")
    else:
        print("\n❌ PROBLÈME DANS LE FORMULAIRE CLIENT")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    print("\nAppuyez sur Entrée pour continuer...")
    input()