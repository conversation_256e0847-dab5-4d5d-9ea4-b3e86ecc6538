# GesComPro_LibTam - Repository Index

## Project Overview
**GesComPro_LibTam** is a modern commercial management desktop application developed in Python using Kivy/KivyMD framework. It provides a comprehensive solution for managing clients, products, categories, sales, and reports with a modern Material Design interface.

**Developer**: LKAIHAL LAHCEN_AIA

## Technology Stack
- **Language**: Python 3.8+
- **UI Framework**: Kivy/KivyMD (Material Design)
- **Database**: SQLite
- **Charts**: Matplotlib
- **PDF Generation**: ReportLab
- **Platform**: Windows (with .exe build support)

## Project Structure

### Core Application Files
- `main.py` - Main application entry point with MDApp class
- `config.py` - Global configuration settings
- `requirements.txt` - Python dependencies
- `build_exe.py` - PyInstaller script for executable creation
- `launch*.py` - Various launch scripts for different configurations

### Database Layer
- `database/db_manager.py` - SQLite database manager with singleton pattern
- `data/gescom.db` - SQLite database file (auto-created)

### User Interface Screens
- `screens/dashboard_screen.py` - Main dashboard with statistics
- `screens/clients_screen.py` - Client management interface
- `screens/products_screen.py` - Product catalog management
- `screens/categories_screen.py` - Product categories management
- `screens/sales_screen.py` - Sales management and creation
- `screens/reports_screen.py` - Reports and analytics
- `screens/settings_screen.py` - Application settings

### Forms and Dialogs
- `forms/sales_form.py` - Sales creation/editing dialog
- `forms/category_form.py` - Category management form

### Models
- `models/category_model.py` - Category data model

### Utilities
- `utils/helpers.py` - General utility functions
- `utils/pdf_generator.py` - PDF document generation
- `utils/barcode_utils.py` - Barcode generation and validation
- `utils/chart_fallback.py` - Chart generation utilities

### Configuration
- `config/currency_config.py` - Currency settings (Dirham DH)

### Data Directories
- `data/` - Database and application data
- `backups/` - Database backups
- `exports/` - Exported files (CSV, PDF)

## Key Features

### 1. Client Management
- Complete client information (name, company, contact details)
- Client search and filtering
- Purchase history tracking

### 2. Product Management
- Product catalog with categories
- Stock management with alerts
- Pricing (purchase/sale prices)
- Barcode support

### 3. Sales Management
- Quick sale creation
- Client and product selection
- Automatic calculations (HT/TTC)
- Multiple payment methods
- Invoice generation and printing

### 4. Categories System
- Hierarchical product categorization
- CRUD operations for categories
- Product-category relationships

### 5. Reports and Analytics
- Sales statistics and charts
- Top products and clients
- Period-based reporting
- Data export capabilities

### 6. User Interface
- Modern Material Design (MD3)
- Light/Dark theme support
- Responsive navigation drawer
- Multi-screen architecture

## Database Schema

### Main Tables
- `clients` - Customer information
- `produits` (products) - Product catalog
- `categories` - Product categories
- `ventes` (sales) - Sales transactions
- `lignes_vente` - Sale line items

### Key Relationships
- Products → Categories (many-to-one)
- Sales → Clients (many-to-one)
- Sale Lines → Sales (many-to-one)
- Sale Lines → Products (many-to-one)

## Testing Approach

### Current Testing
The project contains numerous test files (test_*.py) that appear to be:
- **Unit tests** for individual components
- **Integration tests** for database operations
- **UI tests** for form validation and display
- **Performance tests** for database optimization

### Test Categories
- Database functionality tests
- Form validation and display tests
- Sales workflow tests
- Category management tests
- Client management tests
- Performance and optimization tests

### Testing Framework
**targetFramework**: pytest
**Current Status**: Mixed approach - custom Python test scripts + pytest for new features
**Recommendation**: Gradually migrate existing tests to pytest

## Build and Deployment

### Development Environment
- Python virtual environment (`.venv/`)
- Windows-specific dependencies (pywin32, kivy-deps)
- Development batch files (`GesComPro.bat`, `Test.bat`)

### Production Build
- PyInstaller for executable creation
- Windows executable generation
- Standalone deployment support

## Configuration Management

### Environment Variables
- `KIVY_GL_BACKEND` - Graphics backend configuration
- `KIVY_LOG_MODE` - Logging configuration

### Application Settings
- Theme customization (light/dark)
- Currency configuration (Dirham)
- Database optimization settings

## Documentation Files

### Implementation Guides
- Multiple `.md` files documenting features and fixes
- Performance optimization guides
- UI improvement documentation
- Database schema evolution

### Key Documentation
- `README.md` - Main project documentation
- `GUIDE_*.md` - Feature-specific guides
- `RESUME_*.md` - Implementation summaries
- `CORRECTION_*.md` - Bug fix documentation

## Development Patterns

### Architecture
- **MVC Pattern**: Models, Views (Screens), Controllers (Forms)
- **Singleton Pattern**: Database manager
- **Observer Pattern**: UI callbacks and events

### Code Organization
- Modular screen-based architecture
- Separation of concerns (database, UI, business logic)
- Utility functions for common operations

### Error Handling
- Custom warning handlers for Kivy deprecation warnings
- Database connection error handling
- UI validation and user feedback

## Performance Optimizations

### Database
- Connection pooling and singleton pattern
- Optimized queries and indexing
- Automatic database optimization scripts

### UI
- Lazy loading for large datasets
- Efficient widget creation and destruction
- Memory management for charts and images

## Security Considerations

### Data Protection
- Local SQLite database (no network exposure)
- File system permissions
- Backup and restore capabilities

### Input Validation
- Form field validation
- SQL injection prevention
- Data sanitization

## Future Enhancements

### Potential Improvements
- Web-based version
- Multi-user support
- Cloud synchronization
- Advanced reporting features
- Mobile companion app

### Technical Debt
- Migrate to modern testing framework
- Consolidate multiple launch scripts
- Improve error handling consistency
- Enhance documentation coverage

---

This repository represents a comprehensive commercial management solution with modern UI, robust database management, and extensive feature set for small to medium businesses.