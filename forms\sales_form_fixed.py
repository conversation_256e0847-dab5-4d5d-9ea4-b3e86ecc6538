"""
Formulaire de vente corrigé avec hauteurs fixes
"""

from kivymd.uix.dialog import MDDialog
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.button import MDRaisedButton, MDFlatButton
from kivymd.uix.snackbar import MDSnackbar
from kivymd.uix.menu import MDDropdownMenu
from database.db_manager import DatabaseManager
from datetime import datetime
import uuid


class SalesFormDialogFixed(MDDialog):
    """Formulaire de vente avec hauteurs fixes pour corriger l'affichage"""
    
    def __init__(self, sale_data=None, on_save_callback=None, **kwargs):
        self.sale_data = sale_data or {}
        self.on_save_callback = on_save_callback
        self.db_manager = DatabaseManager()
        
        # Déterminer le mode (création ou modification)
        self.is_edit_mode = bool(self.sale_data and self.sale_data.get('id'))
        
        # Données pour les listes déroulantes
        self.clients_list = []
        self.selected_client = None
        
        # Modes de paiement
        self.payment_modes = [
            "💰 Espèces",
            "💳 Carte bancaire", 
            "📄 Chèque",
            "🏦 Virement bancaire",
            "📱 Paiement électronique",
            "💸 Crédit"
        ]
        
        # Charger les clients et créer le contenu
        self.load_clients()
        content = self.create_content()
        
        # Créer les boutons
        cancel_btn = MDFlatButton(
            text="❌ Annuler",
            on_release=self.dismiss
        )
        
        save_btn = MDRaisedButton(
            text="💾 Enregistrer",
            on_release=self.save_sale
        )
        
        super().__init__(
            title="✏️ Modifier la vente" if self.is_edit_mode else "🛒 Nouvelle vente",
            type="custom",
            content_cls=content,
            size_hint=(0.9, None),
            height="600dp",
            buttons=[cancel_btn, save_btn],
            **kwargs
        )
    
    def create_content(self):
        """Créer le contenu avec hauteurs fixes"""
        # Container principal - HAUTEUR FIXE
        main_layout = MDBoxLayout(
            orientation='vertical',
            spacing="16dp",
            padding="20dp",
            size_hint_y=None,
            height="500dp"  # HAUTEUR FIXE pour éviter les problèmes
        )
        
        # En-tête - HAUTEUR FIXE
        header = MDLabel(
            text="🛒 Formulaire de Vente",
            font_style="H6",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="40dp"
        )
        main_layout.add_widget(header)
        
        # Section client - HAUTEUR FIXE
        client_section = self.create_client_section()
        main_layout.add_widget(client_section)
        
        # Section montants - HAUTEUR FIXE
        montants_section = self.create_montants_section()
        main_layout.add_widget(montants_section)
        
        # Section paiement - HAUTEUR FIXE
        paiement_section = self.create_paiement_section()
        main_layout.add_widget(paiement_section)
        
        # Section notes - HAUTEUR FIXE
        notes_section = self.create_notes_section()
        main_layout.add_widget(notes_section)
        
        return main_layout
    
    def create_client_section(self):
        """Créer la section client avec hauteur fixe"""
        client_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="80dp"  # HAUTEUR FIXE
        )
        
        client_label = MDLabel(
            text="👤 Client",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        # Champ client
        self.client_field = MDTextField(
            hint_text="Sélectionner un client",
            mode="rectangle",
            readonly=True,
            size_hint_y=None,
            height="48dp"
        )
        
        # Pré-remplir si modification
        if self.is_edit_mode and self.selected_client:
            nom_complet = f"{self.selected_client.get('prenom', '')} {self.selected_client.get('nom', '')}".strip()
            if not nom_complet:
                nom_complet = self.selected_client.get('entreprise', f"Client {self.selected_client.get('id', '')}")
            self.client_field.text = nom_complet
        
        # Créer le menu des clients
        self.create_client_menu()
        
        client_layout.add_widget(client_label)
        client_layout.add_widget(self.client_field)
        
        return client_layout
    
    def create_montants_section(self):
        """Créer la section des montants avec hauteur fixe"""
        montants_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="120dp"  # HAUTEUR FIXE
        )
        
        montants_label = MDLabel(
            text="💰 Montants",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        # Montant TTC
        self.montant_ttc_field = MDTextField(
            text=str(self.sale_data.get('montant_ttc', '')) if self.is_edit_mode else '',
            hint_text="Montant TTC (DH) - OBLIGATOIRE",
            mode="rectangle",
            input_filter="float",
            size_hint_y=None,
            height="48dp"
        )
        
        # Montant HT
        self.montant_ht_field = MDTextField(
            text=str(self.sale_data.get('montant_ht', '')) if self.is_edit_mode else '',
            hint_text="Montant HT (DH) - Optionnel",
            mode="rectangle",
            input_filter="float",
            size_hint_y=None,
            height="48dp"
        )
        
        montants_layout.add_widget(montants_label)
        montants_layout.add_widget(self.montant_ttc_field)
        montants_layout.add_widget(self.montant_ht_field)
        
        return montants_layout
    
    def create_paiement_section(self):
        """Créer la section paiement avec hauteur fixe"""
        paiement_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="80dp"  # HAUTEUR FIXE
        )
        
        paiement_label = MDLabel(
            text="💳 Mode de Paiement",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        # Champ paiement
        self.paiement_field = MDTextField(
            text=self.sale_data.get('mode_paiement', '💰 Espèces') if self.is_edit_mode else '💰 Espèces',
            hint_text="Mode de paiement",
            mode="rectangle",
            readonly=True,
            size_hint_y=None,
            height="48dp"
        )
        
        # Créer le menu des modes de paiement
        self.create_paiement_menu()
        
        paiement_layout.add_widget(paiement_label)
        paiement_layout.add_widget(self.paiement_field)
        
        return paiement_layout
    
    def create_notes_section(self):
        """Créer la section notes avec hauteur fixe"""
        notes_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="100dp"  # HAUTEUR FIXE
        )
        
        notes_label = MDLabel(
            text="📝 Notes",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        self.notes_field = MDTextField(
            text=self.sale_data.get('notes', '') if self.is_edit_mode else '',
            hint_text="Notes sur la vente (optionnel)",
            mode="rectangle",
            multiline=True,
            size_hint_y=None,
            height="68dp"
        )
        
        notes_layout.add_widget(notes_label)
        notes_layout.add_widget(self.notes_field)
        
        return notes_layout
    
    def load_clients(self):
        """Charger la liste des clients"""
        try:
            if not self.db_manager.connect():
                print("⚠️ Base de données non disponible - utilisation de clients de test")
                self.clients_list = [
                    {'id': 1, 'nom': 'Dupont', 'prenom': 'Jean', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0123456789'},
                    {'id': 2, 'nom': 'Martin', 'prenom': 'Marie', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0987654321'},
                    {'id': 3, 'nom': '', 'prenom': '', 'entreprise': 'Entreprise Test SARL', 'email': '<EMAIL>', 'telephone': '0555123456'}
                ]
                return
            
            clients = self.db_manager.execute_query("""
                SELECT id, nom, prenom, entreprise, email, telephone
                FROM clients 
                ORDER BY nom, prenom
            """)
            
            self.clients_list = clients or []
            
            if not self.clients_list:
                print("⚠️ Aucun client en base - utilisation de clients de test")
                self.clients_list = [
                    {'id': 1, 'nom': 'Dupont', 'prenom': 'Jean', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0123456789'},
                    {'id': 2, 'nom': 'Martin', 'prenom': 'Marie', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0987654321'},
                    {'id': 3, 'nom': '', 'prenom': '', 'entreprise': 'Entreprise Test SARL', 'email': '<EMAIL>', 'telephone': '0555123456'}
                ]
            
            # Si modification, trouver le client sélectionné
            if self.is_edit_mode and self.sale_data.get('client_id'):
                for client in self.clients_list:
                    if client['id'] == self.sale_data['client_id']:
                        self.selected_client = client
                        break
            
        except Exception as e:
            print(f"Erreur chargement clients: {e}")
            self.clients_list = [
                {'id': 1, 'nom': 'Dupont', 'prenom': 'Jean', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0123456789'},
                {'id': 2, 'nom': 'Martin', 'prenom': 'Marie', 'entreprise': '', 'email': '<EMAIL>', 'telephone': '0987654321'}
            ]
        finally:
            try:
                self.db_manager.close()
            except:
                pass
    
    def create_client_menu(self):
        """Créer le menu des clients"""
        client_menu_items = []
        
        for client in self.clients_list:
            nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
            if not nom_complet:
                nom_complet = client.get('entreprise', f"Client {client.get('id', '')}")
            
            client_menu_items.append({
                "text": nom_complet,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=client: self.select_client(x)
            })
        
        self.client_menu = MDDropdownMenu(
            caller=self.client_field,
            items=client_menu_items,
            max_height="200dp"
        )
        
        self.client_field.bind(on_release=self.client_menu.open)
    
    def create_paiement_menu(self):
        """Créer le menu des modes de paiement"""
        paiement_menu_items = []
        
        for mode in self.payment_modes:
            paiement_menu_items.append({
                "text": mode,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=mode: self.select_paiement(x)
            })
        
        self.paiement_menu = MDDropdownMenu(
            caller=self.paiement_field,
            items=paiement_menu_items,
            max_height="200dp"
        )
        
        self.paiement_field.bind(on_release=self.paiement_menu.open)
    
    def select_client(self, client):
        """Sélectionner un client"""
        self.selected_client = client
        nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
        if not nom_complet:
            nom_complet = client.get('entreprise', f"Client {client.get('id', '')}")
        self.client_field.text = nom_complet
        self.client_menu.dismiss()
    
    def select_paiement(self, mode):
        """Sélectionner un mode de paiement"""
        self.paiement_field.text = mode
        self.paiement_menu.dismiss()
    
    def validate_form(self):
        """Valider le formulaire"""
        errors = []
        
        # Client obligatoire
        if not self.selected_client:
            errors.append("Veuillez sélectionner un client")
        
        # Montant TTC obligatoire
        try:
            montant_ttc = float(self.montant_ttc_field.text or 0)
            if montant_ttc <= 0:
                errors.append("Le montant TTC doit être supérieur à 0")
        except ValueError:
            errors.append("Le montant TTC doit être un nombre valide")
        
        return errors
    
    def save_sale(self, *args):
        """Sauvegarder la vente"""
        # Validation
        errors = self.validate_form()
        if errors:
            self.show_error("Erreurs:\n" + "\n".join(f"• {error}" for error in errors))
            return
        
        try:
            # Préparer les données
            montant_ttc = float(self.montant_ttc_field.text or 0)
            montant_ht = float(self.montant_ht_field.text or montant_ttc)
            
            # Nettoyer le mode de paiement
            mode_paiement = self.paiement_field.text
            for emoji_mode in self.payment_modes:
                if mode_paiement == emoji_mode:
                    mode_paiement = emoji_mode.split(' ', 1)[1]
                    break
            
            sale_data = {
                'client_id': self.selected_client['id'],
                'montant_ht': montant_ht,
                'montant_ttc': montant_ttc,
                'mode_paiement': mode_paiement,
                'notes': self.notes_field.text.strip(),
                'numero_facture': f"FAC-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
            }
            
            self.show_success("Vente sauvegardée avec succès")
            
            # Callback
            if self.on_save_callback:
                self.on_save_callback(sale_data)
            
            self.dismiss()
            
        except Exception as e:
            self.show_error(f"Erreur: {str(e)}")
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"❌ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"❌ {message}")
    
    def show_success(self, message):
        """Afficher un message de succès"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"✅ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"✅ {message}")