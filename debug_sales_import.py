#!/usr/bin/env python3
"""
Debug de l'import du formulaire de vente dans l'application
"""

import os
import sys

print("🔍 DEBUG - Import du formulaire de vente")
print("=" * 50)

# Ajouter le répertoire de l'application au path
app_dir = r"d:\Apache24\htdocs\gescom"
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

print(f"📁 Répertoire de l'application: {app_dir}")
print(f"🐍 Python path: {sys.path[:3]}...")

# Test 1: Vérifier que le fichier existe
sales_form_path = os.path.join(app_dir, "forms", "sales_form.py")
print(f"\n1. 📄 Fichier sales_form.py existe: {os.path.exists(sales_form_path)}")

# Test 2: Vérifier le contenu du package forms
forms_dir = os.path.join(app_dir, "forms")
if os.path.exists(forms_dir):
    forms_files = os.listdir(forms_dir)
    print(f"2. 📁 Contenu du dossier forms: {forms_files}")
else:
    print("2. ❌ Dossier forms n'existe pas")

# Test 3: Tester l'import du package forms
try:
    import forms
    print(f"3. ✅ Import du package forms réussi: {forms}")
    print(f"   📍 Localisation: {forms.__file__}")
    
    # Vérifier __all__
    if hasattr(forms, '__all__'):
        print(f"   📋 __all__: {forms.__all__}")
    else:
        print("   ⚠️ Pas de __all__ défini")
        
except Exception as e:
    print(f"3. ❌ Erreur import package forms: {e}")

# Test 4: Tester l'import direct du module
try:
    from forms import sales_form
    print(f"4. ✅ Import du module sales_form réussi: {sales_form}")
    print(f"   📍 Localisation: {sales_form.__file__}")
    
    # Vérifier la classe
    if hasattr(sales_form, 'SalesFormDialog'):
        print(f"   🏗️ Classe SalesFormDialog trouvée: {sales_form.SalesFormDialog}")
    else:
        print("   ❌ Classe SalesFormDialog non trouvée")
        
except Exception as e:
    print(f"4. ❌ Erreur import module sales_form: {e}")
    import traceback
    traceback.print_exc()

# Test 5: Tester l'import de la classe
try:
    from forms.sales_form import SalesFormDialog
    print(f"5. ✅ Import de la classe SalesFormDialog réussi: {SalesFormDialog}")
    
    # Vérifier les méthodes
    methods = [method for method in dir(SalesFormDialog) if not method.startswith('_')]
    print(f"   🔧 Méthodes publiques: {methods[:5]}...")
    
except Exception as e:
    print(f"5. ❌ Erreur import classe SalesFormDialog: {e}")
    import traceback
    traceback.print_exc()

# Test 6: Tester la création d'une instance
try:
    from forms.sales_form import SalesFormDialog
    
    # Créer une instance sans l'ouvrir
    dialog = SalesFormDialog()
    print(f"6. ✅ Création d'instance réussie: {dialog}")
    print(f"   🏷️ Titre: {dialog.title}")
    print(f"   📏 Taille: {dialog.size_hint}")
    
except Exception as e:
    print(f"6. ❌ Erreur création instance: {e}")
    import traceback
    traceback.print_exc()

# Test 7: Vérifier les imports dans sales_screen.py
try:
    sales_screen_path = os.path.join(app_dir, "screens", "sales_screen.py")
    if os.path.exists(sales_screen_path):
        with open(sales_screen_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Chercher les imports
        import_lines = [line.strip() for line in content.split('\n') if 'import' in line and 'sales' in line.lower()]
        print(f"7. 📄 Imports dans sales_screen.py:")
        for line in import_lines:
            print(f"   {line}")
            
    else:
        print("7. ❌ Fichier sales_screen.py non trouvé")
        
except Exception as e:
    print(f"7. ❌ Erreur lecture sales_screen.py: {e}")

print("\n" + "=" * 50)
print("🎯 RÉSUMÉ DU DEBUG")
print("Si tous les tests passent, le problème vient d'ailleurs.")
print("Si un test échoue, c'est là qu'est le problème.")