#!/usr/bin/env python3
"""
Script pour ajouter des données de test complètes
"""

import os
import sys
from datetime import datetime, timedelta
import random

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager, add_client, add_product, create_sale

def ajouter_donnees_test():
    """Ajouter des données de test complètes"""
    print("🚀 AJOUT DE DONNÉES DE TEST COMPLÈTES")
    print("=" * 60)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    
    if not db_manager.connect():
        print("❌ Impossible de se connecter à la base de données")
        return
    
    if not db_manager.initialize_database():
        print("❌ Impossible d'initialiser la base de données")
        return
    
    print("✅ Base de données initialisée")
    
    try:
        # 1. CLIENTS DE TEST
        print("\n👥 AJOUT DES CLIENTS DE TEST")
        print("-" * 40)
        
        clients_test = [
            {
                'nom': 'Alami',
                'prenom': '<PERSON>',
                'email': '<EMAIL>',
                'telephone': '0612345678',
                'adresse': '123 Rue Hassan II, Casablanca',
                'ville': 'Casablanca',
                'code_postal': '20000',
                'pays': 'Maroc',
                'actif': True
            },
            {
                'nom': 'Bennani',
                'prenom': 'Fatima',
                'email': '<EMAIL>',
                'telephone': '0687654321',
                'adresse': '456 Avenue Mohammed V, Rabat',
                'ville': 'Rabat',
                'code_postal': '10000',
                'pays': 'Maroc',
                'actif': True
            },
            {
                'nom': 'Tazi',
                'prenom': 'Ahmed',
                'email': '<EMAIL>',
                'telephone': '0698765432',
                'adresse': '789 Boulevard Zerktouni, Marrakech',
                'ville': 'Marrakech',
                'code_postal': '40000',
                'pays': 'Maroc',
                'actif': True
            },
            {
                'entreprise': 'TechCorp Maroc SARL',
                'nom': 'Responsable',
                'prenom': 'Achats',
                'email': '<EMAIL>',
                'telephone': '0522123456',
                'adresse': '321 Zone Industrielle, Casablanca',
                'ville': 'Casablanca',
                'code_postal': '20100',
                'pays': 'Maroc',
                'actif': True
            },
            {
                'entreprise': 'Boutique El Fassi',
                'nom': 'El Fassi',
                'prenom': 'Youssef',
                'email': '<EMAIL>',
                'telephone': '0535987654',
                'adresse': '654 Médina, Fès',
                'ville': 'Fès',
                'code_postal': '30000',
                'pays': 'Maroc',
                'actif': True
            },
            {
                'nom': 'Idrissi',
                'prenom': 'Aicha',
                'email': '<EMAIL>',
                'telephone': '0661234567',
                'adresse': '987 Quartier Administratif, Agadir',
                'ville': 'Agadir',
                'code_postal': '80000',
                'pays': 'Maroc',
                'actif': True
            },
            {
                'nom': 'Chakir',
                'prenom': 'Omar',
                'email': '<EMAIL>',
                'telephone': '0672345678',
                'adresse': '147 Avenue des FAR, Tanger',
                'ville': 'Tanger',
                'code_postal': '90000',
                'pays': 'Maroc',
                'actif': True
            },
            {
                'entreprise': 'Restaurant Al Andalous',
                'nom': 'Andalous',
                'prenom': 'Gérant',
                'email': '<EMAIL>',
                'telephone': '0524567890',
                'adresse': '258 Place Jemaa el-Fna, Marrakech',
                'ville': 'Marrakech',
                'code_postal': '40000',
                'pays': 'Maroc',
                'actif': True
            }
        ]
        
        clients_ids = []
        for i, client in enumerate(clients_test, 1):
            try:
                client_id = add_client(db_manager, client)
                clients_ids.append(client_id)
                nom_affiche = client.get('entreprise') or f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
                print(f"✅ Client {i}: {nom_affiche} (ID: {client_id})")
            except Exception as e:
                print(f"⚠️ Client {i} déjà existant ou erreur: {e}")
                # Essayer de récupérer l'ID existant
                try:
                    existing = db_manager.execute_query("SELECT id FROM clients WHERE email = ?", (client['email'],))
                    if existing:
                        clients_ids.append(existing[0]['id'])
                except:
                    clients_ids.append(i)  # ID par défaut
        
        print(f"📊 Total clients: {len(clients_ids)}")
        
        # 2. CATÉGORIES DE TEST
        print("\n📂 AJOUT DES CATÉGORIES DE TEST")
        print("-" * 40)
        
        categories_test = [
            {
                'nom': 'Électronique',
                'description': 'Appareils électroniques et high-tech'
            },
            {
                'nom': 'Informatique',
                'description': 'Ordinateurs, tablettes et accessoires informatiques'
            },
            {
                'nom': 'Audio & Vidéo',
                'description': 'Équipements audio, vidéo et multimédia'
            },
            {
                'nom': 'Gaming',
                'description': 'Consoles de jeux et accessoires gaming'
            },
            {
                'nom': 'Électroménager',
                'description': 'Appareils électroménagers pour la maison'
            },
            {
                'nom': 'Mode & Textile',
                'description': 'Vêtements et accessoires de mode'
            },
            {
                'nom': 'Maison & Jardin',
                'description': 'Articles pour la maison et le jardin'
            }
        ]
        
        categories_ids = []
        for i, category in enumerate(categories_test, 1):
            try:
                from database.db_manager import add_category
                category_id = add_category(db_manager, category)
                categories_ids.append(category_id)
                print(f"✅ Catégorie {i}: {category['nom']} (ID: {category_id})")
            except Exception as e:
                print(f"⚠️ Catégorie {i} déjà existante ou erreur: {e}")
                # Essayer de récupérer l'ID existant
                try:
                    existing = db_manager.execute_query("SELECT id FROM categories WHERE nom = ?", (category['nom'],))
                    if existing:
                        categories_ids.append(existing[0]['id'])
                except:
                    categories_ids.append(i)  # ID par défaut
        
        print(f"📊 Total catégories: {len(categories_ids)}")
        
        # 3. PRODUITS DE TEST
        print("\n📦 AJOUT DES PRODUITS DE TEST")
        print("-" * 40)
        
        produits_test = [
            {
                'nom': 'iPhone 15 Pro Max',
                'description': 'Smartphone Apple dernière génération, 256GB, Titane Naturel',
                'reference': 'APPLE-IP15PM-256',
                'categorie_id': categories_ids[0] if categories_ids else None,  # Électronique
                'prix_achat': 8500.0,
                'prix_vente': 12000.0,
                'stock_actuel': 15,
                'stock_minimum': 3,
                'tva': 20.0,
                'actif': True
            },
            {
                'nom': 'Samsung Galaxy S24 Ultra',
                'description': 'Smartphone Samsung haut de gamme, 512GB, Noir Titanium',
                'reference': 'SAMSUNG-S24U-512',
                'categorie_id': categories_ids[0] if categories_ids else None,  # Électronique
                'prix_achat': 7200.0,
                'prix_vente': 10500.0,
                'stock_actuel': 12,
                'stock_minimum': 2,
                'tva': 20.0,
                'actif': True
            },
            {
                'nom': 'MacBook Air M3',
                'description': 'Ordinateur portable Apple, 13 pouces, 16GB RAM, 512GB SSD',
                'reference': 'APPLE-MBA-M3-512',
                'categorie_id': categories_ids[1] if len(categories_ids) > 1 else None,  # Informatique
                'prix_achat': 9800.0,
                'prix_vente': 13500.0,
                'stock_actuel': 8,
                'stock_minimum': 2,
                'tva': 20.0,
                'actif': True
            },
            {
                'nom': 'AirPods Pro 2',
                'description': 'Écouteurs sans fil Apple avec réduction de bruit active',
                'reference': 'APPLE-APP2',
                'categorie_id': categories_ids[2] if len(categories_ids) > 2 else None,  # Audio & Vidéo
                'prix_achat': 1800.0,
                'prix_vente': 2500.0,
                'stock_actuel': 25,
                'stock_minimum': 5,
                'tva': 20.0,
                'actif': True
            },
            {
                'nom': 'Sony WH-1000XM5',
                'description': 'Casque audio sans fil avec réduction de bruit, Noir',
                'reference': 'SONY-WH1000XM5',
                'categorie_id': categories_ids[2] if len(categories_ids) > 2 else None,  # Audio & Vidéo
                'prix_achat': 2200.0,
                'prix_vente': 3200.0,
                'stock_actuel': 18,
                'stock_minimum': 3,
                'tva': 20.0,
                'actif': True
            },
            {
                'nom': 'iPad Pro 12.9" M4',
                'description': 'Tablette Apple, 12.9 pouces, 256GB, Wi-Fi + Cellular',
                'reference': 'APPLE-IPADPRO-M4-256',
                'categorie_id': categories_ids[1] if len(categories_ids) > 1 else None,  # Informatique
                'prix_achat': 7500.0,
                'prix_vente': 10800.0,
                'stock_actuel': 10,
                'stock_minimum': 2,
                'tva': 20.0,
                'actif': True
            },
            {
                'nom': 'Dell XPS 13',
                'description': 'Ordinateur portable Dell, 13 pouces, Intel i7, 16GB RAM, 1TB SSD',
                'reference': 'DELL-XPS13-I7-1TB',
                'categorie_id': categories_ids[1] if len(categories_ids) > 1 else None,  # Informatique
                'prix_achat': 8200.0,
                'prix_vente': 11500.0,
                'stock_actuel': 6,
                'stock_minimum': 1,
                'tva': 20.0,
                'actif': True
            },
            {
                'nom': 'Apple Watch Series 9',
                'description': 'Montre connectée Apple, 45mm, GPS + Cellular, Aluminium',
                'reference': 'APPLE-AW9-45-CELL',
                'categorie_id': categories_ids[0] if categories_ids else None,  # Électronique
                'prix_achat': 3200.0,
                'prix_vente': 4500.0,
                'stock_actuel': 20,
                'stock_minimum': 4,
                'tva': 20.0,
                'actif': True
            },
            {
                'nom': 'Nintendo Switch OLED',
                'description': 'Console de jeu portable Nintendo, écran OLED, Blanc',
                'reference': 'NINTENDO-SWITCH-OLED',
                'categorie_id': categories_ids[3] if len(categories_ids) > 3 else None,  # Gaming
                'prix_achat': 2400.0,
                'prix_vente': 3200.0,
                'stock_actuel': 14,
                'stock_minimum': 3,
                'tva': 20.0,
                'actif': True
            },
            {
                'nom': 'Samsung 4K Smart TV 55"',
                'description': 'Téléviseur Samsung QLED 4K, 55 pouces, Smart TV, HDR10+',
                'reference': 'SAMSUNG-TV55-QLED4K',
                'categorie_id': categories_ids[2] if len(categories_ids) > 2 else None,  # Audio & Vidéo
                'prix_achat': 4500.0,
                'prix_vente': 6800.0,
                'stock_actuel': 5,
                'stock_minimum': 1,
                'tva': 20.0,
                'actif': True
            },
            {
                'nom': 'Dyson V15 Detect',
                'description': 'Aspirateur sans fil Dyson avec détection laser de poussière',
                'reference': 'DYSON-V15-DETECT',
                'categorie_id': categories_ids[4] if len(categories_ids) > 4 else None,  # Électroménager
                'prix_achat': 3800.0,
                'prix_vente': 5200.0,
                'stock_actuel': 8,
                'stock_minimum': 2,
                'tva': 20.0,
                'actif': True
            },
            {
                'nom': 'Nespresso Vertuo Next',
                'description': 'Machine à café Nespresso, système Vertuo, Gris Anthracite',
                'reference': 'NESPRESSO-VERTUO-NEXT',
                'categorie_id': categories_ids[4] if len(categories_ids) > 4 else None,  # Électroménager
                'prix_achat': 1200.0,
                'prix_vente': 1800.0,
                'stock_actuel': 12,
                'stock_minimum': 3,
                'tva': 20.0,
                'actif': True
            }
        ]
        
        produits_ids = []
        for i, produit in enumerate(produits_test, 1):
            try:
                produit_id = add_product(db_manager, produit)
                produits_ids.append(produit_id)
                print(f"✅ Produit {i}: {produit['nom']} - {produit['prix_vente']:.0f} DH (ID: {produit_id})")
            except Exception as e:
                print(f"⚠️ Produit {i} déjà existant ou erreur: {e}")
                # Essayer de récupérer l'ID existant
                try:
                    existing = db_manager.execute_query("SELECT id FROM produits WHERE reference = ?", (produit['reference'],))
                    if existing:
                        produits_ids.append(existing[0]['id'])
                except:
                    produits_ids.append(i)  # ID par défaut
        
        print(f"📊 Total produits: {len(produits_ids)}")
        
        # 3. VENTES DE TEST
        print("\n🛒 AJOUT DES VENTES DE TEST")
        print("-" * 40)
        
        modes_paiement = ['Espèces', 'Carte bancaire', 'Chèque', 'Virement bancaire', 'Électronique']
        statuts = ['En cours', 'Payée', 'Annulée']
        
        # Récupérer les produits avec leurs prix
        produits_data = db_manager.execute_query("SELECT id, nom, prix_vente, tva FROM produits WHERE actif = 1")
        
        ventes_creees = 0
        for i in range(20):  # Créer 20 ventes de test
            try:
                # Sélectionner un client aléatoire
                client_id = random.choice(clients_ids)
                
                # Sélectionner un mode de paiement aléatoire
                mode_paiement = random.choice(modes_paiement)
                
                # Date aléatoire dans les 30 derniers jours
                date_base = datetime.now() - timedelta(days=random.randint(0, 30))
                
                # Statut aléatoire (plus de chances d'être payée)
                statut = random.choices(statuts, weights=[20, 70, 10])[0]  # 70% payée, 20% en cours, 10% annulée
                
                vente_data = {
                    'client_id': client_id,
                    'mode_paiement': mode_paiement,
                    'notes': f'Vente de test #{i+1} - {date_base.strftime("%d/%m/%Y")}',
                    'statut': statut
                }
                
                # Sélectionner 1 à 4 produits aléatoires
                nb_produits = random.randint(1, 4)
                produits_selectionnes = random.sample(produits_data, min(nb_produits, len(produits_data)))
                
                sale_items = []
                for produit in produits_selectionnes:
                    quantite = random.randint(1, 3)
                    sale_items.append({
                        'product': {
                            'id': produit['id'],
                            'tva': produit['tva']
                        },
                        'quantite': quantite,
                        'prix_unitaire': produit['prix_vente']
                    })
                
                # Créer la vente
                vente_id = create_sale(db_manager, vente_data, sale_items)
                
                if vente_id:
                    ventes_creees += 1
                    # Calculer le total pour affichage
                    total = sum(item['quantite'] * item['prix_unitaire'] * 1.2 for item in sale_items)  # Avec TVA
                    print(f"✅ Vente {i+1}: {total:.0f} DH - {mode_paiement} - {statut} (ID: {vente_id})")
                else:
                    print(f"❌ Échec création vente {i+1}")
                    
            except Exception as e:
                print(f"❌ Erreur vente {i+1}: {e}")
        
        print(f"📊 Total ventes créées: {ventes_creees}")
        
        # 4. RÉSUMÉ FINAL
        print("\n📊 RÉSUMÉ DES DONNÉES DE TEST")
        print("=" * 60)
        
        # Statistiques clients
        clients_count = db_manager.execute_query("SELECT COUNT(*) as count FROM clients WHERE actif = 1")
        print(f"👥 Clients actifs: {clients_count[0]['count'] if clients_count else 0}")
        
        # Statistiques produits
        produits_count = db_manager.execute_query("SELECT COUNT(*) as count FROM produits WHERE actif = 1")
        print(f"📦 Produits actifs: {produits_count[0]['count'] if produits_count else 0}")
        
        # Statistiques ventes
        ventes_stats = db_manager.execute_query("""
            SELECT 
                COUNT(*) as total_ventes,
                SUM(CASE WHEN statut = 'En cours' THEN 1 ELSE 0 END) as en_cours,
                SUM(CASE WHEN statut = 'Payée' THEN 1 ELSE 0 END) as payees,
                SUM(CASE WHEN statut = 'Annulée' THEN 1 ELSE 0 END) as annulees,
                SUM(montant_ttc) as ca_total
            FROM ventes
        """)
        
        if ventes_stats:
            stats = ventes_stats[0]
            print(f"🛒 Total ventes: {stats['total_ventes']}")
            print(f"   • En cours: {stats['en_cours']}")
            print(f"   • Payées: {stats['payees']}")
            print(f"   • Annulées: {stats['annulees']}")
            print(f"💰 Chiffre d'affaires total: {stats['ca_total']:.2f} DH")
        
        print("\n🎉 DONNÉES DE TEST AJOUTÉES AVEC SUCCÈS !")
        print("=" * 60)
        print("✅ Vous pouvez maintenant tester toutes les fonctionnalités :")
        print("   • Gestion des clients")
        print("   • Gestion des produits") 
        print("   • Création de ventes")
        print("   • Modification des statuts")
        print("   • Génération de rapports")
        print("\n🚀 Lancez l'application avec: python launch_simple.py")
        
    except Exception as e:
        print(f"❌ Erreur lors de l'ajout des données: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if db_manager.connection:
            db_manager.disconnect()
            print("🔒 Connexion fermée proprement")

if __name__ == "__main__":
    ajouter_donnees_test()