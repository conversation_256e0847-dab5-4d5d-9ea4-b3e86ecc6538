#!/usr/bin/env python3
"""
Test complet de la liaison catégories ↔ produits avec interface
"""

import os
import sys
import time

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager, get_all_categories, add_product, add_category

def test_liaison_complete():
    """Test complet de la liaison avec simulation d'interface"""
    print("🔗 TEST COMPLET DE LIAISON CATÉGORIES ↔ PRODUITS")
    print("=" * 60)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    
    if not db_manager.connect():
        print("❌ Impossible de se connecter à la base de données")
        return
    
    if not db_manager.initialize_database():
        print("❌ Impossible d'initialiser la base de données")
        return
    
    print("✅ Base de données initialisée")
    
    try:
        # ==================== 1. CRÉER DES CATÉGORIES DE TEST ====================
        print("\n📂 1. CRÉATION DE CATÉGORIES DE TEST")
        print("-" * 40)
        
        categories_test = [
            {
                'nom': f'Test Interface {int(time.time())}',
                'description': 'Catégorie pour tester l\'interface'
            },
            {
                'nom': f'Test Liaison {int(time.time())}',
                'description': 'Catégorie pour tester la liaison'
            }
        ]
        
        created_categories = []
        for i, cat_data in enumerate(categories_test, 1):
            try:
                category_id = add_category(db_manager, cat_data)
                created_categories.append({'id': category_id, **cat_data})
                print(f"✅ Catégorie {i} créée: {cat_data['nom']} (ID: {category_id})")
            except Exception as e:
                print(f"⚠️ Catégorie {i} existe déjà ou erreur: {e}")
        
        # ==================== 2. CRÉER DES PRODUITS AVEC LIAISONS ====================
        print("\n📦 2. CRÉATION DE PRODUITS AVEC LIAISONS")
        print("-" * 40)
        
        # Récupérer toutes les catégories disponibles
        all_categories = get_all_categories(db_manager)
        print(f"📂 {len(all_categories)} catégories disponibles")
        
        # Créer des produits avec différentes liaisons
        produits_test = [
            {
                'nom': f'Produit avec catégorie {int(time.time())}',
                'description': 'Produit lié à une catégorie',
                'reference': f'AVEC-CAT-{int(time.time())}',
                'categorie_id': all_categories[0]['id'] if all_categories else None,
                'prix_achat': 50.0,
                'prix_vente': 75.0,
                'stock_actuel': 20,
                'stock_minimum': 5,
                'tva': 20.0,
                'actif': True
            },
            {
                'nom': f'Produit sans catégorie {int(time.time())}',
                'description': 'Produit sans catégorie spécifique',
                'reference': f'SANS-CAT-{int(time.time())}',
                'categorie_id': None,  # Pas de catégorie
                'prix_achat': 30.0,
                'prix_vente': 45.0,
                'stock_actuel': 15,
                'stock_minimum': 3,
                'tva': 20.0,
                'actif': True
            }
        ]
        
        if len(all_categories) > 1:
            produits_test.append({
                'nom': f'Produit autre catégorie {int(time.time())}',
                'description': 'Produit avec une autre catégorie',
                'reference': f'AUTRE-CAT-{int(time.time())}',
                'categorie_id': all_categories[1]['id'],
                'prix_achat': 80.0,
                'prix_vente': 120.0,
                'stock_actuel': 8,
                'stock_minimum': 2,
                'tva': 20.0,
                'actif': True
            })
        
        created_products = []
        for i, prod_data in enumerate(produits_test, 1):
            try:
                product_id = add_product(db_manager, prod_data)
                created_products.append({'id': product_id, **prod_data})
                
                cat_name = "Aucune"
                if prod_data.get('categorie_id'):
                    for cat in all_categories:
                        if cat['id'] == prod_data['categorie_id']:
                            cat_name = cat['nom']
                            break
                
                print(f"✅ Produit {i} créé: {prod_data['nom']}")
                print(f"   📂 Catégorie: {cat_name}")
                print(f"   🔗 ID Catégorie: {prod_data.get('categorie_id', 'NULL')}")
                
            except Exception as e:
                print(f"❌ Erreur produit {i}: {e}")
        
        # ==================== 3. SIMULATION DE L'INTERFACE DROPDOWN ====================
        print("\n🎨 3. SIMULATION DE L'INTERFACE DROPDOWN")
        print("-" * 40)
        
        print("✅ Simulation du chargement des catégories pour dropdown:")
        
        # Simuler le chargement des catégories
        categories_for_dropdown = get_all_categories(db_manager)
        print(f"   📂 {len(categories_for_dropdown)} catégories chargées")
        
        # Simuler la création du menu déroulant
        menu_items = [
            {"text": "📭 Aucune catégorie", "id": None}
        ]
        
        for cat in sorted(categories_for_dropdown, key=lambda x: x['nom']):
            menu_items.append({
                "text": f"📂 {cat['nom']}",
                "id": cat['id']
            })
        
        print(f"   🎯 {len(menu_items)} éléments de menu créés")
        print("   📋 Options disponibles dans le dropdown:")
        for i, item in enumerate(menu_items, 1):
            print(f"      {i:2d}. {item['text']} (ID: {item['id']})")
        
        # ==================== 4. SIMULATION DE L'AFFICHAGE DES PRODUITS ====================
        print("\n📦 4. SIMULATION DE L'AFFICHAGE DES PRODUITS")
        print("-" * 40)
        
        # Simuler la requête avec jointure pour l'affichage
        products_with_categories = db_manager.execute_query("""
            SELECT p.*, c.nom as categorie_nom, c.description as categorie_description
            FROM produits p
            LEFT JOIN categories c ON p.categorie_id = c.id
            WHERE p.actif = 1
            ORDER BY c.nom, p.nom
        """)
        
        print(f"✅ {len(products_with_categories)} produits chargés avec informations de catégorie")
        
        # Grouper par catégorie pour l'affichage
        categories_groups = {}
        for product in products_with_categories:
            cat_name = product.get('categorie_nom') or 'Sans catégorie'
            if cat_name not in categories_groups:
                categories_groups[cat_name] = []
            categories_groups[cat_name].append(product)
        
        print("\n📋 Affichage groupé par catégorie (simulation ProductCard):")
        for cat_name, products in categories_groups.items():
            print(f"\n📂 {cat_name} ({len(products)} produits):")
            for product in products[:3]:  # Afficher max 3 par catégorie
                print(f"   📦 {product['nom']}")
                print(f"      💰 {product['prix_vente']:.0f} DH")
                print(f"      📊 Stock: {product['stock_actuel']}")
                if product.get('categorie_nom'):
                    print(f"      📂 Catégorie: {product['categorie_nom']}")
                else:
                    print(f"      📭 Aucune catégorie")
                print()
            
            if len(products) > 3:
                print(f"   ... et {len(products) - 3} autres produits")
        
        # ==================== 5. SIMULATION DU FILTRE PAR CATÉGORIE ====================
        print("\n🔍 5. SIMULATION DU FILTRE PAR CATÉGORIE")
        print("-" * 40)
        
        # Compter les produits par catégorie pour le filtre
        filter_options = []
        
        # Option "Tous les produits"
        total_products = len(products_with_categories)
        filter_options.append({
            'id': 'all',
            'nom': 'Tous les produits',
            'count': total_products
        })
        
        # Option "Sans catégorie"
        no_category_count = len([p for p in products_with_categories if not p.get('categorie_id')])
        if no_category_count > 0:
            filter_options.append({
                'id': 'none',
                'nom': 'Sans catégorie',
                'count': no_category_count
            })
        
        # Options par catégorie
        for cat_name, products in categories_groups.items():
            if cat_name != 'Sans catégorie':
                # Trouver l'ID de la catégorie
                cat_id = None
                for product in products:
                    if product.get('categorie_id'):
                        cat_id = product['categorie_id']
                        break
                
                filter_options.append({
                    'id': cat_id,
                    'nom': cat_name,
                    'count': len(products)
                })
        
        print("✅ Options de filtre par catégorie:")
        for i, option in enumerate(filter_options, 1):
            icon = "📋" if option['id'] == 'all' else "📭" if option['id'] == 'none' else "📂"
            print(f"   {i:2d}. {icon} {option['nom']} ({option['count']} produits)")
        
        # ==================== 6. SIMULATION DE LA RECHERCHE ÉTENDUE ====================
        print("\n🔍 6. SIMULATION DE LA RECHERCHE ÉTENDUE")
        print("-" * 40)
        
        # Tester la recherche incluant les catégories
        search_terms = ['électronique', 'test', 'sans']
        
        for term in search_terms:
            matching_products = [
                p for p in products_with_categories
                if (term.lower() in p.get('nom', '').lower() or
                    term.lower() in p.get('reference', '').lower() or
                    term.lower() in (p.get('categorie_nom') or '').lower())
            ]
            
            print(f"🔍 Recherche '{term}': {len(matching_products)} produits trouvés")
            for product in matching_products[:2]:  # Afficher max 2 résultats
                cat_display = product.get('categorie_nom', 'Aucune')
                print(f"   📦 {product['nom']} (📂 {cat_display})")
        
        # ==================== 7. STATISTIQUES FINALES ====================
        print("\n📊 7. STATISTIQUES FINALES DE LIAISON")
        print("-" * 40)
        
        # Statistiques globales
        total_categories = len(all_categories)
        total_products = len(products_with_categories)
        products_with_cat = len([p for p in products_with_categories if p.get('categorie_id')])
        products_without_cat = total_products - products_with_cat
        
        print(f"📊 Résumé de la liaison:")
        print(f"   📂 Total catégories      : {total_categories}")
        print(f"   📦 Total produits        : {total_products}")
        print(f"   🔗 Produits avec catégorie : {products_with_cat}")
        print(f"   📭 Produits sans catégorie : {products_without_cat}")
        
        # Pourcentage de liaison
        if total_products > 0:
            percentage = (products_with_cat / total_products) * 100
            print(f"   📈 Taux de liaison       : {percentage:.1f}%")
        
        # Top catégories
        category_counts = {}
        for product in products_with_categories:
            cat_name = product.get('categorie_nom', 'Sans catégorie')
            category_counts[cat_name] = category_counts.get(cat_name, 0) + 1
        
        print(f"\n🏆 Top catégories par nombre de produits:")
        sorted_cats = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)
        for i, (cat_name, count) in enumerate(sorted_cats[:5], 1):
            icon = "📂" if cat_name != 'Sans catégorie' else "📭"
            cat_display = cat_name or 'Sans catégorie'
            print(f"   {i}. {icon} {cat_display:<25} : {count:2d} produits")
        
        print("\n🎉 TEST COMPLET DE LIAISON TERMINÉ AVEC SUCCÈS !")
        print("=" * 60)
        print("✅ Fonctionnalités de liaison validées :")
        print("   • 🔗 CRÉATION de produits avec catégories")
        print("   • 📂 DROPDOWN avec toutes les catégories")
        print("   • 📦 AFFICHAGE avec informations de catégorie")
        print("   • 🔍 FILTRE par catégorie")
        print("   • 🔍 RECHERCHE étendue (inclut catégories)")
        print("   • 📊 STATISTIQUES de liaison")
        print("   • 🎨 INTERFACE utilisateur simulée")
        
    except Exception as e:
        print(f"❌ Erreur lors du test complet: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if db_manager.connection:
            db_manager.disconnect()
            print("🔒 Connexion fermée proprement")

if __name__ == "__main__":
    test_liaison_complete()