#!/usr/bin/env python3
"""
Test du nouveau formulaire de vente
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from forms.sales_form import SalesFormDialog


class TestNewSalesFormApp(MDApp):
    """Test du nouveau formulaire de vente"""
    
    def build(self):
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        title = MDLabel(
            text="🧪 Test Nouveau\nFormulaire de Vente",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Test formulaire création
        create_btn = MDRaisedButton(
            text="🛒 Nouveau Formulaire Création",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_create_form
        )
        
        # Test formulaire modification
        edit_btn = MDRaisedButton(
            text="✏️ Nouveau Formulaire Modification",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_edit_form
        )
        
        self.result_label = MDLabel(
            text="Cliquez pour tester le nouveau formulaire de vente",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(create_btn)
        layout.add_widget(edit_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_create_form(self, *args):
        """Tester le formulaire de création"""
        try:
            dialog = SalesFormDialog(
                on_save_callback=self.on_save
            )
            dialog.open()
            
            self.result_label.text = "✅ Nouveau formulaire de création ouvert !\n\n" \
                                   "Vérifiez que vous voyez :\n" \
                                   "• Titre du formulaire\n" \
                                   "• Section client (liste déroulante)\n" \
                                   "• Section montants (HT et TTC)\n" \
                                   "• Section mode de paiement\n" \
                                   "• Section notes\n" \
                                   "• Boutons Annuler et Enregistrer"
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur nouveau formulaire création :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def test_edit_form(self, *args):
        """Tester le formulaire de modification"""
        try:
            # Données de test
            test_sale = {
                'id': 1,
                'numero_facture': 'FAC-TEST-001',
                'client_id': 1,
                'montant_ht': 100.00,
                'montant_ttc': 120.00,
                'mode_paiement': 'Espèces',
                'notes': 'Vente de test',
                'date_vente': '2024-01-01 10:00:00'
            }
            
            dialog = SalesFormDialog(
                sale_data=test_sale,
                on_save_callback=self.on_save
            )
            dialog.open()
            
            self.result_label.text = "✅ Nouveau formulaire de modification ouvert !\n\n" \
                                   "Vérifiez que vous voyez :\n" \
                                   "• Titre 'Modifier la vente'\n" \
                                   "• Numéro de facture affiché\n" \
                                   "• Champs pré-remplis\n" \
                                   "• Section informations\n" \
                                   "• Boutons Annuler et Enregistrer"
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur nouveau formulaire modification :\n{str(e)}"
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def on_save(self, data):
        """Callback de test"""
        self.result_label.text = f"🎉 Test de sauvegarde réussi !\n\n" \
                               f"Le nouveau formulaire fonctionne parfaitement !"


def main():
    print("🧪 Test Nouveau Formulaire de Vente")
    print("Objectif: Vérifier le nouveau formulaire simplifié")
    
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestNewSalesFormApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()